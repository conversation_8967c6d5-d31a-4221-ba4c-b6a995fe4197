<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\src\main\res"><file name="small_icon" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\drawable\small_icon.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-hdpi\ic_launcher_background.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-hdpi\ic_launcher_monochrome.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-mdpi\ic_launcher_background.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-mdpi\ic_launcher_monochrome.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xhdpi\ic_launcher_background.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xhdpi\ic_launcher_monochrome.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxhdpi\ic_launcher_background.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxhdpi\ic_launcher_monochrome.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxxhdpi\ic_launcher_background.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\mipmap-xxxhdpi\ic_launcher_monochrome.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">RikkaHub</string><string name="chat_input_placeholder">Type a message to chat with AI</string><string name="editing">Editing</string><string name="cancel_edit">Cancel Edit</string><string name="stop">Stop</string><string name="send">Send</string><string name="more_options">More options</string><string name="use_web_search">Use Web Search</string><string name="photo">Photo</string><string name="take_picture">Take Picture</string><string name="copy">Copy</string><string name="regenerate">Regenerate</string><string name="edit">Edit</string><string name="tts">TTS</string><string name="deep_thinking">Deep Thinking</string><string name="citations_count">%1$d Citations</string><string name="back">Back</string><string name="settings">Settings</string><string name="chat_page_no_conversations">No conversations</string><string name="chat_page_new_message">New message</string><string name="chat_page_today">Today</string><string name="chat_page_yesterday">Yesterday</string><string name="chat_page_date_format_same_year">%1$d/%2$d</string><string name="chat_page_date_format_different_year">%1$d/%2$d/%3$d</string><string name="chat_page_regenerate_title">Regenerate title</string><string name="chat_page_delete">Delete</string><string name="chat_page_history">Chat History</string><string name="chat_page_scroll_to_bottom">Scroll to bottom</string><string name="chat_page_edit_title">Edit title</string><string name="chat_page_save">Save</string><string name="chat_page_cancel">Cancel</string><string name="chat_page_new_chat">New Chat</string><string name="chat_page_edit_title_warning">Please chat first before editing the title</string><string name="chat_page_search_placeholder">Search conversations</string><string name="chat_page_clear_context">Clear Context</string><string name="chat_page_export_format">Export Format</string><string name="chat_page_export_markdown">Markdown</string><string name="chat_page_export_markdown_desc">Export conversation as a markdown file</string><string name="chat_page_export_success">Exported to %1$s</string><string name="chat_page_export_share_via">Share via</string><string name="setting_page_display_setting">Display Settings</string><string name="setting_page_display_setting_desc">Manage display settings</string><string name="setting_page_dynamic_color">Dynamic Color</string><string name="setting_page_dynamic_color_desc">Use dynamic color</string><string name="setting_page_general_settings">General Settings</string><string name="setting_page_model_and_services">Models &amp; Services</string><string name="setting_page_default_model">Default Model</string><string name="setting_page_default_model_desc">Set default models for each feature</string><string name="setting_page_providers">Providers</string><string name="setting_page_providers_desc">Configure AI providers</string><string name="setting_page_search_service">Search Service</string><string name="setting_page_search_service_desc">Set up search service</string><string name="setting_page_assistant">Assistant</string><string name="setting_page_assistant_desc">Set up personalized assistants (agents)</string><string name="setting_page_about">About</string><string name="setting_page_about_desc">About this APP</string><string name="setting_page_chat_storage">Chat Storage</string><string name="setting_page_chat_storage_desc">%1$d files, %2$.2f MB</string><string name="calculating">Calculating...</string><string name="setting_page_share">Share</string><string name="setting_page_share_desc">Share this APP with friends</string><string name="setting_page_share_text">RikkaHub - Open Source Android AI Assistant\n\nWebsite: https://rikka-ai.com/</string><string name="setting_page_no_share_app">No sharing app found</string><string name="setting_page_config_api_title">Please configure API and model</string><string name="setting_page_config_api_desc">You haven\'t configured API and model yet, please configure first</string><string name="setting_page_config">Configure</string><string name="setting_model_page_title">Model Settings</string><string name="setting_model_page_chat_model">Chat Model</string><string name="setting_model_page_title_model">Title Summary Model</string><string name="setting_model_page_translate_model">Translation Model</string><string name="translator_page_title">Translator</string><string name="translator_page_input_text">Input Text</string><string name="translator_page_input_placeholder">Enter text to translate...</string><string name="translator_page_result">Translation Result</string><string name="translator_page_result_placeholder">Translation result will be shown here</string><string name="translator_page_target_language">Target Language:</string><string name="translator_page_translate">Translate</string><string name="translator_page_cancel">Cancel</string><string name="assistant_page_title">Assistant Settings</string><string name="assistant_page_add">Add</string><string name="assistant_page_name">Assistant Name</string><string name="assistant_page_system_prompt">System Prompt</string><string name="assistant_page_available_variables">"Available variables: "</string><string name="assistant_page_temperature">Temperature</string><string name="assistant_page_strict">Strict</string><string name="assistant_page_balanced">Balanced</string><string name="assistant_page_creative">Creative</string><string name="assistant_page_chaotic">Chaotic (Dangerous)</string><string name="assistant_page_top_p">Top P</string><string name="assistant_page_top_p_warning">Please don\'t modify this value unless you know what you\'re doing</string><string name="assistant_page_context_message_size">Context Message Size</string><string name="assistant_page_context_message_desc">Controls how many historical messages will be sent to the model as context. Messages exceeding this number will be ignored, only the most recent N messages will be retained, which can save tokens</string><string name="assistant_page_context_message_count">Context Message Size: %d</string><string name="assistant_page_memory">Memory</string><string name="assistant_page_memory_desc">When memory is enabled, the model will try to actively record your information during conversations and use it in subsequent conversations. This feature requires the model to support tool calls to work properly</string><string name="assistant_page_manage_memory">Manage Memory (%d items)</string><string name="assistant_page_cancel">Cancel</string><string name="assistant_page_save">Save</string><string name="assistant_page_manage_memory_title">Manage Memory</string><string name="assistant_page_delete">Delete</string><string name="assistant_page_default_assistant">Default Assistant</string><string name="assistant_page_temperature_value">Temperature: %s</string><string name="assistant_page_memory_count">Memory: %d</string><string name="assistant_page_no_system_prompt">No system prompt</string><string name="assistant_page_top_p_value">Top P: %s</string><string name="assistant_page_inject_message_time">Inject Message Time</string><string name="assistant_page_inject_message_time_desc">Whether to inject the message sending time into the context to help the model understand when each message was sent. Note that enabling this will consume more tokens</string><string name="assistant_page_thinking_budget">Thinking Budget</string><string name="assistant_page_thinking_budget_desc">The number of thoughts tokens that the model should generate. 0 means thinking will be disabled, and blank means using the default value of the model.</string><string name="assistant_page_thinking_budget_tokens">%s tokens</string><string name="assistant_page_thinking_budget_default">Default</string><string name="assistant_page_thinking_budget_warning">Note: Different providers define various thinking budget API formats that the APP cannot accommodate for all, so this option may not work. If it doesn\'t work, we recommend using the custom body below to customize your request.</string><string name="assistant_page_custom_headers">Custom Headers</string><string name="assistant_page_header_name">Header Name</string><string name="assistant_page_header_value">Header Value</string><string name="assistant_page_delete_header">Delete Header</string><string name="assistant_page_add_header">Add Header</string><string name="assistant_page_custom_bodies">Custom Body</string><string name="assistant_page_body_key">Body Key</string><string name="assistant_page_body_value">Body Value (JSON)</string><string name="assistant_page_invalid_json">Invalid JSON: %s</string><string name="assistant_page_delete_body">Delete Body</string><string name="assistant_page_add_body">Add Body</string><string name="notification_channel_chat_completed">Chat Completed</string><string name="setting_page_theme_type_standard">Standard</string><string name="setting_page_theme_type_medium_contrast">Medium Contrast</string><string name="setting_page_theme_type_high_contrast">High Contrast</string><string name="theme_name_black">Neutral Black</string><string name="theme_name_sakura">Sakura Pink</string><string name="theme_name_ocean">Ocean Blue</string><string name="theme_name_spring">Meadow Green</string><string name="menu_page_morning_greeting">Good morning\uD83D\uDC4B</string><string name="menu_page_afternoon_greeting">Good afternoon\uD83D\uDC4B</string><string name="menu_page_evening_greeting">Good evening\uD83D\uDC4B</string><string name="menu_page_night_greeting">It\'s late, take care\uD83D\uDC4B</string><string name="menu_page_ai_translator">AI Translator</string><string name="menu_page_knowledge_base">Knowledge Base (WIP)</string><string name="menu_page_llm_leaderboard">LLM Leaderboard</string><string name="model_list_select_model">Select Model</string><string name="model_list_no_providers">No available AI providers, please add in settings</string><string name="model_list_favorite">Favorites</string><string name="model_list_chat">Chat</string><string name="model_list_embedding">Embedding</string><string name="code_block_copy">Copy Code</string><string name="code_block_preview">Preview</string><string name="mermaid_export">Export</string><string name="mermaid_export_success">Export successful</string><string name="mermaid_export_failed">Export failed</string><string name="setting_display_page_chat_list_model_icon_title">Chat List Model Icon</string><string name="setting_display_page_chat_list_model_icon_desc">Whether to display model icons in chat list messages</string><string name="setting_display_page_show_token_usage_title">Show Token Usage</string><string name="setting_display_page_show_token_usage_desc">Display token usage at the bottom of the conversation</string><string name="setting_display_page_auto_collapse_thinking_title">Auto Collapse Thinking</string><string name="setting_display_page_auto_collapse_thinking_desc">Automatically collapse thinking content after thinking is complete</string><string name="setting_display_page_show_updates_title">Show Updates</string><string name="setting_display_page_show_updates_desc">Whether to display app update notifications</string><string name="setting_display_page_title">Display Settings</string><string name="setting_page_color_mode">Color Mode</string><string name="setting_page_color_mode_system">System</string><string name="setting_page_color_mode_light">Light</string><string name="setting_page_color_mode_dark">Dark</string><string name="assistant_page_tab_basic">Basic Settings</string><string name="assistant_page_tab_prompt">Prompts</string><string name="assistant_page_tab_memory">Memory</string><string name="assistant_page_tab_request">Custom Request</string><string name="assistant_page_stream_output">Stream Output</string><string name="assistant_page_stream_output_desc">Whether to enable streaming output for messages</string><string name="chat_page_context_truncated">Context truncated - Only messages from this point forward will be included</string><string name="chat_page_ignore_context">Ignore Context</string><string name="chat_page_restore_context">Restore Context</string></file><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Rikkahub" parent="android:Theme.Material.Light.NoActionBar"/></file><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values-ja\strings.xml" qualifiers="ja"><string name="app_name">RikkaHub</string><string name="chat_input_placeholder">AIとチャットするメッセージを入力</string><string name="editing">編集中</string><string name="cancel_edit">編集をキャンセル</string><string name="stop">停止</string><string name="send">送信</string><string name="more_options">その他のオプション</string><string name="use_web_search">ウェブ検索を使用</string><string name="photo">写真</string><string name="take_picture">写真を撮る</string><string name="copy">コピー</string><string name="regenerate">再生成</string><string name="edit">編集</string><string name="tts">音声</string><string name="deep_thinking">深く考える</string><string name="citations_count">%1$d 件の引用</string><string name="back">戻る</string><string name="settings">設定</string><string name="chat_page_no_conversations">会話がありません</string><string name="chat_page_new_message">新しいメッセージ</string><string name="chat_page_today">今日</string><string name="chat_page_yesterday">昨日</string><string name="chat_page_date_format_same_year">%1$d月%2$d日</string><string name="chat_page_date_format_different_year">%1$d年%2$d月%3$d日</string><string name="chat_page_regenerate_title">タイトルを再生成</string><string name="chat_page_delete">削除</string><string name="chat_page_history">チャット履歴</string><string name="chat_page_scroll_to_bottom">一番下へスクロール</string><string name="chat_page_edit_title">タイトルを編集</string><string name="chat_page_save">保存</string><string name="chat_page_cancel">キャンセル</string><string name="chat_page_new_chat">新しいチャット</string><string name="chat_page_edit_title_warning">まずチャットしてからタイトルを編集してください</string><string name="chat_page_search_placeholder">会話を検索</string><string name="chat_page_clear_context">コンテキストをクリア</string><string name="chat_page_export_format">エクスポート形式</string><string name="chat_page_export_markdown">Markdown</string><string name="chat_page_export_markdown_desc">会話をmarkdownファイルとしてエクスポート</string><string name="chat_page_export_success">%1$sにエクスポートしました</string><string name="chat_page_export_share_via">共有方法</string><string name="setting_page_display_setting">表示設定</string><string name="setting_page_display_setting_desc">表示設定を管理</string><string name="setting_page_dynamic_color">ダイナミックカラー</string><string name="setting_page_dynamic_color_desc">ダイナミックカラーを使用するかどうか</string><string name="setting_page_general_settings">一般設定</string><string name="setting_page_model_and_services">モデルとサービス</string><string name="setting_page_default_model">デフォルトモデル</string><string name="setting_page_default_model_desc">各機能のデフォルトモデルを設定</string><string name="setting_page_providers">プロバイダー</string><string name="setting_page_providers_desc">AIプロバイダーを設定</string><string name="setting_page_search_service">検索サービス</string><string name="setting_page_search_service_desc">検索サービスを設定</string><string name="setting_page_assistant">アシスタント</string><string name="setting_page_assistant_desc">カスタムアシスタント（エージェント）を設定</string><string name="setting_page_about">アプリについて</string><string name="setting_page_about_desc">このアプリについて</string><string name="setting_page_chat_storage">チャット保存</string><string name="setting_page_chat_storage_desc">%1$d ファイル、%2$.2f MB</string><string name="calculating">計算中...</string><string name="setting_page_share">共有</string><string name="setting_page_share_desc">このアプリを友達と共有</string><string name="setting_page_share_text">RikkaHub - オープンソースAndroid AIアシスタント\n\nウェブサイト: https://rikka-ai.com/</string><string name="setting_page_no_share_app">共有アプリが見つかりません</string><string name="setting_page_config_api_title">APIとモデルを設定してください</string><string name="setting_page_config_api_desc">APIとモデルを設定してください</string><string name="setting_page_config">設定</string><string name="setting_model_page_title">モデル設定</string><string name="setting_model_page_chat_model">チャットモデル</string><string name="setting_model_page_title_model">タイトル要約モデル</string><string name="setting_model_page_translate_model">翻訳モデル</string><string name="translator_page_title">翻訳</string><string name="translator_page_input_text">入力テキスト</string><string name="translator_page_input_placeholder">翻訳するテキストを入力してください...</string><string name="translator_page_result">翻訳結果</string><string name="translator_page_result_placeholder">翻訳結果がここに表示されます</string><string name="translator_page_target_language">対象言語:</string><string name="translator_page_translate">翻訳</string><string name="translator_page_cancel">キャンセル</string><string name="assistant_page_title">アシスタント設定</string><string name="assistant_page_add">追加</string><string name="assistant_page_name">アシスタント名</string><string name="assistant_page_system_prompt">システムプロンプト</string><string name="assistant_page_available_variables">"利用可能な変数: "</string><string name="assistant_page_temperature">温度</string><string name="assistant_page_strict">厳密</string><string name="assistant_page_balanced">バランス</string><string name="assistant_page_creative">創造的</string><string name="assistant_page_chaotic">カオス（危険）</string><string name="assistant_page_top_p">Top P</string><string name="assistant_page_top_p_warning">何をしているのか理解している場合を除き、この値を変更しないでください</string><string name="assistant_page_context_message_size">コンテキストメッセージサイズ</string><string name="assistant_page_context_message_desc">モデルに送信される履歴メッセージの数を制御します。この数を超えるメッセージは無視され、最新のN個のメッセージのみが保持されます。これによりトークンを節約できます</string><string name="assistant_page_context_message_count">コンテキストメッセージサイズ: %d</string><string name="assistant_page_memory">記憶</string><string name="assistant_page_memory_desc">記憶を有効にすると、モデルは会話中にあなたの情報を積極的に記録し、後続の会話で使用しようとします。この機能が正常に動作するには、モデルがツール呼び出しをサポートしている必要があります</string><string name="assistant_page_manage_memory">記憶を管理 (%d項目)</string><string name="assistant_page_cancel">キャンセル</string><string name="assistant_page_save">保存</string><string name="assistant_page_manage_memory_title">記憶を管理</string><string name="assistant_page_delete">削除</string><string name="assistant_page_default_assistant">デフォルトアシスタント</string><string name="assistant_page_temperature_value">温度: %s</string><string name="assistant_page_memory_count">記憶: %d</string><string name="assistant_page_no_system_prompt">システムプロンプトなし</string><string name="assistant_page_top_p_value">Top P: %s</string><string name="assistant_page_inject_message_time">メッセージ時間を注入</string><string name="assistant_page_inject_message_time_desc">各メッセージの送信時間をコンテキストに注入して、モデルがメッセージがいつ送信されたかを理解できるようにするかどうか。これを有効にするとトークンの消費が増えることに注意してください</string><string name="assistant_page_thinking_budget">思考バジェット</string><string name="assistant_page_thinking_budget_desc">モデルが生成すべき思考トークンの数。0は思考が無効になることを意味し、空白はモデルのデフォルト値を使用することを意味します。</string><string name="assistant_page_thinking_budget_tokens">%s トークン</string><string name="assistant_page_thinking_budget_default">デフォルト</string><string name="assistant_page_thinking_budget_warning">注意：異なるプロバイダーは様々な思考バジェットAPIフォーマットを定義しており、アプリはすべてに対応できないため、このオプションが機能しない場合があります。機能しない場合は、下のカスタムボディを使用してリクエストをカスタマイズすることをお勧めします。</string><string name="assistant_page_custom_headers">カスタムヘッダー</string><string name="assistant_page_header_name">ヘッダー名</string><string name="assistant_page_header_value">ヘッダー値</string><string name="assistant_page_delete_header">ヘッダーを削除</string><string name="assistant_page_add_header">ヘッダーを追加</string><string name="assistant_page_custom_bodies">カスタムボディ</string><string name="assistant_page_body_key">ボディキー</string><string name="assistant_page_body_value">ボディ値 (JSON)</string><string name="assistant_page_invalid_json">無効なJSON: %s</string><string name="assistant_page_delete_body">ボディを削除</string><string name="assistant_page_add_body">ボディを追加</string><string name="notification_channel_chat_completed">チャット完了</string><string name="setting_page_theme_type_standard">標準</string><string name="setting_page_theme_type_medium_contrast">中コントラスト</string><string name="setting_page_theme_type_high_contrast">高コントラスト</string><string name="theme_name_black">ニュートラルブラック</string><string name="theme_name_sakura">サクラピンク</string><string name="theme_name_ocean">オーシャンブルー</string><string name="theme_name_spring">メドウグリーン</string><string name="menu_page_morning_greeting">おはようございます\uD83D\uDC4B</string><string name="menu_page_afternoon_greeting">こんにちは\uD83D\uDC4B</string><string name="menu_page_evening_greeting">こんばんは\uD83D\uDC4B</string><string name="menu_page_night_greeting">夜更かしですね、お気をつけて\uD83D\uDC4B</string><string name="menu_page_ai_translator">AI翻訳</string><string name="menu_page_knowledge_base">知識ベース (開発中)</string><string name="menu_page_llm_leaderboard">LLMランキング</string><string name="model_list_select_model">モデルを選択</string><string name="model_list_no_providers">利用可能なAIプロバイダーがありません。設定で追加してください</string><string name="model_list_favorite">お気に入り</string><string name="model_list_chat">チャット</string><string name="model_list_embedding">埋め込み</string><string name="code_block_copy">コードをコピー</string><string name="code_block_preview">プレビュー</string><string name="mermaid_export">エクスポート</string><string name="mermaid_export_success">エクスポートに成功しました</string><string name="mermaid_export_failed">エクスポートに失敗しました</string><string name="setting_display_page_title">表示設定</string><string name="setting_display_page_chat_list_model_icon_title">チャットリストのモデルアイコン</string><string name="setting_display_page_chat_list_model_icon_desc">チャットリストのメッセージにモデルアイコンを表示するかどうか</string><string name="setting_display_page_show_token_usage_title">トークン使用量を表示</string><string name="setting_display_page_show_token_usage_desc">会話の下部にトークン使用量を表示</string><string name="setting_display_page_auto_collapse_thinking_title">思考を自動折りたたむ</string><string name="setting_display_page_auto_collapse_thinking_desc">思考が完了したら思考内容を自動的に折りたたむ</string><string name="setting_display_page_show_updates_title">アップデートを表示</string><string name="setting_display_page_show_updates_desc">アプリのアップデート通知を表示するかどうか</string><string name="setting_page_color_mode">カラーモード</string><string name="setting_page_color_mode_system">システム設定に従う</string><string name="setting_page_color_mode_light">ライト</string><string name="setting_page_color_mode_dark">ダーク</string><string name="assistant_page_tab_basic">基本設定</string><string name="assistant_page_tab_prompt">プロンプト</string><string name="assistant_page_tab_memory">記憶</string><string name="assistant_page_tab_request">カスタムリクエスト</string><string name="assistant_page_stream_output">ストリーミング出力</string><string name="assistant_page_stream_output_desc">メッセージをストリーミング出力するかどうか</string></file><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values-zh\strings.xml" qualifiers="zh"><string name="app_name">RikkaHub</string><string name="chat_input_placeholder">输入消息与AI聊天</string><string name="editing">编辑中</string><string name="cancel_edit">取消编辑</string><string name="stop">停止</string><string name="send">发送</string><string name="more_options">更多选项</string><string name="use_web_search">使用网络搜索</string><string name="photo">照片</string><string name="take_picture">拍照</string><string name="copy">复制</string><string name="regenerate">重新生成</string><string name="edit">编辑</string><string name="tts">语音</string><string name="deep_thinking">深度思考</string><string name="citations_count">%1$d 条引用</string><string name="back">返回</string><string name="settings">设置</string><string name="chat_page_no_conversations">没有对话记录</string><string name="chat_page_new_message">新消息</string><string name="chat_page_today">今天</string><string name="chat_page_yesterday">昨天</string><string name="chat_page_date_format_same_year">%1$d月%2$d日</string><string name="chat_page_date_format_different_year">%1$d年%2$d月%3$d日</string><string name="chat_page_regenerate_title">重新生成标题</string><string name="chat_page_delete">删除</string><string name="chat_page_history">聊天历史</string><string name="chat_page_scroll_to_bottom">滚动到底部</string><string name="chat_page_edit_title">编辑标题</string><string name="chat_page_save">保存</string><string name="chat_page_cancel">取消</string><string name="chat_page_new_chat">新聊天</string><string name="chat_page_edit_title_warning">请先聊天再编辑标题吧</string><string name="chat_page_search_placeholder">搜索聊天记录</string><string name="chat_page_clear_context">清空上下文</string><string name="chat_page_export_format">导出格式</string><string name="chat_page_export_markdown">Markdown</string><string name="chat_page_export_markdown_desc">将对话导出为markdown文件</string><string name="chat_page_export_success">已导出为%1$s</string><string name="chat_page_export_share_via">分享方式</string><string name="setting_page_display_setting">显示设置</string><string name="setting_page_display_setting_desc">管理显示设置</string><string name="setting_page_dynamic_color">动态颜色</string><string name="setting_page_dynamic_color_desc">是否使用动态颜色</string><string name="setting_page_general_settings">通用设置</string><string name="setting_page_model_and_services">模型与服务</string><string name="setting_page_default_model">默认模型</string><string name="setting_page_default_model_desc">设置各个功能的默认模型</string><string name="setting_page_providers">提供商</string><string name="setting_page_providers_desc">配置AI提供商</string><string name="setting_page_search_service">搜索服务</string><string name="setting_page_search_service_desc">设置搜索服务</string><string name="setting_page_assistant">助手</string><string name="setting_page_assistant_desc">设置个性化助手 (智能体)</string><string name="setting_page_about">关于</string><string name="setting_page_about_desc">关于本APP</string><string name="setting_page_chat_storage">聊天记录存储</string><string name="setting_page_chat_storage_desc">%1$d 个文件，%2$.2f MB</string><string name="calculating">计算中...</string><string name="setting_page_share">分享</string><string name="setting_page_share_desc">分享本APP给朋友</string><string name="setting_page_share_text">RikkaHub - 开源安卓AI助手\n\n官网: https://rikka-ai.com/</string><string name="setting_page_no_share_app">找不到分享应用</string><string name="setting_page_config_api_title">请配置API和模型</string><string name="setting_page_config_api_desc">你还没有配置API和模型，请先配置</string><string name="setting_page_config">配置</string><string name="setting_model_page_title">模型设置</string><string name="setting_model_page_chat_model">聊天模型</string><string name="setting_model_page_title_model">标题总结模型</string><string name="setting_model_page_translate_model">翻译模型</string><string name="translator_page_title">翻译</string><string name="translator_page_input_text">输入文本</string><string name="translator_page_input_placeholder">请输入要翻译的文本...</string><string name="translator_page_result">翻译结果</string><string name="translator_page_result_placeholder">翻译结果将显示在这里</string><string name="translator_page_target_language">目标语言:</string><string name="translator_page_translate">翻译</string><string name="translator_page_cancel">取消</string><string name="assistant_page_title">助手设置</string><string name="assistant_page_add">添加</string><string name="assistant_page_name">助手名称</string><string name="assistant_page_system_prompt">系统提示词</string><string name="assistant_page_available_variables">"可用变量: "</string><string name="assistant_page_temperature">温度</string><string name="assistant_page_strict">严谨</string><string name="assistant_page_balanced">平衡</string><string name="assistant_page_creative">创造</string><string name="assistant_page_chaotic">混乱 (危险)</string><string name="assistant_page_top_p">Top P</string><string name="assistant_page_top_p_warning">请不要修改此值, 除非你知道自己在做什么</string><string name="assistant_page_context_message_size">上下文消息数量</string><string name="assistant_page_context_message_desc">控制多少条历史消息会被作为上下文发送给模型, 超过此数量的消息会被忽略，只有最近的N条消息会被保留，可以节省token</string><string name="assistant_page_context_message_count">上下文消息数量: %d</string><string name="assistant_page_memory">记忆</string><string name="assistant_page_memory_desc">启用记忆后，模型在与你对话时尝试主动记录你的信息，并在后续其他对话中使用，此功能需要模型支持工具调用才能正常工作</string><string name="assistant_page_manage_memory">管理记忆 (%d条)</string><string name="assistant_page_cancel">取消</string><string name="assistant_page_save">保存</string><string name="assistant_page_manage_memory_title">管理记忆</string><string name="assistant_page_delete">删除</string><string name="assistant_page_default_assistant">默认助手</string><string name="assistant_page_temperature_value">温度: %s</string><string name="assistant_page_memory_count">记忆: %d</string><string name="assistant_page_no_system_prompt">无系统提示词</string><string name="assistant_page_top_p_value">Top P: %s</string><string name="assistant_page_inject_message_time">注入消息时间</string><string name="assistant_page_inject_message_time_desc">是否把每条消息的发送时间注入到上下文中，以便模型理解消息发送时间，注意开启会消耗更多token</string><string name="assistant_page_thinking_budget">思考预算</string><string name="assistant_page_thinking_budget_desc">模型应该生成的思考内容的token数量。0表示将禁用思考，留空表示使用模型的默认值。</string><string name="assistant_page_thinking_budget_tokens">%s token</string><string name="assistant_page_thinking_budget_default">默认</string><string name="assistant_page_thinking_budget_warning">注意，由于不同提供商定义了各种不同的思考预算API格式，APP无法考虑到，因此这个选项不一定起作用，如果不起作用，推荐使用下面的自定义body来自定义请求</string><string name="assistant_page_custom_headers">自定义 Headers</string><string name="assistant_page_header_name">Header 名称</string><string name="assistant_page_header_value">Header 值</string><string name="assistant_page_delete_header">删除 Header</string><string name="assistant_page_add_header">添加 Header</string><string name="assistant_page_custom_bodies">自定义 Body</string><string name="assistant_page_body_key">Body Key</string><string name="assistant_page_body_value">Body 值 (JSON)</string><string name="assistant_page_invalid_json">无效的 JSON: %s</string><string name="assistant_page_delete_body">删除 Body</string><string name="assistant_page_add_body">添加 Body</string><string name="notification_channel_chat_completed">聊天完成</string><string name="setting_page_theme_type_standard">标准</string><string name="setting_page_theme_type_medium_contrast">中对比</string><string name="setting_page_theme_type_high_contrast">高对比</string><string name="theme_name_black">中性黑</string><string name="theme_name_sakura">樱花粉</string><string name="theme_name_ocean">海湾蓝</string><string name="theme_name_spring">原野绿</string><string name="menu_page_morning_greeting">早上好\uD83D\uDC4B</string><string name="menu_page_afternoon_greeting">下午好\uD83D\uDC4B</string><string name="menu_page_evening_greeting">晚上好\uD83D\uDC4B</string><string name="menu_page_night_greeting">夜深了，注意休息\uD83D\uDC4B</string><string name="menu_page_ai_translator">AI翻译</string><string name="menu_page_knowledge_base">知识库 (施工中)</string><string name="menu_page_llm_leaderboard">LLM排行榜</string><string name="model_list_select_model">选择模型</string><string name="model_list_no_providers">没有可用AI提供商, 请在设置添加</string><string name="model_list_favorite">收藏</string><string name="model_list_chat">聊天</string><string name="model_list_embedding">嵌入</string><string name="code_block_copy">复制代码</string><string name="code_block_preview">预览</string><string name="mermaid_export">导出</string><string name="mermaid_export_success">导出成功</string><string name="mermaid_export_failed">导出失败</string><string name="setting_display_page_chat_list_model_icon_title">聊天列表模型图标</string><string name="setting_display_page_chat_list_model_icon_desc">是否在聊天列表消息中显示模型图标</string><string name="setting_display_page_show_token_usage_title">显示Token消耗</string><string name="setting_display_page_show_token_usage_desc">在对话底部显示Token消耗</string><string name="setting_display_page_auto_collapse_thinking_title">自动折叠思考</string><string name="setting_display_page_auto_collapse_thinking_desc">思考完成自动折叠思考内容</string><string name="setting_display_page_show_updates_title">显示更新</string><string name="setting_display_page_show_updates_desc">是否显示应用更新通知</string><string name="setting_display_page_title">显示设置</string><string name="setting_page_color_mode">颜色模式</string><string name="setting_page_color_mode_system">跟随系统</string><string name="setting_page_color_mode_light">浅色</string><string name="setting_page_color_mode_dark">深色</string><string name="assistant_page_tab_basic">基础设定</string><string name="assistant_page_tab_prompt">提示词</string><string name="assistant_page_tab_memory">记忆</string><string name="assistant_page_tab_request">自定义请求</string><string name="assistant_page_stream_output">流式输出</string><string name="assistant_page_stream_output_desc">是否启用消息的流式输出</string></file><file path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\values-zh-rTW\strings.xml" qualifiers="zh-rTW"><string name="app_name">RikkaHub</string><string name="chat_input_placeholder">輸入訊息與AI聊天</string><string name="editing">編輯中</string><string name="cancel_edit">取消編輯</string><string name="stop">停止</string><string name="send">發送</string><string name="more_options">更多選項</string><string name="use_web_search">使用網路搜尋</string><string name="photo">照片</string><string name="take_picture">拍照</string><string name="copy">複製</string><string name="regenerate">重新生成</string><string name="edit">編輯</string><string name="tts">語音</string><string name="deep_thinking">深度思考</string><string name="citations_count">%1$d 條引用</string><string name="back">返回</string><string name="settings">設定</string><string name="chat_page_no_conversations">沒有對話記錄</string><string name="chat_page_new_message">新訊息</string><string name="chat_page_today">今天</string><string name="chat_page_yesterday">昨天</string><string name="chat_page_date_format_same_year">%1$d月%2$d日</string><string name="chat_page_date_format_different_year">%1$d年%2$d月%3$d日</string><string name="chat_page_regenerate_title">重新生成標題</string><string name="chat_page_delete">刪除</string><string name="chat_page_history">聊天歷史</string><string name="chat_page_scroll_to_bottom">滾動到底部</string><string name="chat_page_edit_title">編輯標題</string><string name="chat_page_save">保存</string><string name="chat_page_cancel">取消</string><string name="chat_page_new_chat">新聊天</string><string name="chat_page_edit_title_warning">請先聊天再編輯標題吧</string><string name="chat_page_search_placeholder">搜尋對話記錄</string><string name="chat_page_clear_context">清空上下文</string><string name="chat_page_export_format">匯出格式</string><string name="chat_page_export_markdown">Markdown</string><string name="chat_page_export_markdown_desc">將對話匯出為markdown檔案</string><string name="chat_page_export_success">已匯出為%1$s</string><string name="chat_page_export_share_via">分享方式</string><string name="setting_page_display_setting">顯示設定</string><string name="setting_page_display_setting_desc">管理顯示設定</string><string name="setting_page_dynamic_color">動態顏色</string><string name="setting_page_dynamic_color_desc">是否使用動態顏色</string><string name="setting_page_general_settings">通用設定</string><string name="setting_page_model_and_services">模型與服務</string><string name="setting_page_default_model">預設模型</string><string name="setting_page_default_model_desc">設定各個功能的預設模型</string><string name="setting_page_providers">提供商</string><string name="setting_page_providers_desc">配置AI提供商</string><string name="setting_page_search_service">搜尋服務</string><string name="setting_page_search_service_desc">設定搜尋服務</string><string name="setting_page_assistant">助手</string><string name="setting_page_assistant_desc">設定個性化助手 (智能體)</string><string name="setting_page_about">關於</string><string name="setting_page_about_desc">關於本APP</string><string name="setting_page_chat_storage">聊天記錄儲存</string><string name="setting_page_chat_storage_desc">%1$d 個檔案，%2$.2f MB</string><string name="calculating">計算中...</string><string name="setting_page_share">分享</string><string name="setting_page_share_desc">分享本APP給朋友</string><string name="setting_page_share_text">RikkaHub - 開源安卓AI助手\n\n官網: https://rikka-ai.com/</string><string name="setting_page_no_share_app">找不到分享應用</string><string name="setting_page_config_api_title">請配置API和模型</string><string name="setting_page_config_api_desc">你還沒有配置API和模型，請先配置</string><string name="setting_page_config">配置</string><string name="setting_model_page_title">模型設定</string><string name="setting_model_page_chat_model">聊天模型</string><string name="setting_model_page_title_model">標題摘要模型</string><string name="setting_model_page_translate_model">翻譯模型</string><string name="translator_page_title">翻譯</string><string name="translator_page_input_text">輸入文字</string><string name="translator_page_input_placeholder">請輸入要翻譯的文字...</string><string name="translator_page_result">翻譯結果</string><string name="translator_page_result_placeholder">翻譯結果將顯示在這裡</string><string name="translator_page_target_language">目標語言:</string><string name="translator_page_translate">翻譯</string><string name="translator_page_cancel">取消</string><string name="assistant_page_title">助手設定</string><string name="assistant_page_add">添加</string><string name="assistant_page_name">助手名稱</string><string name="assistant_page_system_prompt">系統提示詞</string><string name="assistant_page_available_variables">"可用變數: "</string><string name="assistant_page_temperature">溫度</string><string name="assistant_page_strict">嚴謹</string><string name="assistant_page_balanced">平衡</string><string name="assistant_page_creative">創造</string><string name="assistant_page_chaotic">混亂 (危險)</string><string name="assistant_page_top_p">Top P</string><string name="assistant_page_top_p_warning">請不要修改此值, 除非你知道自己在做什麼</string><string name="assistant_page_context_message_size">上下文訊息數量</string><string name="assistant_page_context_message_desc">控制多少條歷史訊息會被作為上下文發送給模型, 超過此數量的訊息會被忽略，只有最近的N條訊息會被保留，可以節省token</string><string name="assistant_page_context_message_count">上下文訊息數量: %d</string><string name="assistant_page_memory">記憶</string><string name="assistant_page_memory_desc">啟用記憶後，模型在與你對話時嘗試主動記錄你的資訊，並在後續其他對話中使用，此功能需要模型支援工具調用才能正常工作</string><string name="assistant_page_manage_memory">管理記憶 (%d條)</string><string name="assistant_page_cancel">取消</string><string name="assistant_page_save">保存</string><string name="assistant_page_manage_memory_title">管理記憶</string><string name="assistant_page_delete">刪除</string><string name="assistant_page_default_assistant">預設助手</string><string name="assistant_page_temperature_value">溫度: %s</string><string name="assistant_page_memory_count">記憶: %d</string><string name="assistant_page_no_system_prompt">無系統提示詞</string><string name="assistant_page_top_p_value">Top P: %s</string><string name="assistant_page_inject_message_time">注入訊息時間</string><string name="assistant_page_inject_message_time_desc">是否把每條訊息的發送時間注入到上下文中，以便模型理解訊息發送時間，注意開啟會消耗更多token</string><string name="assistant_page_thinking_budget">思考預算</string><string name="assistant_page_thinking_budget_desc">模型應該生成的思考內容的token數量。0表示將禁用思考，留空表示使用模型的預設值。</string><string name="assistant_page_thinking_budget_tokens">%s token</string><string name="assistant_page_thinking_budget_default">預設</string><string name="assistant_page_thinking_budget_warning">注意，由於不同提供商定義了各種不同的思考預算API格式，APP無法考慮到，因此這個選項不一定起作用，如果不起作用，推薦使用下面的自定義body來自定義請求</string><string name="assistant_page_custom_headers">自定義 Headers</string><string name="assistant_page_header_name">Header 名稱</string><string name="assistant_page_header_value">Header 值</string><string name="assistant_page_delete_header">刪除 Header</string><string name="assistant_page_add_header">添加 Header</string><string name="assistant_page_custom_bodies">自定義 Body</string><string name="assistant_page_body_key">Body Key</string><string name="assistant_page_body_value">Body 值 (JSON)</string><string name="assistant_page_invalid_json">無效的 JSON: %s</string><string name="assistant_page_delete_body">刪除 Body</string><string name="assistant_page_add_body">添加 Body</string><string name="notification_channel_chat_completed">聊天完成</string><string name="setting_page_theme_type_standard">標準</string><string name="setting_page_theme_type_medium_contrast">中對比</string><string name="setting_page_theme_type_high_contrast">高對比</string><string name="theme_name_black">中性黑</string><string name="theme_name_sakura">櫻花粉</string><string name="theme_name_ocean">海灣藍</string><string name="theme_name_spring">原野綠</string><string name="menu_page_morning_greeting">早上好\uD83D\uDC4B</string><string name="menu_page_afternoon_greeting">下午好\uD83D\uDC4B</string><string name="menu_page_evening_greeting">晚上好\uD83D\uDC4B</string><string name="menu_page_night_greeting">夜深了，注意休息\uD83D\uDC4B</string><string name="menu_page_ai_translator">AI翻譯</string><string name="menu_page_knowledge_base">知識庫 (建設中)</string><string name="menu_page_llm_leaderboard">LLM排行榜</string><string name="model_list_select_model">選擇模型</string><string name="model_list_no_providers">沒有可用AI提供商，請在設置添加</string><string name="model_list_favorite">收藏</string><string name="model_list_chat">聊天</string><string name="model_list_embedding">嵌入</string><string name="code_block_copy">複製代碼</string><string name="code_block_preview">預覽</string><string name="mermaid_export">匯出</string><string name="mermaid_export_success">匯出成功</string><string name="mermaid_export_failed">匯出失敗</string><string name="setting_display_page_chat_list_model_icon_title">聊天列表模型圖標</string><string name="setting_display_page_chat_list_model_icon_desc">是否在聊天列表消息中顯示模型圖標</string><string name="setting_display_page_show_token_usage_title">顯示Token消耗</string><string name="setting_display_page_show_token_usage_desc">在對話底部顯示Token消耗</string><string name="setting_display_page_auto_collapse_thinking_title">自動折疊思考</string><string name="setting_display_page_auto_collapse_thinking_desc">思考完成自動折疊思考內容</string><string name="setting_display_page_show_updates_title">顯示更新</string><string name="setting_display_page_show_updates_desc">是否顯示應用更新通知</string><string name="setting_display_page_title">顯示設定</string><string name="setting_page_color_mode">顏色模式</string><string name="setting_page_color_mode_system">跟隨系統</string><string name="setting_page_color_mode_light">淺色</string><string name="setting_page_color_mode_dark">深色</string><string name="assistant_page_tab_basic">基本設定</string><string name="assistant_page_tab_prompt">提示詞</string><string name="assistant_page_tab_memory">記憶</string><string name="assistant_page_tab_request">自定義請求</string><string name="assistant_page_stream_output">串流輸出</string><string name="assistant_page_stream_output_desc">是否啟用訊息的串流輸出</string></file><file name="backup_rules" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\FromTX3\MyCode\rikkahub\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\resValues\debug"/><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\processDebugGoogleServices"/><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"/><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\localeConfig\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\resValues\debug"/><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\processDebugGoogleServices"><file path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">118046921355</string><string name="google_api_key" translatable="false">AIzaSyAqtEHJi8dAlMxeWFTlOtFOKiNt_wIYkOs</string><string name="google_app_id" translatable="false">1:118046921355:android:08fae5eadabfe77598fa8e</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAqtEHJi8dAlMxeWFTlOtFOKiNt_wIYkOs</string><string name="google_storage_bucket" translatable="false">rikkahub.firebasestorage.app</string><string name="project_id" translatable="false">rikkahub</string></file></source><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"><file path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\injectCrashlyticsMappingFileIdDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source><source path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\localeConfig\debug"><file name="_generated_res_locale_config" path="D:\FromTX3\MyCode\rikkahub\app\build\generated\res\localeConfig\debug\xml\_generated_res_locale_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug" generated-set="res-injectCrashlyticsMappingFileIdDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>