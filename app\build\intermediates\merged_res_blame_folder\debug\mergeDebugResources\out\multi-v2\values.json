{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\me.rerere.rikkahub.app-mergeDebugResources-108:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f1b106457389d514166f73b1eda76c20\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1956", "startColumns": "4", "startOffsets": "130439", "endColumns": "42", "endOffsets": "130477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\824ae5c8d440c8e3d32b709ff4854e24\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1958", "startColumns": "4", "startOffsets": "130542", "endColumns": "53", "endOffsets": "130591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b8d754eb5303359053679da12bad27ab\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "559,560,561,569", "startColumns": "4,4,4,4", "startOffsets": "28624,28689,28759,29276", "endColumns": "64,69,63,60", "endOffsets": "28684,28754,28818,29332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\48c9216ee5d6b429524fc837ac071821\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2076", "startColumns": "4", "startOffsets": "138175", "endColumns": "82", "endOffsets": "138253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,216,217,218,219,220,221,222,223,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,420,421,422,440,442,451,452,453,454,455,456,457,458,459,460,461,465,466,467,468,470,471,472,473,474,475,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,514,515,526,527,535,536,568,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,1065,1069,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1826,1827,1890,1891,1892,1894,1895,1896,1897,1898,1899,1901,1902,1903,1904,1905,1906,1912,1915,1916,1917,1937,1938,1939,1940,1941,1942,1943,1954,1964,1965,1968,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2045,2078,2133,2134,2135,2136,2137,2138,2148,2149,2150,2178,2210,2213,2214,2215,2216,2222,2223,2226,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2439,2442,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2498,2499,2500,2501,2502,2511,2514,2515,2564,2565,2609,2613,2617,2621,2625,2626,2669,2677,2684,2854,2857,2867,2876,2885,2954,2955,2956,2957,2963,2964,2965,2966,2967,2968,2974,2975,2976,2977,2978,2983,2984,2988,2989,2995,2999,3000,3001,3002,3012,3013,3014,3018,3019,3025,3029,3099,3102,3103,3108,3109,3112,3113,3114,3115,3379,3386,3647,3653,3917,3924,4185,4191,4254,4336,4388,4470,4532,4614,4678,4730,4812,4820,4826,4837,4841,4845,4858,5633,5649,5656,5662,5679,5692,5712,5729,5738,5743,5750,5770,5783,5800,5806,5812,5819,5823,5829,5843,5846,5856,5857,5858,5906,5910,5914,5918,5919,5920,5923,5939,5946,5960,6005,6039,6045,6049,6053,6058,6065,6071,6072,6075,6079,6084,6097,6101,6106,6111,6116,6119,6122,6125,6129,6272,6273,6274,6275,6356,6357,6358,6359,6360,6361,6362,6363,6364,6365,6366,6367,6368,6369,6370,6371,6372,6373,6374,6378,6382,6386,6390,6394,6398,6402,6403,6404,6405,6406,6407,6408,6409,6413,6417,6418,6422,6423,6426,6430,6433,6436,6439,6443,6446,6449,6453,6457,6461,6465,6468,6469,6470,6471,6474,6478,6481,6484,6487,6490,6493,6496,6567,6570,6571,6574,6577,6578,6581,6582,6583,6587,6588,6593,6600,6607,6614,6621,6628,6635,6642,6649,6656,6665,6674,6683,6690,6699,6708,6711,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6730,6735,6740,6743,6744,6745,6746,6747,6755,6763,6764,6772,6776,6784,6792,6800,6808,6816,6817,6825,6833,6834,6837,6876,6878,6883,6885,6890,6894,6898,6899,6900,6901,6905,6909,6910,6914,6915,6916,6917,6918,6919,6920,6921,6922,6923,6924,6928,6929,6930,6931,6935,6936,6937,6938,6942,6946,6947,6951,6952,6953,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6975,6976,6977,6978,6982,6983,6984,6990,6991,6995,6997,6998,7003,7004,7005,7006,7007,7008,7012,7013,7014,7020,7021,7025,7027,7031,7035,7039,7056,7057,7058,7059,7062,7065,7068,7071,7074,7079,7083,7086,7087,7092,7096,7101,7107,7113,7118,7122,7127,7131,7135,7176,7177,7178,7179,7180,7184,7185,7186,7187,7191,7195,7199,7203,7207,7211,7215,7219,7225,7226,7267,7281,7286,7312,7319,7322,7333,7338,7341,7344,7399,7405,7406,7409,7412,7415,7418,7421,7424,7427,7431,7434,7435,7436,7444,7452,7455,7460,7465,7470,7475,7479,7483,7484,7492,7493,7494,7495,7496,7504,7509,7514,7515,7516,7517,7542,7548,7553,7556,7560,7563,7567,7577,7580,7585,7588,7592,7693,7701,7715,7728,7732,7747,7758,7761,7772,7777,7781,7816,7817,7818,7830,7838,7846,7854,7862,7882,7885,7912,7917,7937,7940,7943,7950,7963,7972,7975,7995,8005,8009,8013,8026,8030,8034,8038,8044,8048,8065,8073,8077,8081,8085,8088,8092,8096,8100,8110,8117,8124,8128,8154,8164,8189,8198,8218,8228,8232,8242,8267,8277,8280,8287,8294,8301,8302,8303,8304,8305,8312,8316,8322,8328,8329,8342,8343,8344,8347,8350,8353,8356,8359,8362,8365,8368,8371,8374,8377,8380,8383,8386,8389,8392,8395,8398,8401,8404,8407,8408,8416,8424,8425,8438,8448,8452,8457,8462,8466,8469,8473,8477,8480,8484,8487,8491,8496,8501,8504,8511,8515,8519,8528,8533,8538,8539,8543,8546,8550,8563,8568,8576,8580,8584,8601,8605,8610,8628,8635,8639,8669,8672,8675,8678,8681,8684,8687,8706,8712,8720,8727,8739,8747,8752,8760,8764,8782,8789,8805,8809,8817,8820,8825,8826,8827,8828,8832,8836,8840,8844,8879,8882,8886,8890,8924,8927,8931,8935,8944,8950,8953,8963,8967,8968,8975,8979,8986,8987,8988,8991,8996,9001,9002,9006,9021,9040,9044,9045,9057,9067,9068,9080,9085,9109,9112,9118,9121,9130,9138,9142,9145,9148,9151,9155,9158,9175,9179,9182,9197,9200,9208,9213,9220,9225,9226,9231,9232,9238,9244,9250,9282,9293,9310,9317,9321,9324,9337,9346,9350,9355,9359,9363,9367,9371,9375,9379,9383,9388,9391,9403,9408,9417,9420,9427,9428,9432,9441,9447,9451,9452,9456,9477,9483,9487,9491,9492,9510,9511,9512,9513,9514,9519,9522,9523,9529,9530,9542,9554,9561,9562,9567,9572,9573,9577,9591,9596,9602,9608,9614,9619,9625,9631,9632,9638,9653,9658,9667,9676,9679,9693,9698,9709,9713,9722,9731,9732,9739,13124,13125,13126,13127,13128,13129,13130,13131,13132,13133,13134,13135,13136,13137,13138,13139,13140,13141,13142,13143,13144,13145,13146,13147,13148,13149,13150,13151,13152,13153,13154,13155,13156,13157,13158,13159,13160,13161,13162,13163,13164,13165,13166,13167,13168,13169,13170,13171,13172,13173,13174,13175,13176,13177,13178,13179,13180,13181,13182,13183,13184,13185,13186,13187,13188,13189,13190,13191,13192,13193,13194,13195,13196,13197,13198,13199,13200,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13216,13217,13218,13219,13220,13221,13222,13223,13224,13225,13226,13227,13228,13229,13230,13231,13232,13233,13234,13235,13236,13237,13238,13239,13240,13241,13242,13243,13244,13245,13246,13247,13248,13249,13250,13251,13252,13253,13254,13255,13256,13257,13258,13259,13260,13261,13262,13263,13264,13265,13266,13267,13268,13269,13270,13271,13272,13273,13274,13275,13276,13277,13278,13279,13280,13281,13282,13283,13284,13285,13286,13287,13288,13289,13290,13291,13292,13293,13294,13295,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "207,449,902,957,1013,1073,1134,1199,1354,1404,1454,1507,1565,1664,1733,1781,1852,1924,1996,2069,2136,2185,2239,2276,2327,2387,2434,2490,2539,2597,2651,2712,2768,2819,2879,2935,2998,3047,3103,3159,3209,3268,3323,3385,3432,3486,3542,3594,3649,3703,3757,3811,3860,3918,3972,4029,4085,4132,4185,4241,4301,4364,4423,4485,4535,4589,4643,4691,4748,4801,5363,5417,6250,6361,6423,6479,6539,6592,6653,6732,6813,6885,6964,7044,7120,7198,7267,7343,7420,7491,7564,7640,7718,7787,7863,7940,8004,8075,10497,10593,10646,10902,10969,11022,11074,11124,11182,11247,11295,16789,16856,16922,16980,17049,17107,17176,17246,17319,17393,17461,17528,17598,17664,17737,17797,17873,17933,17993,18068,18136,18202,18270,18330,18389,18446,18512,18574,18631,18699,18772,18842,18904,18965,19033,19095,19165,19234,19290,19349,19411,19473,19540,19597,19658,19719,19780,19841,19897,19953,20009,20065,20123,20181,20239,20297,20354,20411,20468,20525,20584,20643,20701,20784,20867,20940,20994,21063,21119,21200,21281,21352,21687,21740,21798,22539,22653,23006,23066,23120,23190,23260,23325,23391,23456,23524,23593,23661,23791,23844,23903,23961,24059,24109,24161,24207,24257,24313,24407,24465,24523,24585,24648,24710,24769,24829,24894,24960,25025,25087,25149,25211,25273,25335,25397,25463,25530,25596,25659,25723,25786,25854,25915,25977,26039,26102,26166,26229,26293,26371,26430,26496,26576,26637,26731,26789,27222,27267,27621,27685,29214,32051,32125,32196,32262,32336,32405,32476,32549,32620,32688,32761,32837,32907,32985,33053,33119,33180,33249,33313,33379,33447,33513,33576,33644,33715,33780,33853,33916,33997,34061,34127,34197,34267,34337,34407,37553,37610,37668,37727,37787,37846,37905,37964,38023,38082,38141,38200,38259,38318,38377,38437,38498,38560,38621,38682,38743,38804,38865,38926,38986,39047,39108,39168,39229,39290,39351,39412,39473,39534,39595,39656,39717,39778,39839,39907,39976,40046,40115,40184,40253,40322,40391,40460,40529,40598,40667,40736,40796,40857,40919,40980,41041,41102,41163,41224,41285,41346,41407,41468,41529,41591,41654,41718,41781,41844,41907,41970,42033,42096,42159,42222,42285,42348,42409,42471,42534,42596,42658,42720,42782,42844,42906,42968,43030,43092,43154,43211,43297,43377,43467,43562,43654,43746,43836,43919,44012,44099,44196,44287,44388,44475,44578,44667,44766,44858,44958,45042,45136,45224,45322,45405,45496,45590,45689,45791,45889,45989,46076,46176,46262,46358,46446,46527,46618,46714,46807,46900,46991,47076,47170,47259,47357,47450,47552,47640,47744,47835,47935,48028,48129,48214,48309,48398,48497,48582,48674,48769,48869,48972,49071,49174,49263,49364,49451,49548,49636,49732,49824,49924,50014,50112,50197,50286,50375,50468,50555,51318,51384,51460,51529,51608,51681,51761,51841,51918,51986,52064,52140,52211,52292,52365,52448,52523,52608,52681,52762,52843,52917,53001,53071,53149,53219,53299,53377,53449,53531,53601,53678,53758,53843,53931,54015,54102,54176,54254,54332,54403,54484,54575,54658,54754,54852,54959,55024,55090,55143,55219,55285,55372,55448,64745,64980,65575,65629,65708,65786,65859,65924,65987,66053,66124,66195,66265,66327,66396,66462,66522,66589,66656,66712,66763,66816,66868,66922,66993,67056,67115,67177,67236,67309,67376,67446,67506,67569,67644,67716,67812,67883,67939,68010,68067,68124,68190,68254,68325,68382,68435,68498,68550,68608,70837,70906,70972,71031,71114,71173,71230,71297,71367,71441,71503,71572,71642,71741,71838,71937,72023,72109,72190,72265,72354,72445,72529,72588,72634,72700,72757,72824,72881,72963,73028,73094,73217,73301,73422,73487,73549,73647,73721,73804,73893,73957,74036,74110,74172,74268,74333,74392,74448,74504,74564,74671,74718,74778,74839,74903,74964,75024,75082,75125,75174,75226,75277,75329,75378,75427,75492,75558,75618,75679,75735,75794,75843,75891,75949,76006,76108,76165,76240,76288,76339,76401,76466,76518,76592,76655,76718,76786,76836,76898,76958,77015,77075,77124,77192,77298,77400,77469,77540,77596,77645,77745,77816,77926,78017,78099,78197,78253,78354,78464,78563,78626,78732,78809,78921,79048,79160,79287,79357,79471,79602,79699,79767,79885,79988,80106,80167,80241,80308,80413,80535,80609,80676,80786,80885,80958,81055,81177,81295,81413,81474,81596,81713,81781,81887,81989,82069,82140,82236,82303,82377,82451,82537,82627,82705,82782,82882,82953,83074,83195,83259,83384,83458,83582,83706,83773,83882,84010,84122,84201,84279,84380,84451,84573,84695,84760,84886,84998,85104,85172,85271,85375,85438,85504,85588,85701,85814,85932,86010,86082,86218,86354,86439,86579,86717,86855,86997,87079,87165,87242,87315,87424,87535,87663,87791,87923,88053,88183,88317,88406,88468,88564,88631,88748,88869,88966,89048,89135,89222,89353,89484,89619,89696,89773,89884,89998,90072,90181,90293,90360,90433,90498,90600,90696,90800,90868,90933,91027,91099,91209,91315,91388,91479,91581,91684,91779,91886,91991,92113,92235,92361,92420,92478,92602,92726,92854,92972,93090,93212,93298,93395,93529,93663,93743,93881,94013,94145,94281,94356,94432,94535,94609,94722,94803,94860,94921,94980,95040,95098,95159,95217,95267,95316,95383,95442,95501,95550,95621,95705,95775,95846,95926,95995,96058,96126,96192,96260,96325,96391,96468,96546,96652,96758,96854,96983,97072,97199,97265,97335,97421,97487,97570,97644,97742,97838,97934,98032,98141,98236,98325,98387,98447,98512,98569,98650,98704,98761,98858,98968,99029,99144,99265,99360,99452,99545,99601,99660,99709,99801,99850,99904,99958,100012,100066,100120,100175,100285,100395,100503,100613,100723,100833,100943,101051,101157,101261,101365,101469,101564,101659,101752,101845,101949,102055,102159,102263,102356,102449,102542,102635,102743,102849,102955,103061,103158,103253,103348,103443,103549,103655,103761,103867,103965,104060,104156,104253,104318,104422,104480,104544,104605,104667,104727,104792,104854,104922,104980,105043,105106,105173,105248,105321,105387,105439,105492,105544,105601,105685,105780,105865,105946,106026,106103,106182,106259,106333,106407,106478,106558,106630,106705,106770,106831,106891,106966,107040,107113,107183,107255,107325,107398,107462,107532,107578,107647,107699,107784,107867,107924,107990,108057,108123,108204,108279,108335,108388,108449,108507,108557,108606,108655,108704,108766,108818,108863,108944,108995,109049,109102,109156,109207,109256,109322,109373,109434,109495,109557,109607,109648,109725,109784,109843,109902,109963,110019,110075,110142,110203,110268,110323,110388,110457,110525,110603,110672,110732,110803,110877,110942,111014,111084,111151,111235,111304,111371,111441,111504,111571,111639,111722,111801,111891,111968,112036,112103,112181,112238,112295,112363,112429,112485,112545,112604,112658,112708,112758,112806,112868,112919,112992,113072,113152,113216,113283,113354,113412,113473,113539,113598,113665,113725,113785,113848,113916,113977,114044,114122,114192,114241,114298,114367,114428,114516,114604,114692,114780,114867,114954,115041,115128,115186,115260,115330,115386,115457,115522,115584,115659,115732,115822,115888,115954,116015,116079,116141,116199,116270,116353,116412,116483,116549,116614,116675,116734,116805,116871,116936,117019,117095,117170,117251,117311,117380,117450,117519,117574,117630,117686,117747,117805,117861,117920,117974,118029,118091,118148,118242,118311,118412,118463,118533,118596,118652,118710,118769,118823,118909,118993,119063,119132,119202,119317,119438,119505,119572,119647,119714,119773,119827,119881,119935,119988,120040,123032,123169,126973,127022,127072,127163,127211,127267,127325,127387,127442,127553,127624,127688,127747,127809,127875,128189,128334,128378,128423,129515,129566,129613,129658,129709,129760,129811,130348,130889,130955,131134,131197,131337,131394,131448,131503,131561,131616,131675,131731,131800,131869,131938,132008,132071,132134,132197,132260,132325,132390,132455,132520,132583,132647,132711,132775,132826,132904,132982,133053,133125,133198,133343,133409,133475,133543,133611,133677,133744,133818,133881,133938,133998,134063,134130,134195,134252,134313,134371,134475,134585,134694,134798,134876,134941,135008,135074,135144,135191,135243,135293,135989,138304,143190,143321,143505,143683,143921,144110,144897,144995,145110,147074,150361,150521,150586,150675,150832,151504,151657,151923,158974,159161,159257,159347,159443,159533,159699,159822,159945,160115,160221,160336,160451,160553,160659,160776,166033,166115,166288,166456,166604,166763,166918,167091,167208,167325,167493,167605,167719,167891,168067,168225,168358,168470,168616,168768,168900,169043,170334,170512,170648,170744,170880,170975,171142,171235,171327,171514,171670,171848,172012,172194,172511,172693,172875,173065,173297,173487,173664,173826,173983,174093,174276,174413,174617,174801,174985,175145,175303,175487,175714,175917,176088,176308,176530,176685,176885,177069,177172,177362,177503,177668,177839,178039,178243,178445,178610,178815,179014,179213,179410,179501,179650,179800,179884,180033,180178,180330,180471,180637,180993,181071,181372,181538,181693,182234,182392,182556,186562,186785,189898,190175,190447,190725,190970,191032,193860,194311,194767,205904,206052,206566,207003,207437,211777,211862,211983,212082,212487,212584,212701,212788,212911,213012,213418,213517,213636,213729,213836,214179,214286,214531,214652,215061,215309,215409,215514,215633,216142,216289,216408,216659,216792,217207,217461,222676,222923,223048,223456,223577,223805,223926,224059,224206,244928,245420,265891,266315,287082,287576,308092,308518,313359,318776,322867,328298,333040,338417,342401,346393,351784,352331,352764,353520,353750,353993,355160,403330,404234,404818,405291,406721,407465,408658,409712,410190,410483,410866,412381,413146,414289,414730,415171,415767,416041,416452,417468,417646,418399,418536,418627,420821,421087,421409,421619,421728,421847,422031,423149,423619,424370,426953,428777,429153,429381,429637,429896,430472,430826,430948,431087,431379,431639,432567,432853,433256,433658,434001,434213,434414,434627,434916,445803,445876,445963,446048,450968,451080,451186,451309,451441,451564,451694,451818,451951,452082,452207,452324,452444,452576,452704,452818,452936,453049,453170,453358,453545,453726,453909,454093,454258,454440,454560,454680,454788,454898,455010,455118,455228,455393,455559,455711,455876,455977,456097,456268,456429,456592,456753,456920,457039,457156,457336,457518,457699,457882,458037,458182,458304,458439,458602,458795,458921,459073,459215,459385,459541,459713,466476,466671,466763,466936,467098,467193,467362,467456,467545,467788,467877,468170,468586,469006,469427,469853,470270,470686,471103,471521,471935,472405,472878,473350,473761,474232,474704,474894,475100,475206,475314,475420,475532,475646,475758,475872,475988,476102,476210,476320,476428,476690,477069,477473,477620,477728,477838,477946,478060,478469,478883,478999,479417,479658,480088,480523,480933,481355,481765,481887,482296,482712,482834,483052,485872,485940,486284,486364,486720,486870,487014,487090,487202,487292,487554,487819,487927,488079,488187,488263,488375,488465,488567,488675,488783,488883,488991,489076,489242,489346,489474,489561,489728,489806,489920,490012,490276,490543,490653,490806,490916,491000,491389,491487,491595,491689,491819,491927,492049,492185,492293,492413,492547,492669,492797,492939,493065,493205,493331,493449,493581,493679,493789,494089,494201,494319,494783,494899,495202,495328,495424,495825,495935,496059,496197,496307,496429,496741,496865,496995,497471,497599,497914,498052,498214,498430,498586,499878,499946,500030,500134,500337,500526,500727,500920,501125,501438,501650,501816,501932,502178,502394,502707,503133,503595,503832,503984,504244,504388,504530,507762,507876,507996,508112,508206,508527,508626,508744,508845,509124,509409,509688,509970,510223,510482,510735,510991,511415,511491,514741,516096,516540,518394,518969,519177,520187,520567,520733,520874,525894,526320,526432,526567,526720,526917,527088,527271,527446,527633,527905,528063,528147,528251,528738,529294,529452,529671,529902,530125,530360,530582,530848,530986,531585,531699,531837,531949,532073,532644,533139,533685,533830,533923,534015,535942,536512,536810,536999,537205,537398,537608,538492,538637,539029,539187,539404,547460,547892,548767,549387,549584,550532,551297,551420,552193,552414,552614,554591,554691,554781,555467,556220,556985,557748,558523,559736,559901,561514,561835,562898,563108,563278,563848,564743,565376,565542,567028,567644,567880,568101,569059,569324,569589,569836,570250,570486,571771,572220,572407,572656,572898,573074,573315,573548,573773,574368,574843,575367,575628,576979,577454,578680,579150,580198,580650,580894,581351,582596,583079,583229,583784,584236,584636,584789,584934,585077,585147,585575,585863,586367,586876,586992,587894,588016,588128,588305,588571,588841,589107,589375,589631,589891,590147,590405,590657,590913,591165,591419,591651,591887,592139,592395,592647,592901,593133,593367,593479,594131,594586,594710,595802,596617,596813,597137,597526,597878,598119,598333,598632,598824,599139,599346,599692,599992,600393,600612,601025,601262,601632,602356,602711,602980,603120,603374,603518,603795,604787,605196,605828,606174,606542,607616,607979,608379,609887,610472,610790,613325,613519,613737,613963,614175,614374,614581,615785,616080,616637,617027,617659,618136,618381,618868,619114,620310,620707,621713,621935,622358,622549,622928,623016,623124,623232,623545,623870,624189,624520,627223,627411,627672,627921,630505,630697,630962,631215,631747,632155,632354,632938,633173,633297,633709,633923,634325,634428,634558,634733,634985,635181,635321,635515,636526,637595,637883,638013,638790,639447,639593,640299,640537,642077,642227,642644,642809,643495,643965,644161,644252,644336,644480,644714,644881,645809,646095,646255,646870,647029,647357,647584,648096,648458,648537,648876,648981,649346,649717,650078,651952,652581,653657,654081,654334,654486,655534,656271,656474,656720,656967,657185,657427,657748,658012,658317,658540,658851,659040,659755,660024,660518,660744,661184,661343,661627,662372,662737,663042,663200,663438,664757,665155,665383,665603,665745,667035,667141,667271,667409,667533,667821,667990,668090,668375,668489,669372,670127,670566,670690,670936,671129,671263,671454,672233,672451,672742,673021,673338,673560,673855,674138,674242,674583,675399,675715,676276,676782,676987,677773,678178,678839,679028,679579,680145,680265,680667,802884,802979,803072,803135,803217,803310,803403,803490,803588,803679,803770,803858,803942,804038,804138,804244,804347,804448,804552,804658,804757,804863,804965,805072,805181,805292,805423,805543,805659,805777,805876,805983,806099,806218,806346,806435,806530,806607,806696,806787,806880,806954,807051,807146,807244,807343,807447,807543,807645,807748,807848,807951,808036,808137,808235,808325,808420,808507,808613,808715,808809,808900,808994,809070,809162,809251,809354,809465,809548,809634,809729,809826,809922,810010,810111,810212,810315,810421,810519,810616,810711,810809,810912,811012,811115,811220,811338,811454,811549,811642,811727,811823,811917,812009,812092,812196,812301,812401,812502,812607,812707,812808,812907,813009,813103,813210,813312,813415,813508,813604,813706,813809,813905,814007,814110,814207,814310,814408,814512,814617,814714,814822,814936,815051,815159,815273,815388,815490,815595,815703,815813,815929,816046,816141,816238,816337,816442,816548,816647,816752,816858,816958,817064,817165,817272,817391,817490,817595,817697,817799,817899,818002,818097,818201,818286,818390,818494,818592,818696,818802,818900,819005,819103,819216,819310,819399,819488,819571,819662,819745,819843,819933,820029,820118,820212,820300,820396,820481,820589,820690,820791,820889,820995,821086,821185,821282,821380,821476,821569,821679,821777,821872,821982,822074,822174,822273,822360,822464,822569,822668,822775,822882,822981,823090,823182,823293,823404,823515,823619,823734,823850,823977,824097,824192,824287,824384,824483,824575,824674,824766,824865,824951,825045,825148,825244,825347,825443,825546,825643,825741,825844,825937,826027,826128,826211,826302,826387,826479,826582,826677,826773,826866,826960,827039,827146,827237,827336,827429,827532,827636,827737,827838,827942,828036,828140,828244,828357,828463,828569,828677,828794,828895,829003,829103,829206,829311,829418,829514,829593,829683,829767,829859,829932,830024,830113,830205,830290,830387,830480,830575,830674,830771,830862,830953,831045,831140,831247,831355,831457,831554,831651,831744,831831,831915,832012,832109,832202,832289,832380,832479,832578,832673,832762,832843,832942,833046,833143,833248,833345,833429,833528,833632,833729,833834,833931,834029,834130,834236,834335,834442,834541,834640,834731,834820,834909,834991,835084,835175,835286,835387,835487,835599,835712,835810,835918,836012,836112,836201,836293,836404,836514,836609,836725,836851,836977,837096,837224,837349,837474,837592,837719,837828,837937,838050,838173,838296,838412,838537,838634,838742,838864,838980,839096,839205,839293,839394,839483,839584,839671,839759,839856,839948,840054,840154,840230", "endLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,216,217,218,219,220,221,222,223,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,415,420,421,422,440,442,451,452,453,454,455,456,457,458,459,460,464,465,466,467,468,470,471,472,473,474,475,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,514,515,526,527,535,536,568,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,1065,1069,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1826,1827,1890,1891,1892,1894,1895,1896,1897,1898,1899,1901,1902,1903,1904,1905,1906,1912,1915,1916,1917,1937,1938,1939,1940,1941,1942,1943,1954,1964,1965,1968,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2048,2078,2133,2134,2135,2136,2137,2138,2148,2149,2150,2178,2212,2213,2214,2215,2216,2222,2223,2226,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2427,2428,2429,2430,2431,2432,2433,2434,2435,2438,2441,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2498,2499,2500,2501,2502,2513,2514,2515,2564,2565,2612,2616,2620,2624,2625,2629,2676,2683,2691,2856,2866,2875,2884,2893,2954,2955,2956,2962,2963,2964,2965,2966,2967,2973,2974,2975,2976,2977,2982,2983,2987,2988,2994,2998,2999,3000,3001,3011,3012,3013,3017,3018,3024,3028,3029,3101,3102,3107,3108,3111,3112,3113,3114,3378,3385,3646,3652,3916,3923,4184,4190,4253,4335,4387,4469,4531,4613,4677,4729,4811,4819,4825,4836,4840,4844,4857,4872,5648,5655,5661,5678,5691,5711,5728,5737,5742,5749,5769,5782,5799,5805,5811,5818,5822,5828,5842,5845,5855,5856,5857,5905,5909,5913,5917,5918,5919,5922,5938,5945,5959,6004,6005,6044,6048,6052,6057,6064,6070,6071,6074,6078,6083,6096,6100,6105,6110,6115,6118,6121,6124,6128,6132,6272,6273,6274,6275,6356,6357,6358,6359,6360,6361,6362,6363,6364,6365,6366,6367,6368,6369,6370,6371,6372,6373,6377,6381,6385,6389,6393,6397,6401,6402,6403,6404,6405,6406,6407,6408,6412,6416,6417,6421,6422,6425,6429,6432,6435,6438,6442,6445,6448,6452,6456,6460,6464,6467,6468,6469,6470,6473,6477,6480,6483,6486,6489,6492,6495,6499,6569,6570,6573,6576,6577,6580,6581,6582,6586,6587,6592,6599,6606,6613,6620,6627,6634,6641,6648,6655,6664,6673,6682,6689,6698,6707,6710,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6729,6734,6739,6742,6743,6744,6745,6746,6754,6762,6763,6771,6775,6783,6791,6799,6807,6815,6816,6824,6832,6833,6836,6839,6877,6882,6884,6889,6893,6897,6898,6899,6900,6904,6908,6909,6913,6914,6915,6916,6917,6918,6919,6920,6921,6922,6923,6927,6928,6929,6930,6934,6935,6936,6937,6941,6945,6946,6950,6951,6952,6957,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6975,6976,6977,6981,6982,6983,6989,6990,6994,6996,6997,7002,7003,7004,7005,7006,7007,7011,7012,7013,7019,7020,7024,7026,7030,7034,7038,7042,7056,7057,7058,7061,7064,7067,7070,7073,7078,7082,7085,7086,7091,7095,7100,7106,7112,7117,7121,7126,7130,7134,7175,7176,7177,7178,7179,7183,7184,7185,7186,7190,7194,7198,7202,7206,7210,7214,7218,7224,7225,7266,7280,7285,7311,7318,7321,7332,7337,7340,7343,7398,7404,7405,7408,7411,7414,7417,7420,7423,7426,7430,7433,7434,7435,7443,7451,7454,7459,7464,7469,7474,7478,7482,7483,7491,7492,7493,7494,7495,7503,7508,7513,7514,7515,7516,7541,7547,7552,7555,7559,7562,7566,7576,7579,7584,7587,7591,7595,7700,7714,7727,7731,7746,7757,7760,7771,7776,7780,7815,7816,7817,7829,7837,7845,7853,7861,7881,7884,7911,7916,7936,7939,7942,7949,7962,7971,7974,7994,8004,8008,8012,8025,8029,8033,8037,8043,8047,8064,8072,8076,8080,8084,8087,8091,8095,8099,8109,8116,8123,8127,8153,8163,8188,8197,8217,8227,8231,8241,8266,8276,8279,8286,8293,8300,8301,8302,8303,8304,8311,8315,8321,8327,8328,8341,8342,8343,8346,8349,8352,8355,8358,8361,8364,8367,8370,8373,8376,8379,8382,8385,8388,8391,8394,8397,8400,8403,8406,8407,8415,8423,8424,8437,8447,8451,8456,8461,8465,8468,8472,8476,8479,8483,8486,8490,8495,8500,8503,8510,8514,8518,8527,8532,8537,8538,8542,8545,8549,8562,8567,8575,8579,8583,8600,8604,8609,8627,8634,8638,8668,8671,8674,8677,8680,8683,8686,8705,8711,8719,8726,8738,8746,8751,8759,8763,8781,8788,8804,8808,8816,8819,8824,8825,8826,8827,8831,8835,8839,8843,8878,8881,8885,8889,8923,8926,8930,8934,8943,8949,8952,8962,8966,8967,8974,8978,8985,8986,8987,8990,8995,9000,9001,9005,9020,9039,9043,9044,9056,9066,9067,9079,9084,9108,9111,9117,9120,9129,9137,9141,9144,9147,9150,9154,9157,9174,9178,9181,9196,9199,9207,9212,9219,9224,9225,9230,9231,9237,9243,9249,9281,9292,9309,9316,9320,9323,9336,9345,9349,9354,9358,9362,9366,9370,9374,9378,9382,9387,9390,9402,9407,9416,9419,9426,9427,9431,9440,9446,9450,9451,9455,9476,9482,9486,9490,9491,9509,9510,9511,9512,9513,9518,9521,9522,9528,9529,9541,9553,9560,9561,9566,9571,9572,9576,9590,9595,9601,9607,9613,9618,9624,9630,9631,9637,9652,9657,9666,9675,9678,9692,9697,9708,9712,9721,9730,9731,9738,9746,13124,13125,13126,13127,13128,13129,13130,13131,13132,13133,13134,13135,13136,13137,13138,13139,13140,13141,13142,13143,13144,13145,13146,13147,13148,13149,13150,13151,13152,13153,13154,13155,13156,13157,13158,13159,13160,13161,13162,13163,13164,13165,13166,13167,13168,13169,13170,13171,13172,13173,13174,13175,13176,13177,13178,13179,13180,13181,13182,13183,13184,13185,13186,13187,13188,13189,13190,13191,13192,13193,13194,13195,13196,13197,13198,13199,13200,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13216,13217,13218,13219,13220,13221,13222,13223,13224,13225,13226,13227,13228,13229,13230,13231,13232,13233,13234,13235,13236,13237,13238,13239,13240,13241,13242,13243,13244,13245,13246,13247,13248,13249,13250,13251,13252,13253,13254,13255,13256,13257,13258,13259,13260,13261,13262,13263,13264,13265,13266,13267,13268,13269,13270,13271,13272,13273,13274,13275,13276,13277,13278,13279,13280,13281,13282,13283,13284,13285,13286,13287,13288,13289,13290,13291,13292,13293,13294,13295,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "258,493,952,1008,1068,1129,1194,1249,1399,1449,1502,1560,1608,1728,1776,1847,1919,1991,2064,2131,2180,2234,2271,2322,2382,2429,2485,2534,2592,2646,2707,2763,2814,2874,2930,2993,3042,3098,3154,3204,3263,3318,3380,3427,3481,3537,3589,3644,3698,3752,3806,3855,3913,3967,4024,4080,4127,4180,4236,4296,4359,4418,4480,4530,4584,4638,4686,4743,4796,4852,5412,5468,6308,6418,6474,6534,6587,6648,6727,6808,6880,6959,7039,7115,7193,7262,7338,7415,7486,7559,7635,7713,7782,7858,7935,7999,8070,8142,10543,10641,10696,10964,11017,11069,11119,11177,11242,11290,11341,16851,16917,16975,17044,17102,17171,17241,17314,17388,17456,17523,17593,17659,17732,17792,17868,17928,17988,18063,18131,18197,18265,18325,18384,18441,18507,18569,18626,18694,18767,18837,18899,18960,19028,19090,19160,19229,19285,19344,19406,19468,19535,19592,19653,19714,19775,19836,19892,19948,20004,20060,20118,20176,20234,20292,20349,20406,20463,20520,20579,20638,20696,20779,20862,20935,20989,21058,21114,21195,21276,21347,21476,21735,21793,21851,22592,22694,23061,23115,23185,23255,23320,23386,23451,23519,23588,23656,23786,23839,23898,23956,24008,24104,24156,24202,24252,24308,24355,24460,24518,24580,24643,24705,24764,24824,24889,24955,25020,25082,25144,25206,25268,25330,25392,25458,25525,25591,25654,25718,25781,25849,25910,25972,26034,26097,26161,26224,26288,26366,26425,26491,26571,26632,26685,26784,26835,27262,27323,27680,27739,29271,32120,32191,32257,32331,32400,32471,32544,32615,32683,32756,32832,32902,32980,33048,33114,33175,33244,33308,33374,33442,33508,33571,33639,33710,33775,33848,33911,33992,34056,34122,34192,34262,34332,34402,34469,37605,37663,37722,37782,37841,37900,37959,38018,38077,38136,38195,38254,38313,38372,38432,38493,38555,38616,38677,38738,38799,38860,38921,38981,39042,39103,39163,39224,39285,39346,39407,39468,39529,39590,39651,39712,39773,39834,39902,39971,40041,40110,40179,40248,40317,40386,40455,40524,40593,40662,40731,40791,40852,40914,40975,41036,41097,41158,41219,41280,41341,41402,41463,41524,41586,41649,41713,41776,41839,41902,41965,42028,42091,42154,42217,42280,42343,42404,42466,42529,42591,42653,42715,42777,42839,42901,42963,43025,43087,43149,43206,43292,43372,43462,43557,43649,43741,43831,43914,44007,44094,44191,44282,44383,44470,44573,44662,44761,44853,44953,45037,45131,45219,45317,45400,45491,45585,45684,45786,45884,45984,46071,46171,46257,46353,46441,46522,46613,46709,46802,46895,46986,47071,47165,47254,47352,47445,47547,47635,47739,47830,47930,48023,48124,48209,48304,48393,48492,48577,48669,48764,48864,48967,49066,49169,49258,49359,49446,49543,49631,49727,49819,49919,50009,50107,50192,50281,50370,50463,50550,50641,51379,51455,51524,51603,51676,51756,51836,51913,51981,52059,52135,52206,52287,52360,52443,52518,52603,52676,52757,52838,52912,52996,53066,53144,53214,53294,53372,53444,53526,53596,53673,53753,53838,53926,54010,54097,54171,54249,54327,54398,54479,54570,54653,54749,54847,54954,55019,55085,55138,55214,55280,55367,55443,55519,64805,65030,65624,65703,65781,65854,65919,65982,66048,66119,66190,66260,66322,66391,66457,66517,66584,66651,66707,66758,66811,66863,66917,66988,67051,67110,67172,67231,67304,67371,67441,67501,67564,67639,67711,67807,67878,67934,68005,68062,68119,68185,68249,68320,68377,68430,68493,68545,68603,68670,70901,70967,71026,71109,71168,71225,71292,71362,71436,71498,71567,71637,71736,71833,71932,72018,72104,72185,72260,72349,72440,72524,72583,72629,72695,72752,72819,72876,72958,73023,73089,73212,73296,73417,73482,73544,73642,73716,73799,73888,73952,74031,74105,74167,74263,74328,74387,74443,74499,74559,74666,74713,74773,74834,74898,74959,75019,75077,75120,75169,75221,75272,75324,75373,75422,75487,75553,75613,75674,75730,75789,75838,75886,75944,76001,76103,76160,76235,76283,76334,76396,76461,76513,76587,76650,76713,76781,76831,76893,76953,77010,77070,77119,77187,77293,77395,77464,77535,77591,77640,77740,77811,77921,78012,78094,78192,78248,78349,78459,78558,78621,78727,78804,78916,79043,79155,79282,79352,79466,79597,79694,79762,79880,79983,80101,80162,80236,80303,80408,80530,80604,80671,80781,80880,80953,81050,81172,81290,81408,81469,81591,81708,81776,81882,81984,82064,82135,82231,82298,82372,82446,82532,82622,82700,82777,82877,82948,83069,83190,83254,83379,83453,83577,83701,83768,83877,84005,84117,84196,84274,84375,84446,84568,84690,84755,84881,84993,85099,85167,85266,85370,85433,85499,85583,85696,85809,85927,86005,86077,86213,86349,86434,86574,86712,86850,86992,87074,87160,87237,87310,87419,87530,87658,87786,87918,88048,88178,88312,88401,88463,88559,88626,88743,88864,88961,89043,89130,89217,89348,89479,89614,89691,89768,89879,89993,90067,90176,90288,90355,90428,90493,90595,90691,90795,90863,90928,91022,91094,91204,91310,91383,91474,91576,91679,91774,91881,91986,92108,92230,92356,92415,92473,92597,92721,92849,92967,93085,93207,93293,93390,93524,93658,93738,93876,94008,94140,94276,94351,94427,94530,94604,94717,94798,94855,94916,94975,95035,95093,95154,95212,95262,95311,95378,95437,95496,95545,95616,95700,95770,95841,95921,95990,96053,96121,96187,96255,96320,96386,96463,96541,96647,96753,96849,96978,97067,97194,97260,97330,97416,97482,97565,97639,97737,97833,97929,98027,98136,98231,98320,98382,98442,98507,98564,98645,98699,98756,98853,98963,99024,99139,99260,99355,99447,99540,99596,99655,99704,99796,99845,99899,99953,100007,100061,100115,100170,100280,100390,100498,100608,100718,100828,100938,101046,101152,101256,101360,101464,101559,101654,101747,101840,101944,102050,102154,102258,102351,102444,102537,102630,102738,102844,102950,103056,103153,103248,103343,103438,103544,103650,103756,103862,103960,104055,104151,104248,104313,104417,104475,104539,104600,104662,104722,104787,104849,104917,104975,105038,105101,105168,105243,105316,105382,105434,105487,105539,105596,105680,105775,105860,105941,106021,106098,106177,106254,106328,106402,106473,106553,106625,106700,106765,106826,106886,106961,107035,107108,107178,107250,107320,107393,107457,107527,107573,107642,107694,107779,107862,107919,107985,108052,108118,108199,108274,108330,108383,108444,108502,108552,108601,108650,108699,108761,108813,108858,108939,108990,109044,109097,109151,109202,109251,109317,109368,109429,109490,109552,109602,109643,109720,109779,109838,109897,109958,110014,110070,110137,110198,110263,110318,110383,110452,110520,110598,110667,110727,110798,110872,110937,111009,111079,111146,111230,111299,111366,111436,111499,111566,111634,111717,111796,111886,111963,112031,112098,112176,112233,112290,112358,112424,112480,112540,112599,112653,112703,112753,112801,112863,112914,112987,113067,113147,113211,113278,113349,113407,113468,113534,113593,113660,113720,113780,113843,113911,113972,114039,114117,114187,114236,114293,114362,114423,114511,114599,114687,114775,114862,114949,115036,115123,115181,115255,115325,115381,115452,115517,115579,115654,115727,115817,115883,115949,116010,116074,116136,116194,116265,116348,116407,116478,116544,116609,116670,116729,116800,116866,116931,117014,117090,117165,117246,117306,117375,117445,117514,117569,117625,117681,117742,117800,117856,117915,117969,118024,118086,118143,118237,118306,118407,118458,118528,118591,118647,118705,118764,118818,118904,118988,119058,119127,119197,119312,119433,119500,119567,119642,119709,119768,119822,119876,119930,119983,120035,120109,123164,123304,127017,127067,127117,127206,127262,127320,127382,127437,127495,127619,127683,127742,127804,127870,127936,128227,128373,128418,128461,129561,129608,129653,129704,129755,129806,129857,130391,130950,131012,131192,131264,131389,131443,131498,131556,131611,131670,131726,131795,131864,131933,132003,132066,132129,132192,132255,132320,132385,132450,132515,132578,132642,132706,132770,132821,132899,132977,133048,133120,133193,133265,133404,133470,133538,133606,133672,133739,133813,133876,133933,133993,134058,134125,134190,134247,134308,134366,134470,134580,134689,134793,134871,134936,135003,135069,135139,135186,135238,135288,135345,136304,138449,143316,143500,143678,143916,144105,144274,144990,145105,145190,147148,150516,150581,150670,150827,150984,151652,151806,151977,159156,159252,159342,159438,159528,159694,159817,159940,160110,160216,160331,160446,160548,160654,160771,160886,166110,166283,166451,166599,166758,166913,167086,167203,167320,167488,167600,167714,167886,168062,168220,168353,168465,168611,168763,168895,169038,169160,170507,170643,170739,170875,170970,171137,171230,171322,171509,171665,171843,172007,172189,172506,172688,172870,173060,173292,173482,173659,173821,173978,174088,174271,174408,174612,174796,174980,175140,175298,175482,175709,175912,176083,176303,176525,176680,176880,177064,177167,177357,177498,177663,177834,178034,178238,178440,178605,178810,179009,179208,179405,179496,179645,179795,179879,180028,180173,180325,180466,180632,180793,181066,181367,181533,181688,181790,182387,182551,182737,186780,186905,190170,190442,190720,190965,191027,191312,194306,194762,195271,206047,206561,206998,207432,207875,211857,211978,212077,212482,212579,212696,212783,212906,213007,213413,213512,213631,213724,213831,214174,214281,214526,214647,215056,215304,215404,215509,215628,216137,216284,216403,216654,216787,217202,217456,217568,222918,223043,223451,223572,223800,223921,224054,224201,244923,245415,265886,266310,287077,287571,308087,308513,313354,318771,322862,328293,333035,338412,342396,346388,351779,352326,352759,353515,353745,353988,355155,356084,404229,404813,405286,406716,407460,408653,409707,410185,410478,410861,412376,413141,414284,414725,415166,415762,416036,416447,417463,417641,418394,418531,418622,420816,421082,421404,421614,421723,421842,422026,423144,423614,424365,426948,427043,429148,429376,429632,429891,430467,430821,430943,431082,431374,431634,432562,432848,433251,433653,433996,434208,434409,434622,434911,435196,445871,445958,446043,446142,451075,451181,451304,451436,451559,451689,451813,451946,452077,452202,452319,452439,452571,452699,452813,452931,453044,453165,453353,453540,453721,453904,454088,454253,454435,454555,454675,454783,454893,455005,455113,455223,455388,455554,455706,455871,455972,456092,456263,456424,456587,456748,456915,457034,457151,457331,457513,457694,457877,458032,458177,458299,458434,458597,458790,458916,459068,459210,459380,459536,459708,459999,466666,466758,466931,467093,467188,467357,467451,467540,467783,467872,468165,468581,469001,469422,469848,470265,470681,471098,471516,471930,472400,472873,473345,473756,474227,474699,474889,475095,475201,475309,475415,475527,475641,475753,475867,475983,476097,476205,476315,476423,476685,477064,477468,477615,477723,477833,477941,478055,478464,478878,478994,479412,479653,480083,480518,480928,481350,481760,481882,482291,482707,482829,483047,483231,485935,486279,486359,486715,486865,487009,487085,487197,487287,487549,487814,487922,488074,488182,488258,488370,488460,488562,488670,488778,488878,488986,489071,489237,489341,489469,489556,489723,489801,489915,490007,490271,490538,490648,490801,490911,490995,491384,491482,491590,491684,491814,491922,492044,492180,492288,492408,492542,492664,492792,492934,493060,493200,493326,493444,493576,493674,493784,494084,494196,494314,494778,494894,495197,495323,495419,495820,495930,496054,496192,496302,496424,496736,496860,496990,497466,497594,497909,498047,498209,498425,498581,498785,499941,500025,500129,500332,500521,500722,500915,501120,501433,501645,501811,501927,502173,502389,502702,503128,503590,503827,503979,504239,504383,504525,507757,507871,507991,508107,508201,508522,508621,508739,508840,509119,509404,509683,509965,510218,510477,510730,510986,511410,511486,514736,516091,516535,518389,518964,519172,520182,520562,520728,520869,525889,526315,526427,526562,526715,526912,527083,527266,527441,527628,527900,528058,528142,528246,528733,529289,529447,529666,529897,530120,530355,530577,530843,530981,531580,531694,531832,531944,532068,532639,533134,533680,533825,533918,534010,535937,536507,536805,536994,537200,537393,537603,538487,538632,539024,539182,539399,539660,547887,548762,549382,549579,550527,551292,551415,552188,552409,552609,554586,554686,554776,555462,556215,556980,557743,558518,559731,559896,561509,561830,562893,563103,563273,563843,564738,565371,565537,567023,567639,567875,568096,569054,569319,569584,569831,570245,570481,571766,572215,572402,572651,572893,573069,573310,573543,573768,574363,574838,575362,575623,576974,577449,578675,579145,580193,580645,580889,581346,582591,583074,583224,583779,584231,584631,584784,584929,585072,585142,585570,585858,586362,586871,586987,587889,588011,588123,588300,588566,588836,589102,589370,589626,589886,590142,590400,590652,590908,591160,591414,591646,591882,592134,592390,592642,592896,593128,593362,593474,594126,594581,594705,595797,596612,596808,597132,597521,597873,598114,598328,598627,598819,599134,599341,599687,599987,600388,600607,601020,601257,601627,602351,602706,602975,603115,603369,603513,603790,604782,605191,605823,606169,606537,607611,607974,608374,609882,610467,610785,613320,613514,613732,613958,614170,614369,614576,615780,616075,616632,617022,617654,618131,618376,618863,619109,620305,620702,621708,621930,622353,622544,622923,623011,623119,623227,623540,623865,624184,624515,627218,627406,627667,627916,630500,630692,630957,631210,631742,632150,632349,632933,633168,633292,633704,633918,634320,634423,634553,634728,634980,635176,635316,635510,636521,637590,637878,638008,638785,639442,639588,640294,640532,642072,642222,642639,642804,643490,643960,644156,644247,644331,644475,644709,644876,645804,646090,646250,646865,647024,647352,647579,648091,648453,648532,648871,648976,649341,649712,650073,651947,652576,653652,654076,654329,654481,655529,656266,656469,656715,656962,657180,657422,657743,658007,658312,658535,658846,659035,659750,660019,660513,660739,661179,661338,661622,662367,662732,663037,663195,663433,664752,665150,665378,665598,665740,667030,667136,667266,667404,667528,667816,667985,668085,668370,668484,669367,670122,670561,670685,670931,671124,671258,671449,672228,672446,672737,673016,673333,673555,673850,674133,674237,674578,675394,675710,676271,676777,676982,677768,678173,678834,679023,679574,680140,680260,680662,681196,802974,803067,803130,803212,803305,803398,803485,803583,803674,803765,803853,803937,804033,804133,804239,804342,804443,804547,804653,804752,804858,804960,805067,805176,805287,805418,805538,805654,805772,805871,805978,806094,806213,806341,806430,806525,806602,806691,806782,806875,806949,807046,807141,807239,807338,807442,807538,807640,807743,807843,807946,808031,808132,808230,808320,808415,808502,808608,808710,808804,808895,808989,809065,809157,809246,809349,809460,809543,809629,809724,809821,809917,810005,810106,810207,810310,810416,810514,810611,810706,810804,810907,811007,811110,811215,811333,811449,811544,811637,811722,811818,811912,812004,812087,812191,812296,812396,812497,812602,812702,812803,812902,813004,813098,813205,813307,813410,813503,813599,813701,813804,813900,814002,814105,814202,814305,814403,814507,814612,814709,814817,814931,815046,815154,815268,815383,815485,815590,815698,815808,815924,816041,816136,816233,816332,816437,816543,816642,816747,816853,816953,817059,817160,817267,817386,817485,817590,817692,817794,817894,817997,818092,818196,818281,818385,818489,818587,818691,818797,818895,819000,819098,819211,819305,819394,819483,819566,819657,819740,819838,819928,820024,820113,820207,820295,820391,820476,820584,820685,820786,820884,820990,821081,821180,821277,821375,821471,821564,821674,821772,821867,821977,822069,822169,822268,822355,822459,822564,822663,822770,822877,822976,823085,823177,823288,823399,823510,823614,823729,823845,823972,824092,824187,824282,824379,824478,824570,824669,824761,824860,824946,825040,825143,825239,825342,825438,825541,825638,825736,825839,825932,826022,826123,826206,826297,826382,826474,826577,826672,826768,826861,826955,827034,827141,827232,827331,827424,827527,827631,827732,827833,827937,828031,828135,828239,828352,828458,828564,828672,828789,828890,828998,829098,829201,829306,829413,829509,829588,829678,829762,829854,829927,830019,830108,830200,830285,830382,830475,830570,830669,830766,830857,830948,831040,831135,831242,831350,831452,831549,831646,831739,831826,831910,832007,832104,832197,832284,832375,832474,832573,832668,832757,832838,832937,833041,833138,833243,833340,833424,833523,833627,833724,833829,833926,834024,834125,834231,834330,834437,834536,834635,834726,834815,834904,834986,835079,835170,835281,835382,835482,835594,835707,835805,835913,836007,836107,836196,836288,836399,836509,836604,836720,836846,836972,837091,837219,837344,837469,837587,837714,837823,837932,838045,838168,838291,838407,838532,838629,838737,838859,838975,839091,839200,839288,839389,839478,839579,839666,839754,839851,839943,840049,840149,840225,840302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\53ec144f1612c59cfdaa42b4b0883eab\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1874", "startColumns": "4", "startOffsets": "126019", "endColumns": "65", "endOffsets": "126080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9e22fcbbd83ad381ad62f98cd9fdd486\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1959", "startColumns": "4", "startOffsets": "130596", "endColumns": "49", "endOffsets": "130641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b78d207cda22ac4214cb8630687fc1a6\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "94,9747", "startColumns": "4,4", "startOffsets": "5016,681201", "endLines": "94,9749", "endColumns": "60,12", "endOffsets": "5072,681341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2410", "startColumns": "4", "startOffsets": "169165", "endColumns": "57", "endOffsets": "169218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5aea9c81c7615102d7b5640b77daf851\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,2032,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,9765,9772,9779,9788,9797,9805", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57502,57592,57684,57733,57792,57871,57936,58002,58065,58126,58183,58237,58327,58416,58504,58560,58631,58709,58758,58828,58905,58991,59079,59172,121545,121600,121667,121733,121794,121879,121957,122022,122089,122147,122222,122283,122342,122417,122478,122538,122594,122645,122706,122770,122831,122890,122963,135420,188393,188470,188543,188608,188734,188796,188875,188924,189110,189179,189256,189323,682221,682580,683001,683547,684091,684508", "endLines": "959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,2032,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,9771,9778,9787,9796,9804,9810", "endColumns": "89,91,48,58,78,64,65,62,60,56,53,89,88,87,55,70,77,48,69,76,85,87,92,81,54,66,65,60,84,77,64,66,57,74,60,58,74,60,59,55,50,60,63,60,58,72,68,67,76,72,64,125,61,78,48,185,68,76,66,74,12,12,12,12,12,12", "endOffsets": "57587,57679,57728,57787,57866,57931,57997,58060,58121,58178,58232,58322,58411,58499,58555,58626,58704,58753,58823,58900,58986,59074,59167,59249,121595,121662,121728,121789,121874,121952,122017,122084,122142,122217,122278,122337,122412,122473,122533,122589,122640,122701,122765,122826,122885,122958,123027,135483,188465,188538,188603,188729,188791,188870,188919,189105,189174,189251,189318,189393,682575,682996,683542,684086,684503,684879"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2217,2218,2219,2220,2221,2504", "startColumns": "4,4,4,4,4,4", "startOffsets": "150989,151071,151175,151284,151404,181835", "endColumns": "81,103,108,119,99,68", "endOffsets": "151066,151170,151279,151399,151499,181899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,208,215,513,557,558,570,571,572,573,574,575,576,579,580,581,582,584,585,586,587,588,589,590,591,641,642,643,644,645,646,647,648,649,650,862,863,864,865,866,867,868,869,870,871,872,873,930,931,932,933,934,935,936,937,945,946,947,948,949,950,951,952,953,954,957,958,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1126,1127,1131,1132,1133,1134,1135,1136,1137,1795,1796,1797,1798,1799,1800,1801,1802,1863,1864,1865,1866,1876,1909,1910,1919,1953,1962,1963,1966,1967,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2510,2604,2605,2606,2607,2608,2630,2638,2639,2643,2647,2658,2663,2692,2699,2703,2707,2712,2716,2720,2724,2728,2732,2736,2742,2746,2752,2756,2762,2766,2771,2775,2778,2782,2788,2792,2798,2802,2808,2811,2815,2819,2823,2827,2831,2832,2833,2834,2837,2840,2843,2846,2850,2851,2852,2853,2894,2897,2899,2901,2903,2908,2909,2913,2919,2923,2924,2926,2938,2939,2943,2949,2953,3030,3031,3035,3062,3066,3067,3071,4873,5045,5071,5242,5268,5299,5307,5313,5329,5351,5356,5361,5371,5380,5389,5393,5400,5419,5426,5427,5436,5439,5442,5446,5450,5454,5457,5458,5463,5468,5478,5483,5490,5496,5497,5500,5504,5509,5511,5513,5516,5519,5521,5525,5528,5535,5538,5541,5545,5547,5551,5553,5555,5557,5561,5569,5577,5589,5595,5604,5607,5618,5621,5622,5627,5628,6133,6202,6276,6277,6287,6296,6301,6303,6307,6310,6313,6316,6319,6322,6325,6328,6332,6335,6338,6341,6345,6348,6352,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6512,6513,6514,6515,6516,6517,6518,6519,6520,6522,6524,6525,6526,6527,6528,6529,6530,6531,6533,6534,6536,6537,6539,6541,6542,6544,6545,6546,6547,6548,6549,6551,6552,6553,6554,6555,6840,6842,6844,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6860,6861,6862,6863,6864,6865,6866,6868,6872,7044,7045,7046,7047,7048,7049,7053,7054,7055,7596,7598,7600,7602,7604,7606,7607,7608,7609,7611,7613,7615,7616,7617,7618,7619,7620,7621,7622,7623,7624,7625,7626,7629,7630,7631,7632,7634,7636,7637,7639,7640,7642,7644,7646,7647,7648,7649,7650,7651,7652,7653,7654,7655,7656,7657,7659,7660,7661,7662,7664,7665,7666,7667,7668,7670,7672,7674,7676,7677,7678,7679,7680,7681,7682,7683,7684,7685,7686,7687,7688,7689,7690", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6094,10548,10853,26690,28507,28562,29337,29401,29471,29532,29607,29683,29760,29998,30083,30165,30241,30359,30436,30514,30620,30726,30805,30885,30942,34474,34548,34623,34688,34754,34814,34875,34947,35020,35087,50646,50705,50764,50823,50882,50941,50995,51049,51102,51156,51210,51264,55656,55730,55809,55882,55956,56027,56099,56171,56594,56651,56709,56782,56856,56930,57005,57077,57150,57220,57381,57441,59296,59365,59434,59504,59578,59654,59718,59795,59871,59948,60013,60082,60159,60234,60303,60371,60448,60514,60575,60672,60737,60806,60905,60976,61035,61093,61150,61209,61273,61344,61416,61488,61560,61632,61699,61767,61835,61894,61957,62021,62111,62202,62262,62328,62395,62461,62531,62595,62648,62715,62776,62843,62956,63014,63077,63142,63207,63282,63355,63427,63471,63518,63564,63613,63674,63735,63796,63858,63922,63986,64050,64115,64178,64238,64299,64365,64424,64484,64546,64617,64677,68675,68761,69011,69101,69188,69276,69358,69441,69531,121092,121144,121202,121247,121313,121377,121434,121491,125432,125489,125537,125586,126136,128051,128098,128531,130316,130763,130827,131017,131077,136309,136383,136453,136531,136585,136655,136740,136788,136834,136895,136958,137024,137088,137159,137222,137287,137351,137412,137473,137525,137598,137672,137741,137816,137890,137964,138105,182181,189456,189534,189624,189712,189808,191317,191899,191988,192235,192516,193182,193467,195276,195753,195975,196197,196473,196700,196930,197160,197390,197620,197847,198266,198492,198917,199147,199575,199794,200077,200285,200416,200643,201069,201294,201721,201942,202367,202487,202763,203064,203388,203679,203993,204130,204261,204366,204608,204775,204979,205187,205458,205570,205682,205787,207880,208094,208240,208380,208466,208814,208902,209148,209566,209815,209897,209995,210652,210752,211004,211428,211683,217573,217662,217899,219923,220165,220267,220520,356089,366770,368286,378981,380509,382266,382892,383312,384573,385838,386094,386330,386877,387371,387976,388174,388754,390122,390497,390615,391153,391310,391506,391779,392035,392205,392346,392410,392775,393142,393818,394082,394420,394773,394867,395053,395359,395621,395746,395873,396112,396323,396442,396635,396812,397267,397448,397570,397829,397942,398129,398231,398338,398467,398742,399250,399746,400623,400917,401487,401636,402368,402540,402624,402960,403052,435201,440432,446147,446209,446787,447371,447713,447826,448055,448215,448367,448538,448704,448873,449040,449203,449446,449616,449789,449960,450234,450433,450638,460004,460088,460184,460280,460378,460478,460580,460682,460784,460886,460988,461088,461184,461296,461425,461548,461679,461810,461908,462022,462116,462256,462390,462486,462598,462698,462814,462910,463022,463122,463262,463398,463562,463692,463850,464000,464141,464285,464420,464532,464682,464810,464938,465074,465206,465336,465466,465578,483236,483382,483526,483664,483730,483820,483896,484000,484090,484192,484300,484408,484508,484588,484680,484778,484888,484940,485018,485124,485216,485320,485430,485552,485715,498875,498955,499055,499145,499255,499345,499586,499680,499786,539665,539765,539877,539991,540107,540223,540317,540431,540543,540645,540765,540887,540969,541073,541193,541319,541417,541511,541599,541711,541827,541949,542061,542236,542352,542438,542530,542642,542766,542833,542959,543027,543155,543299,543427,543496,543591,543706,543819,543918,544027,544138,544249,544350,544455,544555,544685,544776,544899,544993,545105,545191,545295,545391,545479,545597,545701,545805,545931,546019,546127,546227,546317,546427,546511,546613,546697,546751,546815,546921,547007,547117,547201", "endLines": "120,208,215,513,557,558,570,571,572,573,574,575,576,579,580,581,582,584,585,586,587,588,589,590,591,641,642,643,644,645,646,647,648,649,650,862,863,864,865,866,867,868,869,870,871,872,873,930,931,932,933,934,935,936,937,945,946,947,948,949,950,951,952,953,954,957,958,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1126,1127,1131,1132,1133,1134,1135,1136,1137,1795,1796,1797,1798,1799,1800,1801,1802,1863,1864,1865,1866,1876,1909,1910,1919,1953,1962,1963,1966,1967,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2510,2604,2605,2606,2607,2608,2637,2638,2642,2646,2650,2662,2668,2698,2702,2706,2711,2715,2719,2723,2727,2731,2735,2741,2745,2751,2755,2761,2765,2770,2774,2777,2781,2787,2791,2797,2801,2807,2810,2814,2818,2822,2826,2830,2831,2832,2833,2836,2839,2842,2845,2849,2850,2851,2852,2853,2896,2898,2900,2902,2907,2908,2912,2918,2922,2923,2925,2937,2938,2942,2948,2952,2953,3030,3034,3061,3065,3066,3070,3098,5044,5070,5241,5267,5298,5306,5312,5328,5350,5355,5360,5370,5379,5388,5392,5399,5418,5425,5426,5435,5438,5441,5445,5449,5453,5456,5457,5462,5467,5477,5482,5489,5495,5496,5499,5503,5508,5510,5512,5515,5518,5520,5524,5527,5534,5537,5540,5544,5546,5550,5552,5554,5556,5560,5568,5576,5588,5594,5603,5606,5617,5620,5621,5626,5627,5632,6201,6271,6276,6286,6295,6296,6302,6306,6309,6312,6315,6318,6321,6324,6327,6331,6334,6337,6340,6344,6347,6351,6355,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6512,6513,6514,6515,6516,6517,6518,6519,6521,6523,6524,6525,6526,6527,6528,6529,6530,6532,6533,6535,6536,6538,6540,6541,6543,6544,6545,6546,6547,6548,6550,6551,6552,6553,6554,6555,6841,6843,6845,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6859,6860,6861,6862,6863,6864,6865,6867,6871,6875,7044,7045,7046,7047,7048,7052,7053,7054,7055,7597,7599,7601,7603,7605,7606,7607,7608,7610,7612,7614,7615,7616,7617,7618,7619,7620,7621,7622,7623,7624,7625,7628,7629,7630,7631,7633,7635,7636,7638,7639,7641,7643,7645,7646,7647,7648,7649,7650,7651,7652,7653,7654,7655,7656,7658,7659,7660,7661,7663,7664,7665,7666,7667,7669,7671,7673,7675,7676,7677,7678,7679,7680,7681,7682,7683,7684,7685,7686,7687,7688,7689,7690", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6144,10588,10897,26726,28557,28619,29396,29466,29527,29602,29678,29755,29833,30078,30160,30236,30312,30431,30509,30615,30721,30800,30880,30937,30995,34543,34618,34683,34749,34809,34870,34942,35015,35082,35150,50700,50759,50818,50877,50936,50990,51044,51097,51151,51205,51259,51313,55725,55804,55877,55951,56022,56094,56166,56239,56646,56704,56777,56851,56925,57000,57072,57145,57215,57286,57436,57497,59360,59429,59499,59573,59649,59713,59790,59866,59943,60008,60077,60154,60229,60298,60366,60443,60509,60570,60667,60732,60801,60900,60971,61030,61088,61145,61204,61268,61339,61411,61483,61555,61627,61694,61762,61830,61889,61952,62016,62106,62197,62257,62323,62390,62456,62526,62590,62643,62710,62771,62838,62951,63009,63072,63137,63202,63277,63350,63422,63466,63513,63559,63608,63669,63730,63791,63853,63917,63981,64045,64110,64173,64233,64294,64360,64419,64479,64541,64612,64672,64740,68756,68843,69096,69183,69271,69353,69436,69526,69617,121139,121197,121242,121308,121372,121429,121486,121540,125484,125532,125581,125632,126165,128093,128142,128572,130343,130822,130884,131072,131129,136378,136448,136526,136580,136650,136735,136783,136829,136890,136953,137019,137083,137154,137217,137282,137346,137407,137468,137520,137593,137667,137736,137811,137885,137959,138100,138170,182229,189529,189619,189707,189803,189893,191894,191983,192230,192511,192763,193462,193855,195748,195970,196192,196468,196695,196925,197155,197385,197615,197842,198261,198487,198912,199142,199570,199789,200072,200280,200411,200638,201064,201289,201716,201937,202362,202482,202758,203059,203383,203674,203988,204125,204256,204361,204603,204770,204974,205182,205453,205565,205677,205782,205899,208089,208235,208375,208461,208809,208897,209143,209561,209810,209892,209990,210647,210747,210999,211423,211678,211772,217657,217894,219918,220160,220262,220515,222671,366765,368281,378976,380504,382261,382887,383307,384568,385833,386089,386325,386872,387366,387971,388169,388749,390117,390492,390610,391148,391305,391501,391774,392030,392200,392341,392405,392770,393137,393813,394077,394415,394768,394862,395048,395354,395616,395741,395868,396107,396318,396437,396630,396807,397262,397443,397565,397824,397937,398124,398226,398333,398462,398737,399245,399741,400618,400912,401482,401631,402363,402535,402619,402955,403047,403325,440427,445798,446204,446782,447366,447457,447821,448050,448210,448362,448533,448699,448868,449035,449198,449441,449611,449784,449955,450229,450428,450633,450963,460083,460179,460275,460373,460473,460575,460677,460779,460881,460983,461083,461179,461291,461420,461543,461674,461805,461903,462017,462111,462251,462385,462481,462593,462693,462809,462905,463017,463117,463257,463393,463557,463687,463845,463995,464136,464280,464415,464527,464677,464805,464933,465069,465201,465331,465461,465573,465713,483377,483521,483659,483725,483815,483891,483995,484085,484187,484295,484403,484503,484583,484675,484773,484883,484935,485013,485119,485211,485315,485425,485547,485710,485867,498950,499050,499140,499250,499340,499581,499675,499781,499873,539760,539872,539986,540102,540218,540312,540426,540538,540640,540760,540882,540964,541068,541188,541314,541412,541506,541594,541706,541822,541944,542056,542231,542347,542433,542525,542637,542761,542828,542954,543022,543150,543294,543422,543491,543586,543701,543814,543913,544022,544133,544244,544345,544450,544550,544680,544771,544894,544988,545100,545186,545290,545386,545474,545592,545696,545800,545926,546014,546122,546222,546312,546422,546506,546608,546692,546746,546810,546916,547002,547112,547196,547316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4ee0524a935fe4c924df4ba2e4e24000\\transformed\\leakcanary-android-core-2.14\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "562,563,564,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1880,1881,1882,1883,1884,1885,1886,1887,2033,2037,2041,2227,2228,2229,2230,2231,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,9750,9754,9761", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28823,28885,28944,35155,35218,35275,35337,35393,35474,35526,35577,35631,35685,35744,35809,35874,35931,35991,36046,36111,36173,36235,36297,36361,36424,36488,36545,36605,36672,36733,36795,36857,36921,36978,37041,37104,37162,37213,37264,37320,37372,37425,37485,69845,69907,69973,70040,70104,70163,70222,70275,70335,70390,70441,70507,70573,70644,70713,70772,126345,126415,126484,126551,126631,126700,126771,126842,135488,135650,135818,151982,152069,152148,152225,152293,153020,153092,153172,153249,153389,153458,153512,153574,153680,153749,153813,153889,153968,154061,154147,154254,154372,154452,154529,154625,154734,154852,154964,155073,155192,155289,155365,155439,155510,155585,155707,155789,155871,155963,156033,156096,156179,156268,156354,156428,156510,156605,156727,156838,156932,157046,157152,157269,157360,157500,157587,157731,157843,157944,158043,158106,158167,158256,158349,158441,158548,158652,158803,681346,681600,681985", "endLines": "562,563,564,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1880,1881,1882,1883,1884,1885,1886,1887,2036,2040,2044,2227,2228,2229,2230,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,9753,9760,9764", "endColumns": "61,58,71,62,56,61,55,80,51,50,53,53,58,64,64,56,59,54,64,61,61,61,63,62,63,56,59,66,60,61,61,63,56,62,62,57,50,50,55,51,52,59,67,61,65,66,63,58,58,52,59,54,50,65,65,70,68,58,64,69,68,66,79,68,70,70,60,12,12,12,86,78,76,67,12,71,79,76,139,68,53,61,105,68,63,75,78,92,85,106,117,79,76,95,108,117,111,108,118,96,75,73,70,74,121,81,81,91,69,62,82,88,85,73,81,94,121,110,93,113,105,116,90,139,86,143,111,100,98,62,60,88,92,91,106,103,150,170,10,10,10", "endOffsets": "28880,28939,29011,35213,35270,35332,35388,35469,35521,35572,35626,35680,35739,35804,35869,35926,35986,36041,36106,36168,36230,36292,36356,36419,36483,36540,36600,36667,36728,36790,36852,36916,36973,37036,37099,37157,37208,37259,37315,37367,37420,37480,37548,69902,69968,70035,70099,70158,70217,70270,70330,70385,70436,70502,70568,70639,70708,70767,70832,126410,126479,126546,126626,126695,126766,126837,126898,135645,135813,135984,152064,152143,152220,152288,153015,153087,153167,153244,153384,153453,153507,153569,153675,153744,153808,153884,153963,154056,154142,154249,154367,154447,154524,154620,154729,154847,154959,155068,155187,155284,155360,155434,155505,155580,155702,155784,155866,155958,156028,156091,156174,156263,156349,156423,156505,156600,156722,156833,156927,157041,157147,157264,157355,157495,157582,157726,157838,157939,158038,158101,158162,158251,158344,158436,158543,158647,158798,158969,681595,681980,682216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\887c9c1e0bbe1248378b1eec22ea3bb5\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1911,1957", "startColumns": "4,4", "startOffsets": "128147,130482", "endColumns": "41,59", "endOffsets": "128184,130537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1970,2193", "startColumns": "4,4", "startOffsets": "131269,148667", "endColumns": "67,166", "endOffsets": "131332,148829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2130,2580,2581", "startColumns": "4,4,4", "startOffsets": "142982,187660,187716", "endColumns": "45,55,54", "endOffsets": "143023,187711,187766"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\build\\generated\\res\\injectCrashlyticsMappingFileIdDebug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2184", "startColumns": "4", "startOffsets": "147451", "endColumns": "175", "endOffsets": "147622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1867,1869,1870,1875,1877,1961,2180,2181,2205,2206,2207,2224,2225,2495,2496,2507,2508,2516,2566,2567,2568,2569,2572,2573,2575,6014,6031,6034", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125637,125762,125820,126085,126170,130710,147223,147288,150054,150120,150221,151811,151863,180798,180860,182027,182077,182742,186910,186964,187010,187052,187201,187248,187338,427366,428409,428520", "endLines": "1867,1869,1870,1875,1877,1961,2180,2181,2205,2206,2207,2224,2225,2495,2496,2507,2508,2516,2566,2567,2568,2569,2572,2573,2575,6016,6033,6038", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "125706,125815,125870,126131,126220,130758,147283,147337,150115,150216,150274,151858,151918,180855,180909,182072,182126,182783,186959,187005,187047,187087,187243,187279,187423,427473,428515,428772"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\search\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2132,2179", "startColumns": "4,4", "startOffsets": "143066,147153", "endColumns": "123,69", "endOffsets": "143185,147218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\7e3f2c5e9beccbf3f25b116a07f38db6\\transformed\\leakcanary-object-watcher-android-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "566", "startColumns": "4", "startOffsets": "29078", "endColumns": "61", "endOffsets": "29135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6100cf79e00b0bf6c9c6362128e7edd6\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1900", "startColumns": "4", "startOffsets": "127500", "endColumns": "52", "endOffsets": "127548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\48ffa0e1b44e6c5d14d02c946f3f174d\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,4,10,11,19,27,33,91,92,93,95,99,100,101,104,112,122,150,151,156,157,162,167,168,169,174,175,180,181,186,187,188,194,195,196,201,206,224,225,229,230,231,232,235,236,239,242,243,244,245,246,249,252,253,254,255,260,263,266,267,268,273,274,275,278,281,282,285,288,291,294,295,296,299,302,303,308,309,314,317,320,321,322,323,324,325,326,327,328,329,416,417,418,419,424,430,431,432,435,469,476,516,517,528,534,537,541,542,543,544,553,1893", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,263,498,559,850,1306,1613,4857,4909,4970,5077,5210,5262,5312,5473,5782,6205,8147,8206,8403,8460,8655,8837,8891,8948,9140,9198,9394,9450,9644,9701,9752,9974,10026,10081,10271,10447,11346,11402,11562,11623,11683,11753,11886,11954,12083,12209,12271,12336,12404,12471,12594,12719,12786,12851,12916,13097,13218,13339,13405,13472,13682,13751,13817,13942,14068,14135,14261,14388,14513,14640,14696,14761,14887,15010,15075,15283,15350,15530,15650,15770,15835,15897,15959,16021,16080,16140,16201,16262,16321,21481,21532,21581,21629,21916,22146,22193,22253,22359,24013,24360,26840,26892,27328,27566,27744,27883,27929,27984,28029,28370,127122", "endLines": "2,8,10,18,19,27,33,91,92,93,98,99,100,101,111,119,122,150,155,156,161,166,167,168,173,174,179,180,185,186,187,193,194,195,200,205,206,224,228,229,230,231,234,235,238,241,242,243,244,245,248,251,252,253,254,259,262,265,266,267,272,273,274,277,280,281,284,287,290,293,294,295,298,301,302,307,308,313,316,319,320,321,322,323,324,325,326,327,328,340,416,417,418,419,429,430,431,434,439,469,476,516,525,533,534,540,541,542,543,552,556,1893", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40", "endOffsets": "202,444,554,845,897,1349,1659,4904,4965,5011,5205,5257,5307,5358,5777,6089,6245,8201,8398,8455,8650,8832,8886,8943,9135,9193,9389,9445,9639,9696,9747,9969,10021,10076,10266,10442,10492,11397,11557,11618,11678,11748,11881,11949,12078,12204,12266,12331,12399,12466,12589,12714,12781,12846,12911,13092,13213,13334,13400,13467,13677,13746,13812,13937,14063,14130,14256,14383,14508,14635,14691,14756,14882,15005,15070,15278,15345,15525,15645,15765,15830,15892,15954,16016,16075,16135,16196,16257,16316,16784,21527,21576,21624,21682,22141,22188,22248,22354,22534,24054,24402,26887,27217,27561,27616,27878,27924,27979,28024,28365,28502,127158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d7ab330357aa20624881ed94e7768b51\\transformed\\plumber-android-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "565", "startColumns": "4", "startOffsets": "29016", "endColumns": "61", "endOffsets": "29073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\755b5a3a3bd78573c96197752104b9b1\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "121,124,1077", "startColumns": "4,4,4", "startOffsets": "6149,6313,65523", "endColumns": "55,47,51", "endOffsets": "6200,6356,65570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\02a224bf1e10a1e6b2e19a748804e152\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "26,594,595,596,597,1066,1067,1068,2651,6006,6008,6011", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,31131,31192,31254,31316,64810,64869,64926,192768,427048,427112,427238", "endLines": "26,594,595,596,597,1066,1067,1068,2657,6007,6010,6013", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1301,31187,31249,31311,31375,64864,64921,64975,193177,427107,427233,427361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\1ba516b7f7e560d6a18c60f8777e4347\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1868", "startColumns": "4", "startOffsets": "125711", "endColumns": "50", "endOffsets": "125757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f3e154f6c2797116b6a57a346ec405cd\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1878,1908", "startColumns": "4,4", "startOffsets": "126225,127984", "endColumns": "53,66", "endOffsets": "126274,128046"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "583,938,939,940,955,956,983", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "30317,56244,56291,56338,57291,57336,59254", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "30354,56286,56333,56380,57331,57376,59291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a91968b4dc7cf6f01038e7dd4d63f60d\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "441,1128,1129,1130,1138,1139,1140,1879", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22597,68848,68907,68955,69622,69697,69773,126279", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "22648,68902,68950,69006,69692,69768,69840,126340"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,39,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2192,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2052,-1,-1,-1,-1,2121,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,130,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,67,-1,-1,-1,-1,69,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2318,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2115,-1,-1,-1,-1,2186,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2077,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2131,2139,2147,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2182,2183,2203,2204,2208,2209,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2497,2503,2509,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2571,2574,2576,2577,2578,2579,2582,2583,2584,2585,2586,2587,2588,2589,2590,2603", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "138258,138454,138505,138566,138631,138718,138779,138840,138912,138969,139040,139130,139390,139475,139536,139605,139678,139757,139814,139881,139952,140019,140088,140171,140405,140478,140560,140637,140694,140761,141027,141090,141167,141220,141291,141401,141458,141529,141597,141658,141720,141790,141857,141934,142009,142084,142299,142375,142670,142738,142793,142858,143028,144279,144845,145195,145277,145329,145395,145526,145606,145676,145728,145788,145888,145954,146017,146115,146180,146250,146309,146377,146433,146495,146567,146639,146709,146757,146829,146907,146957,147015,147342,147396,149960,149998,150279,150317,169223,169307,169373,169453,169527,169597,169677,169764,169814,169878,169947,169996,170055,170113,170215,170280,180914,181795,182131,182788,182826,182970,183071,183199,183296,183421,183510,183621,183702,183774,183843,183911,183990,184071,184124,184191,184252,184348,184415,184496,184559,184621,184685,184751,184809,184932,185021,185090,185186,185260,185346,185415,185493,185568,185650,185725,185786,185865,185936,186019,186072,186152,186282,186362,186446,186516,187163,187284,187428,187487,187543,187601,187771,187829,187918,187984,188054,188155,188232,188293,188357,189398", "endColumns": "45,50,60,64,86,60,60,71,56,70,89,259,84,60,68,72,78,56,66,70,66,68,82,233,72,81,76,56,66,265,62,76,52,70,109,56,70,67,60,61,69,66,76,74,74,214,75,294,67,54,64,123,37,54,51,81,51,65,130,79,69,51,59,99,65,62,97,64,69,58,67,55,61,71,71,69,47,71,77,49,57,58,53,54,37,55,37,43,83,65,79,73,69,79,86,49,63,68,48,58,57,101,64,53,78,39,49,37,143,100,127,96,124,88,110,80,71,68,67,78,80,52,66,60,95,66,80,62,61,63,65,57,122,88,68,95,73,85,68,77,74,81,74,60,78,70,82,52,79,129,79,83,69,45,37,53,58,55,57,58,57,88,65,69,100,76,60,63,35,57", "endOffsets": "138299,138500,138561,138626,138713,138774,138835,138907,138964,139035,139125,139385,139470,139531,139600,139673,139752,139809,139876,139947,140014,140083,140166,140400,140473,140555,140632,140689,140756,141022,141085,141162,141215,141286,141396,141453,141524,141592,141653,141715,141785,141852,141929,142004,142079,142294,142370,142665,142733,142788,142853,142977,143061,144329,144892,145272,145324,145390,145521,145601,145671,145723,145783,145883,145949,146012,146110,146175,146245,146304,146372,146428,146490,146562,146634,146704,146752,146824,146902,146952,147010,147069,147391,147446,149993,150049,150312,150356,169302,169368,169448,169522,169592,169672,169759,169809,169873,169942,169991,170050,170108,170210,170275,170329,180988,181830,182176,182821,182965,183066,183194,183291,183416,183505,183616,183697,183769,183838,183906,183985,184066,184119,184186,184247,184343,184410,184491,184554,184616,184680,184746,184804,184927,185016,185085,185181,185255,185341,185410,185488,185563,185645,185720,185781,185860,185931,186014,186067,186147,186277,186357,186441,186511,186557,187196,187333,187482,187538,187596,187655,187824,187913,187979,188049,188150,188227,188288,188352,188388,189451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\e6e7ee247c83f269d48cbfd5490c1df4\\transformed\\transition-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1872,1873,1907,1913,1914,1945,1946,1947,1948,1949,1950,1951,1952", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125932,125972,127941,128232,128287,129897,129942,129996,130052,130104,130156,130205,130266", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "125967,126014,127979,128282,128329,129937,129991,130047,130099,130151,130200,130261,130311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\3e808f1588c4ca551309bafece8a7467\\transformed\\camera-view-1.5.0-beta01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "211,443", "startColumns": "4,4", "startOffsets": "10701,22699", "endLines": "214,450", "endColumns": "11,11", "endOffsets": "10848,23001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2001,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,6017,6028", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "133270,160891,160979,161065,161146,161230,161299,161364,161447,161553,161639,161759,161813,161882,161943,162012,162101,162196,162270,162367,162460,162558,162707,162798,162886,162982,163080,163144,163212,163299,163393,163460,163532,163604,163705,163814,163890,163959,164007,164073,164137,164211,164286,164357,164414,164471,164529,164601,164668,164718,164787,164841,164912,164983,165053,165122,165180,165256,165327,165401,165487,165537,165642,165739,165809,165874,165951,427478,428256", "endLines": "2001,2318,2319,2320,2321,2322,2323,2324,2325,2326,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,6027,6030", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,74,70,56,56,57,71,66,49,68,53,70,70,69,68,57,75,70,73,85,49,104,96,69,64,76,81,12,12", "endOffsets": "133338,160974,161060,161141,161225,161294,161359,161442,161548,161634,161754,161808,161877,161938,162007,162096,162191,162265,162362,162455,162553,162702,162793,162881,162977,163075,163139,163207,163294,163388,163455,163527,163599,163700,163809,163885,163954,164002,164068,164132,164206,164281,164352,164409,164466,164524,164596,164663,164713,164782,164836,164907,164978,165048,165117,165175,165251,165322,165396,165482,165532,165637,165734,165804,165869,165946,166028,428251,428404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c95c368326577e60c73af6c9ba10d0f5\\transformed\\leakcanary-object-watcher-android-core-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "567", "startColumns": "4", "startOffsets": "29140", "endColumns": "73", "endOffsets": "29209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "598,599,600,601,602,603,604,605,2185,2186,2187,2188,2189,2190,2191,2192,2194,2195,2196,2197,2198,2199,2200,2201,2202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31380,31470,31550,31640,31730,31810,31891,31971,147627,147732,147913,148038,148145,148325,148448,148564,148834,149022,149127,149308,149433,149608,149756,149819,149881", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "31465,31545,31635,31725,31805,31886,31966,32046,147727,147908,148033,148140,148320,148443,148559,148662,149017,149122,149303,149428,149603,149751,149814,149876,149955"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7043", "startColumns": "4", "startOffsets": "498790", "endColumns": "84", "endOffsets": "498870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dfed636760b934f8795b2c5bd3acd456\\transformed\\fragment-1.8.5\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1871,1918,1960", "startColumns": "4,4,4", "startOffsets": "125875,128466,130646", "endColumns": "56,64,63", "endOffsets": "125927,128526,130705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\511d54b94fbbdd77f26be08a0883aa32\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1955", "startColumns": "4", "startOffsets": "130396", "endColumns": "42", "endOffsets": "130434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "941,942,943,944,2505,2506,6297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "56385,56443,56490,56546,181904,181965,447462", "endLines": "941,942,943,944,2505,2506,6300", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "56438,56485,56541,56589,181960,182022,447708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "423,577,578,592,593,928,929,1070,1071,1072,1073,1074,1075,1076,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1888,1889,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1944,2031,2140,2141,2142,2143,2144,2145,2146,2570,6556,6557,6561,6562,6566,7691,7692", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21856,29838,29910,31000,31065,55524,55593,65035,65105,65173,65245,65315,65376,65450,120114,120175,120236,120298,120362,120424,120485,120553,120653,120713,120779,120852,120921,120978,121030,123309,123381,123457,123522,123581,123640,123700,123760,123820,123880,123940,124000,124060,124120,124180,124240,124299,124359,124419,124479,124539,124599,124659,124719,124779,124839,124899,124958,125018,125078,125137,125196,125255,125314,125373,126903,126938,128577,128632,128695,128750,128808,128864,128922,128983,129046,129103,129154,129212,129262,129323,129380,129446,129480,129862,135350,144334,144401,144473,144542,144611,144685,144757,187092,465718,465835,466036,466146,466347,547321,547393", "endLines": "423,577,578,592,593,928,929,1070,1071,1072,1073,1074,1075,1076,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1888,1889,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1944,2031,2140,2141,2142,2143,2144,2145,2146,2570,6556,6560,6561,6565,6566,7691,7692", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "21911,29905,29993,31060,31126,55588,55651,65100,65168,65240,65310,65371,65445,65518,120170,120231,120293,120357,120419,120480,120548,120648,120708,120774,120847,120916,120973,121025,121087,123376,123452,123517,123576,123635,123695,123755,123815,123875,123935,123995,124055,124115,124175,124235,124294,124354,124414,124474,124534,124594,124654,124714,124774,124834,124894,124953,125013,125073,125132,125191,125250,125309,125368,125427,126933,126968,128627,128690,128745,128803,128859,128917,128978,129041,129098,129149,129207,129257,129318,129375,129441,129475,129510,129892,135415,144396,144468,144537,144606,144680,144752,144840,187158,465830,466031,466141,466342,466471,547388,547455"}}]}, {"outputFile": "me.rerere.rikkahub.app-mergeDebugResources-108:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f1b106457389d514166f73b1eda76c20\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1956", "startColumns": "4", "startOffsets": "130439", "endColumns": "42", "endOffsets": "130477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\824ae5c8d440c8e3d32b709ff4854e24\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1958", "startColumns": "4", "startOffsets": "130542", "endColumns": "53", "endOffsets": "130591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b8d754eb5303359053679da12bad27ab\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "559,560,561,569", "startColumns": "4,4,4,4", "startOffsets": "28624,28689,28759,29276", "endColumns": "64,69,63,60", "endOffsets": "28684,28754,28818,29332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\48c9216ee5d6b429524fc837ac071821\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2076", "startColumns": "4", "startOffsets": "138175", "endColumns": "82", "endOffsets": "138253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b26bc0a945bb328544f80db00c932160\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,216,217,218,219,220,221,222,223,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,420,421,422,440,442,451,452,453,454,455,456,457,458,459,460,461,465,466,467,468,470,471,472,473,474,475,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,514,515,526,527,535,536,568,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,1065,1069,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1826,1827,1890,1891,1892,1894,1895,1896,1897,1898,1899,1901,1902,1903,1904,1905,1906,1912,1915,1916,1917,1937,1938,1939,1940,1941,1942,1943,1954,1964,1965,1968,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2045,2078,2133,2134,2135,2136,2137,2138,2148,2149,2150,2178,2210,2213,2214,2215,2216,2222,2223,2226,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2439,2442,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2498,2499,2500,2501,2502,2511,2514,2515,2564,2565,2609,2613,2617,2621,2625,2626,2669,2677,2684,2854,2857,2867,2876,2885,2954,2955,2956,2957,2963,2964,2965,2966,2967,2968,2974,2975,2976,2977,2978,2983,2984,2988,2989,2995,2999,3000,3001,3002,3012,3013,3014,3018,3019,3025,3029,3099,3102,3103,3108,3109,3112,3113,3114,3115,3379,3386,3647,3653,3917,3924,4185,4191,4254,4336,4388,4470,4532,4614,4678,4730,4812,4820,4826,4837,4841,4845,4858,5633,5649,5656,5662,5679,5692,5712,5729,5738,5743,5750,5770,5783,5800,5806,5812,5819,5823,5829,5843,5846,5856,5857,5858,5906,5910,5914,5918,5919,5920,5923,5939,5946,5960,6005,6039,6045,6049,6053,6058,6065,6071,6072,6075,6079,6084,6097,6101,6106,6111,6116,6119,6122,6125,6129,6272,6273,6274,6275,6356,6357,6358,6359,6360,6361,6362,6363,6364,6365,6366,6367,6368,6369,6370,6371,6372,6373,6374,6378,6382,6386,6390,6394,6398,6402,6403,6404,6405,6406,6407,6408,6409,6413,6417,6418,6422,6423,6426,6430,6433,6436,6439,6443,6446,6449,6453,6457,6461,6465,6468,6469,6470,6471,6474,6478,6481,6484,6487,6490,6493,6496,6567,6570,6571,6574,6577,6578,6581,6582,6583,6587,6588,6593,6600,6607,6614,6621,6628,6635,6642,6649,6656,6665,6674,6683,6690,6699,6708,6711,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6730,6735,6740,6743,6744,6745,6746,6747,6755,6763,6764,6772,6776,6784,6792,6800,6808,6816,6817,6825,6833,6834,6837,6876,6878,6883,6885,6890,6894,6898,6899,6900,6901,6905,6909,6910,6914,6915,6916,6917,6918,6919,6920,6921,6922,6923,6924,6928,6929,6930,6931,6935,6936,6937,6938,6942,6946,6947,6951,6952,6953,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6975,6976,6977,6978,6982,6983,6984,6990,6991,6995,6997,6998,7003,7004,7005,7006,7007,7008,7012,7013,7014,7020,7021,7025,7027,7031,7035,7039,7056,7057,7058,7059,7062,7065,7068,7071,7074,7079,7083,7086,7087,7092,7096,7101,7107,7113,7118,7122,7127,7131,7135,7176,7177,7178,7179,7180,7184,7185,7186,7187,7191,7195,7199,7203,7207,7211,7215,7219,7225,7226,7267,7281,7286,7312,7319,7322,7333,7338,7341,7344,7399,7405,7406,7409,7412,7415,7418,7421,7424,7427,7431,7434,7435,7436,7444,7452,7455,7460,7465,7470,7475,7479,7483,7484,7492,7493,7494,7495,7496,7504,7509,7514,7515,7516,7517,7542,7548,7553,7556,7560,7563,7567,7577,7580,7585,7588,7592,7693,7701,7715,7728,7732,7747,7758,7761,7772,7777,7781,7816,7817,7818,7830,7838,7846,7854,7862,7882,7885,7912,7917,7937,7940,7943,7950,7963,7972,7975,7995,8005,8009,8013,8026,8030,8034,8038,8044,8048,8065,8073,8077,8081,8085,8088,8092,8096,8100,8110,8117,8124,8128,8154,8164,8189,8198,8218,8228,8232,8242,8267,8277,8280,8287,8294,8301,8302,8303,8304,8305,8312,8316,8322,8328,8329,8342,8343,8344,8347,8350,8353,8356,8359,8362,8365,8368,8371,8374,8377,8380,8383,8386,8389,8392,8395,8398,8401,8404,8407,8408,8416,8424,8425,8438,8448,8452,8457,8462,8466,8469,8473,8477,8480,8484,8487,8491,8496,8501,8504,8511,8515,8519,8528,8533,8538,8539,8543,8546,8550,8563,8568,8576,8580,8584,8601,8605,8610,8628,8635,8639,8669,8672,8675,8678,8681,8684,8687,8706,8712,8720,8727,8739,8747,8752,8760,8764,8782,8789,8805,8809,8817,8820,8825,8826,8827,8828,8832,8836,8840,8844,8879,8882,8886,8890,8924,8927,8931,8935,8944,8950,8953,8963,8967,8968,8975,8979,8986,8987,8988,8991,8996,9001,9002,9006,9021,9040,9044,9045,9057,9067,9068,9080,9085,9109,9112,9118,9121,9130,9138,9142,9145,9148,9151,9155,9158,9175,9179,9182,9197,9200,9208,9213,9220,9225,9226,9231,9232,9238,9244,9250,9282,9293,9310,9317,9321,9324,9337,9346,9350,9355,9359,9363,9367,9371,9375,9379,9383,9388,9391,9403,9408,9417,9420,9427,9428,9432,9441,9447,9451,9452,9456,9477,9483,9487,9491,9492,9510,9511,9512,9513,9514,9519,9522,9523,9529,9530,9542,9554,9561,9562,9567,9572,9573,9577,9591,9596,9602,9608,9614,9619,9625,9631,9632,9638,9653,9658,9667,9676,9679,9693,9698,9709,9713,9722,9731,9732,9739,13124,13125,13126,13127,13128,13129,13130,13131,13132,13133,13134,13135,13136,13137,13138,13139,13140,13141,13142,13143,13144,13145,13146,13147,13148,13149,13150,13151,13152,13153,13154,13155,13156,13157,13158,13159,13160,13161,13162,13163,13164,13165,13166,13167,13168,13169,13170,13171,13172,13173,13174,13175,13176,13177,13178,13179,13180,13181,13182,13183,13184,13185,13186,13187,13188,13189,13190,13191,13192,13193,13194,13195,13196,13197,13198,13199,13200,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13216,13217,13218,13219,13220,13221,13222,13223,13224,13225,13226,13227,13228,13229,13230,13231,13232,13233,13234,13235,13236,13237,13238,13239,13240,13241,13242,13243,13244,13245,13246,13247,13248,13249,13250,13251,13252,13253,13254,13255,13256,13257,13258,13259,13260,13261,13262,13263,13264,13265,13266,13267,13268,13269,13270,13271,13272,13273,13274,13275,13276,13277,13278,13279,13280,13281,13282,13283,13284,13285,13286,13287,13288,13289,13290,13291,13292,13293,13294,13295,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "207,449,902,957,1013,1073,1134,1199,1354,1404,1454,1507,1565,1664,1733,1781,1852,1924,1996,2069,2136,2185,2239,2276,2327,2387,2434,2490,2539,2597,2651,2712,2768,2819,2879,2935,2998,3047,3103,3159,3209,3268,3323,3385,3432,3486,3542,3594,3649,3703,3757,3811,3860,3918,3972,4029,4085,4132,4185,4241,4301,4364,4423,4485,4535,4589,4643,4691,4748,4801,5363,5417,6250,6361,6423,6479,6539,6592,6653,6732,6813,6885,6964,7044,7120,7198,7267,7343,7420,7491,7564,7640,7718,7787,7863,7940,8004,8075,10497,10593,10646,10902,10969,11022,11074,11124,11182,11247,11295,16789,16856,16922,16980,17049,17107,17176,17246,17319,17393,17461,17528,17598,17664,17737,17797,17873,17933,17993,18068,18136,18202,18270,18330,18389,18446,18512,18574,18631,18699,18772,18842,18904,18965,19033,19095,19165,19234,19290,19349,19411,19473,19540,19597,19658,19719,19780,19841,19897,19953,20009,20065,20123,20181,20239,20297,20354,20411,20468,20525,20584,20643,20701,20784,20867,20940,20994,21063,21119,21200,21281,21352,21687,21740,21798,22539,22653,23006,23066,23120,23190,23260,23325,23391,23456,23524,23593,23661,23791,23844,23903,23961,24059,24109,24161,24207,24257,24313,24407,24465,24523,24585,24648,24710,24769,24829,24894,24960,25025,25087,25149,25211,25273,25335,25397,25463,25530,25596,25659,25723,25786,25854,25915,25977,26039,26102,26166,26229,26293,26371,26430,26496,26576,26637,26731,26789,27222,27267,27621,27685,29214,32051,32125,32196,32262,32336,32405,32476,32549,32620,32688,32761,32837,32907,32985,33053,33119,33180,33249,33313,33379,33447,33513,33576,33644,33715,33780,33853,33916,33997,34061,34127,34197,34267,34337,34407,37553,37610,37668,37727,37787,37846,37905,37964,38023,38082,38141,38200,38259,38318,38377,38437,38498,38560,38621,38682,38743,38804,38865,38926,38986,39047,39108,39168,39229,39290,39351,39412,39473,39534,39595,39656,39717,39778,39839,39907,39976,40046,40115,40184,40253,40322,40391,40460,40529,40598,40667,40736,40796,40857,40919,40980,41041,41102,41163,41224,41285,41346,41407,41468,41529,41591,41654,41718,41781,41844,41907,41970,42033,42096,42159,42222,42285,42348,42409,42471,42534,42596,42658,42720,42782,42844,42906,42968,43030,43092,43154,43211,43297,43377,43467,43562,43654,43746,43836,43919,44012,44099,44196,44287,44388,44475,44578,44667,44766,44858,44958,45042,45136,45224,45322,45405,45496,45590,45689,45791,45889,45989,46076,46176,46262,46358,46446,46527,46618,46714,46807,46900,46991,47076,47170,47259,47357,47450,47552,47640,47744,47835,47935,48028,48129,48214,48309,48398,48497,48582,48674,48769,48869,48972,49071,49174,49263,49364,49451,49548,49636,49732,49824,49924,50014,50112,50197,50286,50375,50468,50555,51318,51384,51460,51529,51608,51681,51761,51841,51918,51986,52064,52140,52211,52292,52365,52448,52523,52608,52681,52762,52843,52917,53001,53071,53149,53219,53299,53377,53449,53531,53601,53678,53758,53843,53931,54015,54102,54176,54254,54332,54403,54484,54575,54658,54754,54852,54959,55024,55090,55143,55219,55285,55372,55448,64745,64980,65575,65629,65708,65786,65859,65924,65987,66053,66124,66195,66265,66327,66396,66462,66522,66589,66656,66712,66763,66816,66868,66922,66993,67056,67115,67177,67236,67309,67376,67446,67506,67569,67644,67716,67812,67883,67939,68010,68067,68124,68190,68254,68325,68382,68435,68498,68550,68608,70837,70906,70972,71031,71114,71173,71230,71297,71367,71441,71503,71572,71642,71741,71838,71937,72023,72109,72190,72265,72354,72445,72529,72588,72634,72700,72757,72824,72881,72963,73028,73094,73217,73301,73422,73487,73549,73647,73721,73804,73893,73957,74036,74110,74172,74268,74333,74392,74448,74504,74564,74671,74718,74778,74839,74903,74964,75024,75082,75125,75174,75226,75277,75329,75378,75427,75492,75558,75618,75679,75735,75794,75843,75891,75949,76006,76108,76165,76240,76288,76339,76401,76466,76518,76592,76655,76718,76786,76836,76898,76958,77015,77075,77124,77192,77298,77400,77469,77540,77596,77645,77745,77816,77926,78017,78099,78197,78253,78354,78464,78563,78626,78732,78809,78921,79048,79160,79287,79357,79471,79602,79699,79767,79885,79988,80106,80167,80241,80308,80413,80535,80609,80676,80786,80885,80958,81055,81177,81295,81413,81474,81596,81713,81781,81887,81989,82069,82140,82236,82303,82377,82451,82537,82627,82705,82782,82882,82953,83074,83195,83259,83384,83458,83582,83706,83773,83882,84010,84122,84201,84279,84380,84451,84573,84695,84760,84886,84998,85104,85172,85271,85375,85438,85504,85588,85701,85814,85932,86010,86082,86218,86354,86439,86579,86717,86855,86997,87079,87165,87242,87315,87424,87535,87663,87791,87923,88053,88183,88317,88406,88468,88564,88631,88748,88869,88966,89048,89135,89222,89353,89484,89619,89696,89773,89884,89998,90072,90181,90293,90360,90433,90498,90600,90696,90800,90868,90933,91027,91099,91209,91315,91388,91479,91581,91684,91779,91886,91991,92113,92235,92361,92420,92478,92602,92726,92854,92972,93090,93212,93298,93395,93529,93663,93743,93881,94013,94145,94281,94356,94432,94535,94609,94722,94803,94860,94921,94980,95040,95098,95159,95217,95267,95316,95383,95442,95501,95550,95621,95705,95775,95846,95926,95995,96058,96126,96192,96260,96325,96391,96468,96546,96652,96758,96854,96983,97072,97199,97265,97335,97421,97487,97570,97644,97742,97838,97934,98032,98141,98236,98325,98387,98447,98512,98569,98650,98704,98761,98858,98968,99029,99144,99265,99360,99452,99545,99601,99660,99709,99801,99850,99904,99958,100012,100066,100120,100175,100285,100395,100503,100613,100723,100833,100943,101051,101157,101261,101365,101469,101564,101659,101752,101845,101949,102055,102159,102263,102356,102449,102542,102635,102743,102849,102955,103061,103158,103253,103348,103443,103549,103655,103761,103867,103965,104060,104156,104253,104318,104422,104480,104544,104605,104667,104727,104792,104854,104922,104980,105043,105106,105173,105248,105321,105387,105439,105492,105544,105601,105685,105780,105865,105946,106026,106103,106182,106259,106333,106407,106478,106558,106630,106705,106770,106831,106891,106966,107040,107113,107183,107255,107325,107398,107462,107532,107578,107647,107699,107784,107867,107924,107990,108057,108123,108204,108279,108335,108388,108449,108507,108557,108606,108655,108704,108766,108818,108863,108944,108995,109049,109102,109156,109207,109256,109322,109373,109434,109495,109557,109607,109648,109725,109784,109843,109902,109963,110019,110075,110142,110203,110268,110323,110388,110457,110525,110603,110672,110732,110803,110877,110942,111014,111084,111151,111235,111304,111371,111441,111504,111571,111639,111722,111801,111891,111968,112036,112103,112181,112238,112295,112363,112429,112485,112545,112604,112658,112708,112758,112806,112868,112919,112992,113072,113152,113216,113283,113354,113412,113473,113539,113598,113665,113725,113785,113848,113916,113977,114044,114122,114192,114241,114298,114367,114428,114516,114604,114692,114780,114867,114954,115041,115128,115186,115260,115330,115386,115457,115522,115584,115659,115732,115822,115888,115954,116015,116079,116141,116199,116270,116353,116412,116483,116549,116614,116675,116734,116805,116871,116936,117019,117095,117170,117251,117311,117380,117450,117519,117574,117630,117686,117747,117805,117861,117920,117974,118029,118091,118148,118242,118311,118412,118463,118533,118596,118652,118710,118769,118823,118909,118993,119063,119132,119202,119317,119438,119505,119572,119647,119714,119773,119827,119881,119935,119988,120040,123032,123169,126973,127022,127072,127163,127211,127267,127325,127387,127442,127553,127624,127688,127747,127809,127875,128189,128334,128378,128423,129515,129566,129613,129658,129709,129760,129811,130348,130889,130955,131134,131197,131337,131394,131448,131503,131561,131616,131675,131731,131800,131869,131938,132008,132071,132134,132197,132260,132325,132390,132455,132520,132583,132647,132711,132775,132826,132904,132982,133053,133125,133198,133343,133409,133475,133543,133611,133677,133744,133818,133881,133938,133998,134063,134130,134195,134252,134313,134371,134475,134585,134694,134798,134876,134941,135008,135074,135144,135191,135243,135293,135989,138304,143190,143321,143505,143683,143921,144110,144897,144995,145110,147022,150309,150469,150534,150623,150780,151452,151605,151871,158922,159109,159205,159295,159391,159481,159647,159770,159893,160063,160169,160284,160399,160501,160607,160724,165981,166063,166236,166404,166552,166711,166866,167039,167156,167273,167441,167553,167667,167839,168015,168173,168306,168418,168564,168716,168848,168991,170282,170460,170596,170692,170828,170923,171090,171183,171275,171462,171618,171796,171960,172142,172459,172641,172823,173013,173245,173435,173612,173774,173931,174041,174224,174361,174565,174749,174933,175093,175251,175435,175662,175865,176036,176256,176478,176633,176833,177017,177120,177310,177451,177616,177787,177987,178191,178393,178558,178763,178962,179161,179358,179449,179598,179748,179832,179981,180126,180278,180419,180585,180941,181019,181320,181486,181641,182182,182340,182504,186510,186733,189846,190123,190395,190673,190918,190980,193808,194259,194715,205852,206000,206514,206951,207385,211725,211810,211931,212030,212435,212532,212649,212736,212859,212960,213366,213465,213584,213677,213784,214127,214234,214479,214600,215009,215257,215357,215462,215581,216090,216237,216356,216607,216740,217155,217409,222624,222871,222996,223404,223525,223753,223874,224007,224154,244876,245368,265839,266263,287030,287524,308040,308466,313307,318724,322815,328246,332988,338365,342349,346341,351732,352279,352712,353468,353698,353941,355108,403278,404182,404766,405239,406669,407413,408606,409660,410138,410431,410814,412329,413094,414237,414678,415119,415715,415989,416400,417416,417594,418347,418484,418575,420769,421035,421357,421567,421676,421795,421979,423097,423567,424318,426901,428725,429101,429329,429585,429844,430420,430774,430896,431035,431327,431587,432515,432801,433204,433606,433949,434161,434362,434575,434864,445751,445824,445911,445996,450916,451028,451134,451257,451389,451512,451642,451766,451899,452030,452155,452272,452392,452524,452652,452766,452884,452997,453118,453306,453493,453674,453857,454041,454206,454388,454508,454628,454736,454846,454958,455066,455176,455341,455507,455659,455824,455925,456045,456216,456377,456540,456701,456868,456987,457104,457284,457466,457647,457830,457985,458130,458252,458387,458550,458743,458869,459021,459163,459333,459489,459661,466424,466619,466711,466884,467046,467141,467310,467404,467493,467736,467825,468118,468534,468954,469375,469801,470218,470634,471051,471469,471883,472353,472826,473298,473709,474180,474652,474842,475048,475154,475262,475368,475480,475594,475706,475820,475936,476050,476158,476268,476376,476638,477017,477421,477568,477676,477786,477894,478008,478417,478831,478947,479365,479606,480036,480471,480881,481303,481713,481835,482244,482660,482782,483000,485820,485888,486232,486312,486668,486818,486962,487038,487150,487240,487502,487767,487875,488027,488135,488211,488323,488413,488515,488623,488731,488831,488939,489024,489190,489294,489422,489509,489676,489754,489868,489960,490224,490491,490601,490754,490864,490948,491337,491435,491543,491637,491767,491875,491997,492133,492241,492361,492495,492617,492745,492887,493013,493153,493279,493397,493529,493627,493737,494037,494149,494267,494731,494847,495150,495276,495372,495773,495883,496007,496145,496255,496377,496689,496813,496943,497419,497547,497862,498000,498162,498378,498534,499826,499894,499978,500082,500285,500474,500675,500868,501073,501386,501598,501764,501880,502126,502342,502655,503081,503543,503780,503932,504192,504336,504478,507710,507824,507944,508060,508154,508475,508574,508692,508793,509072,509357,509636,509918,510171,510430,510683,510939,511363,511439,514689,516044,516488,518342,518917,519125,520135,520515,520681,520822,525842,526268,526380,526515,526668,526865,527036,527219,527394,527581,527853,528011,528095,528199,528686,529242,529400,529619,529850,530073,530308,530530,530796,530934,531533,531647,531785,531897,532021,532592,533087,533633,533778,533871,533963,535890,536460,536758,536947,537153,537346,537556,538440,538585,538977,539135,539352,547408,547840,548715,549335,549532,550480,551245,551368,552141,552362,552562,554539,554639,554729,555415,556168,556933,557696,558471,559684,559849,561462,561783,562846,563056,563226,563796,564691,565324,565490,566976,567592,567828,568049,569007,569272,569537,569784,570198,570434,571719,572168,572355,572604,572846,573022,573263,573496,573721,574316,574791,575315,575576,576927,577402,578628,579098,580146,580598,580842,581299,582544,583027,583177,583732,584184,584584,584737,584882,585025,585095,585523,585811,586315,586824,586940,587842,587964,588076,588253,588519,588789,589055,589323,589579,589839,590095,590353,590605,590861,591113,591367,591599,591835,592087,592343,592595,592849,593081,593315,593427,594079,594534,594658,595750,596565,596761,597085,597474,597826,598067,598281,598580,598772,599087,599294,599640,599940,600341,600560,600973,601210,601580,602304,602659,602928,603068,603322,603466,603743,604735,605144,605776,606122,606490,607564,607927,608327,609835,610420,610738,613273,613467,613685,613911,614123,614322,614529,615733,616028,616585,616975,617607,618084,618329,618816,619062,620258,620655,621661,621883,622306,622497,622876,622964,623072,623180,623493,623818,624137,624468,627171,627359,627620,627869,630453,630645,630910,631163,631695,632103,632302,632886,633121,633245,633657,633871,634273,634376,634506,634681,634933,635129,635269,635463,636474,637543,637831,637961,638738,639395,639541,640247,640485,642025,642175,642592,642757,643443,643913,644109,644200,644284,644428,644662,644829,645757,646043,646203,646818,646977,647305,647532,648044,648406,648485,648824,648929,649294,649665,650026,651900,652529,653605,654029,654282,654434,655482,656219,656422,656668,656915,657133,657375,657696,657960,658265,658488,658799,658988,659703,659972,660466,660692,661132,661291,661575,662320,662685,662990,663148,663386,664705,665103,665331,665551,665693,666983,667089,667219,667357,667481,667769,667938,668038,668323,668437,669320,670075,670514,670638,670884,671077,671211,671402,672181,672399,672690,672969,673286,673508,673803,674086,674190,674531,675347,675663,676224,676730,676935,677721,678126,678787,678976,679527,680093,680213,680615,802832,802927,803020,803083,803165,803258,803351,803438,803536,803627,803718,803806,803890,803986,804086,804192,804295,804396,804500,804606,804705,804811,804913,805020,805129,805240,805371,805491,805607,805725,805824,805931,806047,806166,806294,806383,806478,806555,806644,806735,806828,806902,806999,807094,807192,807291,807395,807491,807593,807696,807796,807899,807984,808085,808183,808273,808368,808455,808561,808663,808757,808848,808942,809018,809110,809199,809302,809413,809496,809582,809677,809774,809870,809958,810059,810160,810263,810369,810467,810564,810659,810757,810860,810960,811063,811168,811286,811402,811497,811590,811675,811771,811865,811957,812040,812144,812249,812349,812450,812555,812655,812756,812855,812957,813051,813158,813260,813363,813456,813552,813654,813757,813853,813955,814058,814155,814258,814356,814460,814565,814662,814770,814884,814999,815107,815221,815336,815438,815543,815651,815761,815877,815994,816089,816186,816285,816390,816496,816595,816700,816806,816906,817012,817113,817220,817339,817438,817543,817645,817747,817847,817950,818045,818149,818234,818338,818442,818540,818644,818750,818848,818953,819051,819164,819258,819347,819436,819519,819610,819693,819791,819881,819977,820066,820160,820248,820344,820429,820537,820638,820739,820837,820943,821034,821133,821230,821328,821424,821517,821627,821725,821820,821930,822022,822122,822221,822308,822412,822517,822616,822723,822830,822929,823038,823130,823241,823352,823463,823567,823682,823798,823925,824045,824140,824235,824332,824431,824523,824622,824714,824813,824899,824993,825096,825192,825295,825391,825494,825591,825689,825792,825885,825975,826076,826159,826250,826335,826427,826530,826625,826721,826814,826908,826987,827094,827185,827284,827377,827480,827584,827685,827786,827890,827984,828088,828192,828305,828411,828517,828625,828742,828843,828951,829051,829154,829259,829366,829462,829541,829631,829715,829807,829880,829972,830061,830153,830238,830335,830428,830523,830622,830719,830810,830901,830993,831088,831195,831303,831405,831502,831599,831692,831779,831863,831960,832057,832150,832237,832328,832427,832526,832621,832710,832791,832890,832994,833091,833196,833293,833377,833476,833580,833677,833782,833879,833977,834078,834184,834283,834390,834489,834588,834679,834768,834857,834939,835032,835123,835234,835335,835435,835547,835660,835758,835866,835960,836060,836149,836241,836352,836462,836557,836673,836799,836925,837044,837172,837297,837422,837540,837667,837776,837885,837998,838121,838244,838360,838485,838582,838690,838812,838928,839044,839153,839241,839342,839431,839532,839619,839707,839804,839896,840002,840102,840178", "endLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,216,217,218,219,220,221,222,223,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,415,420,421,422,440,442,451,452,453,454,455,456,457,458,459,460,464,465,466,467,468,470,471,472,473,474,475,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,514,515,526,527,535,536,568,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,1065,1069,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1826,1827,1890,1891,1892,1894,1895,1896,1897,1898,1899,1901,1902,1903,1904,1905,1906,1912,1915,1916,1917,1937,1938,1939,1940,1941,1942,1943,1954,1964,1965,1968,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2048,2078,2133,2134,2135,2136,2137,2138,2148,2149,2150,2178,2212,2213,2214,2215,2216,2222,2223,2226,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2427,2428,2429,2430,2431,2432,2433,2434,2435,2438,2441,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2498,2499,2500,2501,2502,2513,2514,2515,2564,2565,2612,2616,2620,2624,2625,2629,2676,2683,2691,2856,2866,2875,2884,2893,2954,2955,2956,2962,2963,2964,2965,2966,2967,2973,2974,2975,2976,2977,2982,2983,2987,2988,2994,2998,2999,3000,3001,3011,3012,3013,3017,3018,3024,3028,3029,3101,3102,3107,3108,3111,3112,3113,3114,3378,3385,3646,3652,3916,3923,4184,4190,4253,4335,4387,4469,4531,4613,4677,4729,4811,4819,4825,4836,4840,4844,4857,4872,5648,5655,5661,5678,5691,5711,5728,5737,5742,5749,5769,5782,5799,5805,5811,5818,5822,5828,5842,5845,5855,5856,5857,5905,5909,5913,5917,5918,5919,5922,5938,5945,5959,6004,6005,6044,6048,6052,6057,6064,6070,6071,6074,6078,6083,6096,6100,6105,6110,6115,6118,6121,6124,6128,6132,6272,6273,6274,6275,6356,6357,6358,6359,6360,6361,6362,6363,6364,6365,6366,6367,6368,6369,6370,6371,6372,6373,6377,6381,6385,6389,6393,6397,6401,6402,6403,6404,6405,6406,6407,6408,6412,6416,6417,6421,6422,6425,6429,6432,6435,6438,6442,6445,6448,6452,6456,6460,6464,6467,6468,6469,6470,6473,6477,6480,6483,6486,6489,6492,6495,6499,6569,6570,6573,6576,6577,6580,6581,6582,6586,6587,6592,6599,6606,6613,6620,6627,6634,6641,6648,6655,6664,6673,6682,6689,6698,6707,6710,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6729,6734,6739,6742,6743,6744,6745,6746,6754,6762,6763,6771,6775,6783,6791,6799,6807,6815,6816,6824,6832,6833,6836,6839,6877,6882,6884,6889,6893,6897,6898,6899,6900,6904,6908,6909,6913,6914,6915,6916,6917,6918,6919,6920,6921,6922,6923,6927,6928,6929,6930,6934,6935,6936,6937,6941,6945,6946,6950,6951,6952,6957,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6975,6976,6977,6981,6982,6983,6989,6990,6994,6996,6997,7002,7003,7004,7005,7006,7007,7011,7012,7013,7019,7020,7024,7026,7030,7034,7038,7042,7056,7057,7058,7061,7064,7067,7070,7073,7078,7082,7085,7086,7091,7095,7100,7106,7112,7117,7121,7126,7130,7134,7175,7176,7177,7178,7179,7183,7184,7185,7186,7190,7194,7198,7202,7206,7210,7214,7218,7224,7225,7266,7280,7285,7311,7318,7321,7332,7337,7340,7343,7398,7404,7405,7408,7411,7414,7417,7420,7423,7426,7430,7433,7434,7435,7443,7451,7454,7459,7464,7469,7474,7478,7482,7483,7491,7492,7493,7494,7495,7503,7508,7513,7514,7515,7516,7541,7547,7552,7555,7559,7562,7566,7576,7579,7584,7587,7591,7595,7700,7714,7727,7731,7746,7757,7760,7771,7776,7780,7815,7816,7817,7829,7837,7845,7853,7861,7881,7884,7911,7916,7936,7939,7942,7949,7962,7971,7974,7994,8004,8008,8012,8025,8029,8033,8037,8043,8047,8064,8072,8076,8080,8084,8087,8091,8095,8099,8109,8116,8123,8127,8153,8163,8188,8197,8217,8227,8231,8241,8266,8276,8279,8286,8293,8300,8301,8302,8303,8304,8311,8315,8321,8327,8328,8341,8342,8343,8346,8349,8352,8355,8358,8361,8364,8367,8370,8373,8376,8379,8382,8385,8388,8391,8394,8397,8400,8403,8406,8407,8415,8423,8424,8437,8447,8451,8456,8461,8465,8468,8472,8476,8479,8483,8486,8490,8495,8500,8503,8510,8514,8518,8527,8532,8537,8538,8542,8545,8549,8562,8567,8575,8579,8583,8600,8604,8609,8627,8634,8638,8668,8671,8674,8677,8680,8683,8686,8705,8711,8719,8726,8738,8746,8751,8759,8763,8781,8788,8804,8808,8816,8819,8824,8825,8826,8827,8831,8835,8839,8843,8878,8881,8885,8889,8923,8926,8930,8934,8943,8949,8952,8962,8966,8967,8974,8978,8985,8986,8987,8990,8995,9000,9001,9005,9020,9039,9043,9044,9056,9066,9067,9079,9084,9108,9111,9117,9120,9129,9137,9141,9144,9147,9150,9154,9157,9174,9178,9181,9196,9199,9207,9212,9219,9224,9225,9230,9231,9237,9243,9249,9281,9292,9309,9316,9320,9323,9336,9345,9349,9354,9358,9362,9366,9370,9374,9378,9382,9387,9390,9402,9407,9416,9419,9426,9427,9431,9440,9446,9450,9451,9455,9476,9482,9486,9490,9491,9509,9510,9511,9512,9513,9518,9521,9522,9528,9529,9541,9553,9560,9561,9566,9571,9572,9576,9590,9595,9601,9607,9613,9618,9624,9630,9631,9637,9652,9657,9666,9675,9678,9692,9697,9708,9712,9721,9730,9731,9738,9746,13124,13125,13126,13127,13128,13129,13130,13131,13132,13133,13134,13135,13136,13137,13138,13139,13140,13141,13142,13143,13144,13145,13146,13147,13148,13149,13150,13151,13152,13153,13154,13155,13156,13157,13158,13159,13160,13161,13162,13163,13164,13165,13166,13167,13168,13169,13170,13171,13172,13173,13174,13175,13176,13177,13178,13179,13180,13181,13182,13183,13184,13185,13186,13187,13188,13189,13190,13191,13192,13193,13194,13195,13196,13197,13198,13199,13200,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13216,13217,13218,13219,13220,13221,13222,13223,13224,13225,13226,13227,13228,13229,13230,13231,13232,13233,13234,13235,13236,13237,13238,13239,13240,13241,13242,13243,13244,13245,13246,13247,13248,13249,13250,13251,13252,13253,13254,13255,13256,13257,13258,13259,13260,13261,13262,13263,13264,13265,13266,13267,13268,13269,13270,13271,13272,13273,13274,13275,13276,13277,13278,13279,13280,13281,13282,13283,13284,13285,13286,13287,13288,13289,13290,13291,13292,13293,13294,13295,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "258,493,952,1008,1068,1129,1194,1249,1399,1449,1502,1560,1608,1728,1776,1847,1919,1991,2064,2131,2180,2234,2271,2322,2382,2429,2485,2534,2592,2646,2707,2763,2814,2874,2930,2993,3042,3098,3154,3204,3263,3318,3380,3427,3481,3537,3589,3644,3698,3752,3806,3855,3913,3967,4024,4080,4127,4180,4236,4296,4359,4418,4480,4530,4584,4638,4686,4743,4796,4852,5412,5468,6308,6418,6474,6534,6587,6648,6727,6808,6880,6959,7039,7115,7193,7262,7338,7415,7486,7559,7635,7713,7782,7858,7935,7999,8070,8142,10543,10641,10696,10964,11017,11069,11119,11177,11242,11290,11341,16851,16917,16975,17044,17102,17171,17241,17314,17388,17456,17523,17593,17659,17732,17792,17868,17928,17988,18063,18131,18197,18265,18325,18384,18441,18507,18569,18626,18694,18767,18837,18899,18960,19028,19090,19160,19229,19285,19344,19406,19468,19535,19592,19653,19714,19775,19836,19892,19948,20004,20060,20118,20176,20234,20292,20349,20406,20463,20520,20579,20638,20696,20779,20862,20935,20989,21058,21114,21195,21276,21347,21476,21735,21793,21851,22592,22694,23061,23115,23185,23255,23320,23386,23451,23519,23588,23656,23786,23839,23898,23956,24008,24104,24156,24202,24252,24308,24355,24460,24518,24580,24643,24705,24764,24824,24889,24955,25020,25082,25144,25206,25268,25330,25392,25458,25525,25591,25654,25718,25781,25849,25910,25972,26034,26097,26161,26224,26288,26366,26425,26491,26571,26632,26685,26784,26835,27262,27323,27680,27739,29271,32120,32191,32257,32331,32400,32471,32544,32615,32683,32756,32832,32902,32980,33048,33114,33175,33244,33308,33374,33442,33508,33571,33639,33710,33775,33848,33911,33992,34056,34122,34192,34262,34332,34402,34469,37605,37663,37722,37782,37841,37900,37959,38018,38077,38136,38195,38254,38313,38372,38432,38493,38555,38616,38677,38738,38799,38860,38921,38981,39042,39103,39163,39224,39285,39346,39407,39468,39529,39590,39651,39712,39773,39834,39902,39971,40041,40110,40179,40248,40317,40386,40455,40524,40593,40662,40731,40791,40852,40914,40975,41036,41097,41158,41219,41280,41341,41402,41463,41524,41586,41649,41713,41776,41839,41902,41965,42028,42091,42154,42217,42280,42343,42404,42466,42529,42591,42653,42715,42777,42839,42901,42963,43025,43087,43149,43206,43292,43372,43462,43557,43649,43741,43831,43914,44007,44094,44191,44282,44383,44470,44573,44662,44761,44853,44953,45037,45131,45219,45317,45400,45491,45585,45684,45786,45884,45984,46071,46171,46257,46353,46441,46522,46613,46709,46802,46895,46986,47071,47165,47254,47352,47445,47547,47635,47739,47830,47930,48023,48124,48209,48304,48393,48492,48577,48669,48764,48864,48967,49066,49169,49258,49359,49446,49543,49631,49727,49819,49919,50009,50107,50192,50281,50370,50463,50550,50641,51379,51455,51524,51603,51676,51756,51836,51913,51981,52059,52135,52206,52287,52360,52443,52518,52603,52676,52757,52838,52912,52996,53066,53144,53214,53294,53372,53444,53526,53596,53673,53753,53838,53926,54010,54097,54171,54249,54327,54398,54479,54570,54653,54749,54847,54954,55019,55085,55138,55214,55280,55367,55443,55519,64805,65030,65624,65703,65781,65854,65919,65982,66048,66119,66190,66260,66322,66391,66457,66517,66584,66651,66707,66758,66811,66863,66917,66988,67051,67110,67172,67231,67304,67371,67441,67501,67564,67639,67711,67807,67878,67934,68005,68062,68119,68185,68249,68320,68377,68430,68493,68545,68603,68670,70901,70967,71026,71109,71168,71225,71292,71362,71436,71498,71567,71637,71736,71833,71932,72018,72104,72185,72260,72349,72440,72524,72583,72629,72695,72752,72819,72876,72958,73023,73089,73212,73296,73417,73482,73544,73642,73716,73799,73888,73952,74031,74105,74167,74263,74328,74387,74443,74499,74559,74666,74713,74773,74834,74898,74959,75019,75077,75120,75169,75221,75272,75324,75373,75422,75487,75553,75613,75674,75730,75789,75838,75886,75944,76001,76103,76160,76235,76283,76334,76396,76461,76513,76587,76650,76713,76781,76831,76893,76953,77010,77070,77119,77187,77293,77395,77464,77535,77591,77640,77740,77811,77921,78012,78094,78192,78248,78349,78459,78558,78621,78727,78804,78916,79043,79155,79282,79352,79466,79597,79694,79762,79880,79983,80101,80162,80236,80303,80408,80530,80604,80671,80781,80880,80953,81050,81172,81290,81408,81469,81591,81708,81776,81882,81984,82064,82135,82231,82298,82372,82446,82532,82622,82700,82777,82877,82948,83069,83190,83254,83379,83453,83577,83701,83768,83877,84005,84117,84196,84274,84375,84446,84568,84690,84755,84881,84993,85099,85167,85266,85370,85433,85499,85583,85696,85809,85927,86005,86077,86213,86349,86434,86574,86712,86850,86992,87074,87160,87237,87310,87419,87530,87658,87786,87918,88048,88178,88312,88401,88463,88559,88626,88743,88864,88961,89043,89130,89217,89348,89479,89614,89691,89768,89879,89993,90067,90176,90288,90355,90428,90493,90595,90691,90795,90863,90928,91022,91094,91204,91310,91383,91474,91576,91679,91774,91881,91986,92108,92230,92356,92415,92473,92597,92721,92849,92967,93085,93207,93293,93390,93524,93658,93738,93876,94008,94140,94276,94351,94427,94530,94604,94717,94798,94855,94916,94975,95035,95093,95154,95212,95262,95311,95378,95437,95496,95545,95616,95700,95770,95841,95921,95990,96053,96121,96187,96255,96320,96386,96463,96541,96647,96753,96849,96978,97067,97194,97260,97330,97416,97482,97565,97639,97737,97833,97929,98027,98136,98231,98320,98382,98442,98507,98564,98645,98699,98756,98853,98963,99024,99139,99260,99355,99447,99540,99596,99655,99704,99796,99845,99899,99953,100007,100061,100115,100170,100280,100390,100498,100608,100718,100828,100938,101046,101152,101256,101360,101464,101559,101654,101747,101840,101944,102050,102154,102258,102351,102444,102537,102630,102738,102844,102950,103056,103153,103248,103343,103438,103544,103650,103756,103862,103960,104055,104151,104248,104313,104417,104475,104539,104600,104662,104722,104787,104849,104917,104975,105038,105101,105168,105243,105316,105382,105434,105487,105539,105596,105680,105775,105860,105941,106021,106098,106177,106254,106328,106402,106473,106553,106625,106700,106765,106826,106886,106961,107035,107108,107178,107250,107320,107393,107457,107527,107573,107642,107694,107779,107862,107919,107985,108052,108118,108199,108274,108330,108383,108444,108502,108552,108601,108650,108699,108761,108813,108858,108939,108990,109044,109097,109151,109202,109251,109317,109368,109429,109490,109552,109602,109643,109720,109779,109838,109897,109958,110014,110070,110137,110198,110263,110318,110383,110452,110520,110598,110667,110727,110798,110872,110937,111009,111079,111146,111230,111299,111366,111436,111499,111566,111634,111717,111796,111886,111963,112031,112098,112176,112233,112290,112358,112424,112480,112540,112599,112653,112703,112753,112801,112863,112914,112987,113067,113147,113211,113278,113349,113407,113468,113534,113593,113660,113720,113780,113843,113911,113972,114039,114117,114187,114236,114293,114362,114423,114511,114599,114687,114775,114862,114949,115036,115123,115181,115255,115325,115381,115452,115517,115579,115654,115727,115817,115883,115949,116010,116074,116136,116194,116265,116348,116407,116478,116544,116609,116670,116729,116800,116866,116931,117014,117090,117165,117246,117306,117375,117445,117514,117569,117625,117681,117742,117800,117856,117915,117969,118024,118086,118143,118237,118306,118407,118458,118528,118591,118647,118705,118764,118818,118904,118988,119058,119127,119197,119312,119433,119500,119567,119642,119709,119768,119822,119876,119930,119983,120035,120109,123164,123304,127017,127067,127117,127206,127262,127320,127382,127437,127495,127619,127683,127742,127804,127870,127936,128227,128373,128418,128461,129561,129608,129653,129704,129755,129806,129857,130391,130950,131012,131192,131264,131389,131443,131498,131556,131611,131670,131726,131795,131864,131933,132003,132066,132129,132192,132255,132320,132385,132450,132515,132578,132642,132706,132770,132821,132899,132977,133048,133120,133193,133265,133404,133470,133538,133606,133672,133739,133813,133876,133933,133993,134058,134125,134190,134247,134308,134366,134470,134580,134689,134793,134871,134936,135003,135069,135139,135186,135238,135288,135345,136304,138449,143316,143500,143678,143916,144105,144274,144990,145105,145190,147096,150464,150529,150618,150775,150932,151600,151754,151925,159104,159200,159290,159386,159476,159642,159765,159888,160058,160164,160279,160394,160496,160602,160719,160834,166058,166231,166399,166547,166706,166861,167034,167151,167268,167436,167548,167662,167834,168010,168168,168301,168413,168559,168711,168843,168986,169108,170455,170591,170687,170823,170918,171085,171178,171270,171457,171613,171791,171955,172137,172454,172636,172818,173008,173240,173430,173607,173769,173926,174036,174219,174356,174560,174744,174928,175088,175246,175430,175657,175860,176031,176251,176473,176628,176828,177012,177115,177305,177446,177611,177782,177982,178186,178388,178553,178758,178957,179156,179353,179444,179593,179743,179827,179976,180121,180273,180414,180580,180741,181014,181315,181481,181636,181738,182335,182499,182685,186728,186853,190118,190390,190668,190913,190975,191260,194254,194710,195219,205995,206509,206946,207380,207823,211805,211926,212025,212430,212527,212644,212731,212854,212955,213361,213460,213579,213672,213779,214122,214229,214474,214595,215004,215252,215352,215457,215576,216085,216232,216351,216602,216735,217150,217404,217516,222866,222991,223399,223520,223748,223869,224002,224149,244871,245363,265834,266258,287025,287519,308035,308461,313302,318719,322810,328241,332983,338360,342344,346336,351727,352274,352707,353463,353693,353936,355103,356032,404177,404761,405234,406664,407408,408601,409655,410133,410426,410809,412324,413089,414232,414673,415114,415710,415984,416395,417411,417589,418342,418479,418570,420764,421030,421352,421562,421671,421790,421974,423092,423562,424313,426896,426991,429096,429324,429580,429839,430415,430769,430891,431030,431322,431582,432510,432796,433199,433601,433944,434156,434357,434570,434859,435144,445819,445906,445991,446090,451023,451129,451252,451384,451507,451637,451761,451894,452025,452150,452267,452387,452519,452647,452761,452879,452992,453113,453301,453488,453669,453852,454036,454201,454383,454503,454623,454731,454841,454953,455061,455171,455336,455502,455654,455819,455920,456040,456211,456372,456535,456696,456863,456982,457099,457279,457461,457642,457825,457980,458125,458247,458382,458545,458738,458864,459016,459158,459328,459484,459656,459947,466614,466706,466879,467041,467136,467305,467399,467488,467731,467820,468113,468529,468949,469370,469796,470213,470629,471046,471464,471878,472348,472821,473293,473704,474175,474647,474837,475043,475149,475257,475363,475475,475589,475701,475815,475931,476045,476153,476263,476371,476633,477012,477416,477563,477671,477781,477889,478003,478412,478826,478942,479360,479601,480031,480466,480876,481298,481708,481830,482239,482655,482777,482995,483179,485883,486227,486307,486663,486813,486957,487033,487145,487235,487497,487762,487870,488022,488130,488206,488318,488408,488510,488618,488726,488826,488934,489019,489185,489289,489417,489504,489671,489749,489863,489955,490219,490486,490596,490749,490859,490943,491332,491430,491538,491632,491762,491870,491992,492128,492236,492356,492490,492612,492740,492882,493008,493148,493274,493392,493524,493622,493732,494032,494144,494262,494726,494842,495145,495271,495367,495768,495878,496002,496140,496250,496372,496684,496808,496938,497414,497542,497857,497995,498157,498373,498529,498733,499889,499973,500077,500280,500469,500670,500863,501068,501381,501593,501759,501875,502121,502337,502650,503076,503538,503775,503927,504187,504331,504473,507705,507819,507939,508055,508149,508470,508569,508687,508788,509067,509352,509631,509913,510166,510425,510678,510934,511358,511434,514684,516039,516483,518337,518912,519120,520130,520510,520676,520817,525837,526263,526375,526510,526663,526860,527031,527214,527389,527576,527848,528006,528090,528194,528681,529237,529395,529614,529845,530068,530303,530525,530791,530929,531528,531642,531780,531892,532016,532587,533082,533628,533773,533866,533958,535885,536455,536753,536942,537148,537341,537551,538435,538580,538972,539130,539347,539608,547835,548710,549330,549527,550475,551240,551363,552136,552357,552557,554534,554634,554724,555410,556163,556928,557691,558466,559679,559844,561457,561778,562841,563051,563221,563791,564686,565319,565485,566971,567587,567823,568044,569002,569267,569532,569779,570193,570429,571714,572163,572350,572599,572841,573017,573258,573491,573716,574311,574786,575310,575571,576922,577397,578623,579093,580141,580593,580837,581294,582539,583022,583172,583727,584179,584579,584732,584877,585020,585090,585518,585806,586310,586819,586935,587837,587959,588071,588248,588514,588784,589050,589318,589574,589834,590090,590348,590600,590856,591108,591362,591594,591830,592082,592338,592590,592844,593076,593310,593422,594074,594529,594653,595745,596560,596756,597080,597469,597821,598062,598276,598575,598767,599082,599289,599635,599935,600336,600555,600968,601205,601575,602299,602654,602923,603063,603317,603461,603738,604730,605139,605771,606117,606485,607559,607922,608322,609830,610415,610733,613268,613462,613680,613906,614118,614317,614524,615728,616023,616580,616970,617602,618079,618324,618811,619057,620253,620650,621656,621878,622301,622492,622871,622959,623067,623175,623488,623813,624132,624463,627166,627354,627615,627864,630448,630640,630905,631158,631690,632098,632297,632881,633116,633240,633652,633866,634268,634371,634501,634676,634928,635124,635264,635458,636469,637538,637826,637956,638733,639390,639536,640242,640480,642020,642170,642587,642752,643438,643908,644104,644195,644279,644423,644657,644824,645752,646038,646198,646813,646972,647300,647527,648039,648401,648480,648819,648924,649289,649660,650021,651895,652524,653600,654024,654277,654429,655477,656214,656417,656663,656910,657128,657370,657691,657955,658260,658483,658794,658983,659698,659967,660461,660687,661127,661286,661570,662315,662680,662985,663143,663381,664700,665098,665326,665546,665688,666978,667084,667214,667352,667476,667764,667933,668033,668318,668432,669315,670070,670509,670633,670879,671072,671206,671397,672176,672394,672685,672964,673281,673503,673798,674081,674185,674526,675342,675658,676219,676725,676930,677716,678121,678782,678971,679522,680088,680208,680610,681144,802922,803015,803078,803160,803253,803346,803433,803531,803622,803713,803801,803885,803981,804081,804187,804290,804391,804495,804601,804700,804806,804908,805015,805124,805235,805366,805486,805602,805720,805819,805926,806042,806161,806289,806378,806473,806550,806639,806730,806823,806897,806994,807089,807187,807286,807390,807486,807588,807691,807791,807894,807979,808080,808178,808268,808363,808450,808556,808658,808752,808843,808937,809013,809105,809194,809297,809408,809491,809577,809672,809769,809865,809953,810054,810155,810258,810364,810462,810559,810654,810752,810855,810955,811058,811163,811281,811397,811492,811585,811670,811766,811860,811952,812035,812139,812244,812344,812445,812550,812650,812751,812850,812952,813046,813153,813255,813358,813451,813547,813649,813752,813848,813950,814053,814150,814253,814351,814455,814560,814657,814765,814879,814994,815102,815216,815331,815433,815538,815646,815756,815872,815989,816084,816181,816280,816385,816491,816590,816695,816801,816901,817007,817108,817215,817334,817433,817538,817640,817742,817842,817945,818040,818144,818229,818333,818437,818535,818639,818745,818843,818948,819046,819159,819253,819342,819431,819514,819605,819688,819786,819876,819972,820061,820155,820243,820339,820424,820532,820633,820734,820832,820938,821029,821128,821225,821323,821419,821512,821622,821720,821815,821925,822017,822117,822216,822303,822407,822512,822611,822718,822825,822924,823033,823125,823236,823347,823458,823562,823677,823793,823920,824040,824135,824230,824327,824426,824518,824617,824709,824808,824894,824988,825091,825187,825290,825386,825489,825586,825684,825787,825880,825970,826071,826154,826245,826330,826422,826525,826620,826716,826809,826903,826982,827089,827180,827279,827372,827475,827579,827680,827781,827885,827979,828083,828187,828300,828406,828512,828620,828737,828838,828946,829046,829149,829254,829361,829457,829536,829626,829710,829802,829875,829967,830056,830148,830233,830330,830423,830518,830617,830714,830805,830896,830988,831083,831190,831298,831400,831497,831594,831687,831774,831858,831955,832052,832145,832232,832323,832422,832521,832616,832705,832786,832885,832989,833086,833191,833288,833372,833471,833575,833672,833777,833874,833972,834073,834179,834278,834385,834484,834583,834674,834763,834852,834934,835027,835118,835229,835330,835430,835542,835655,835753,835861,835955,836055,836144,836236,836347,836457,836552,836668,836794,836920,837039,837167,837292,837417,837535,837662,837771,837880,837993,838116,838239,838355,838480,838577,838685,838807,838923,839039,839148,839236,839337,839426,839527,839614,839702,839799,839891,839997,840097,840173,840250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\53ec144f1612c59cfdaa42b4b0883eab\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1874", "startColumns": "4", "startOffsets": "126019", "endColumns": "65", "endOffsets": "126080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\9e22fcbbd83ad381ad62f98cd9fdd486\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1959", "startColumns": "4", "startOffsets": "130596", "endColumns": "49", "endOffsets": "130641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b78d207cda22ac4214cb8630687fc1a6\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "94,9747", "startColumns": "4,4", "startOffsets": "5016,681149", "endLines": "94,9749", "endColumns": "60,12", "endOffsets": "5072,681289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2f477ab85e7d6008688a1a0c4b191573\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2410", "startColumns": "4", "startOffsets": "169113", "endColumns": "57", "endOffsets": "169166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5aea9c81c7615102d7b5640b77daf851\\transformed\\uCrop-n-Edit-4.1.0-non-native\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,2032,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,9765,9772,9779,9788,9797,9805", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57502,57592,57684,57733,57792,57871,57936,58002,58065,58126,58183,58237,58327,58416,58504,58560,58631,58709,58758,58828,58905,58991,59079,59172,121545,121600,121667,121733,121794,121879,121957,122022,122089,122147,122222,122283,122342,122417,122478,122538,122594,122645,122706,122770,122831,122890,122963,135420,188341,188418,188491,188556,188682,188744,188823,188872,189058,189127,189204,189271,682169,682528,682949,683495,684039,684456", "endLines": "959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,2032,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,9771,9778,9787,9796,9804,9810", "endColumns": "89,91,48,58,78,64,65,62,60,56,53,89,88,87,55,70,77,48,69,76,85,87,92,81,54,66,65,60,84,77,64,66,57,74,60,58,74,60,59,55,50,60,63,60,58,72,68,67,76,72,64,125,61,78,48,185,68,76,66,74,12,12,12,12,12,12", "endOffsets": "57587,57679,57728,57787,57866,57931,57997,58060,58121,58178,58232,58322,58411,58499,58555,58626,58704,58753,58823,58900,58986,59074,59167,59249,121595,121662,121728,121789,121874,121952,122017,122084,122142,122217,122278,122337,122412,122473,122533,122589,122640,122701,122765,122826,122885,122958,123027,135483,188413,188486,188551,188677,188739,188818,188867,189053,189122,189199,189266,189341,682523,682944,683490,684034,684451,684827"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2217,2218,2219,2220,2221,2504", "startColumns": "4,4,4,4,4,4", "startOffsets": "150937,151019,151123,151232,151352,181783", "endColumns": "81,103,108,119,99,68", "endOffsets": "151014,151118,151227,151347,151447,181847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\be5cc04b6a4fee62d71666ba2f889b70\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,208,215,513,557,558,570,571,572,573,574,575,576,579,580,581,582,584,585,586,587,588,589,590,591,641,642,643,644,645,646,647,648,649,650,862,863,864,865,866,867,868,869,870,871,872,873,930,931,932,933,934,935,936,937,945,946,947,948,949,950,951,952,953,954,957,958,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1126,1127,1131,1132,1133,1134,1135,1136,1137,1795,1796,1797,1798,1799,1800,1801,1802,1863,1864,1865,1866,1876,1909,1910,1919,1953,1962,1963,1966,1967,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2510,2604,2605,2606,2607,2608,2630,2638,2639,2643,2647,2658,2663,2692,2699,2703,2707,2712,2716,2720,2724,2728,2732,2736,2742,2746,2752,2756,2762,2766,2771,2775,2778,2782,2788,2792,2798,2802,2808,2811,2815,2819,2823,2827,2831,2832,2833,2834,2837,2840,2843,2846,2850,2851,2852,2853,2894,2897,2899,2901,2903,2908,2909,2913,2919,2923,2924,2926,2938,2939,2943,2949,2953,3030,3031,3035,3062,3066,3067,3071,4873,5045,5071,5242,5268,5299,5307,5313,5329,5351,5356,5361,5371,5380,5389,5393,5400,5419,5426,5427,5436,5439,5442,5446,5450,5454,5457,5458,5463,5468,5478,5483,5490,5496,5497,5500,5504,5509,5511,5513,5516,5519,5521,5525,5528,5535,5538,5541,5545,5547,5551,5553,5555,5557,5561,5569,5577,5589,5595,5604,5607,5618,5621,5622,5627,5628,6133,6202,6276,6277,6287,6296,6301,6303,6307,6310,6313,6316,6319,6322,6325,6328,6332,6335,6338,6341,6345,6348,6352,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6512,6513,6514,6515,6516,6517,6518,6519,6520,6522,6524,6525,6526,6527,6528,6529,6530,6531,6533,6534,6536,6537,6539,6541,6542,6544,6545,6546,6547,6548,6549,6551,6552,6553,6554,6555,6840,6842,6844,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6860,6861,6862,6863,6864,6865,6866,6868,6872,7044,7045,7046,7047,7048,7049,7053,7054,7055,7596,7598,7600,7602,7604,7606,7607,7608,7609,7611,7613,7615,7616,7617,7618,7619,7620,7621,7622,7623,7624,7625,7626,7629,7630,7631,7632,7634,7636,7637,7639,7640,7642,7644,7646,7647,7648,7649,7650,7651,7652,7653,7654,7655,7656,7657,7659,7660,7661,7662,7664,7665,7666,7667,7668,7670,7672,7674,7676,7677,7678,7679,7680,7681,7682,7683,7684,7685,7686,7687,7688,7689,7690", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6094,10548,10853,26690,28507,28562,29337,29401,29471,29532,29607,29683,29760,29998,30083,30165,30241,30359,30436,30514,30620,30726,30805,30885,30942,34474,34548,34623,34688,34754,34814,34875,34947,35020,35087,50646,50705,50764,50823,50882,50941,50995,51049,51102,51156,51210,51264,55656,55730,55809,55882,55956,56027,56099,56171,56594,56651,56709,56782,56856,56930,57005,57077,57150,57220,57381,57441,59296,59365,59434,59504,59578,59654,59718,59795,59871,59948,60013,60082,60159,60234,60303,60371,60448,60514,60575,60672,60737,60806,60905,60976,61035,61093,61150,61209,61273,61344,61416,61488,61560,61632,61699,61767,61835,61894,61957,62021,62111,62202,62262,62328,62395,62461,62531,62595,62648,62715,62776,62843,62956,63014,63077,63142,63207,63282,63355,63427,63471,63518,63564,63613,63674,63735,63796,63858,63922,63986,64050,64115,64178,64238,64299,64365,64424,64484,64546,64617,64677,68675,68761,69011,69101,69188,69276,69358,69441,69531,121092,121144,121202,121247,121313,121377,121434,121491,125432,125489,125537,125586,126136,128051,128098,128531,130316,130763,130827,131017,131077,136309,136383,136453,136531,136585,136655,136740,136788,136834,136895,136958,137024,137088,137159,137222,137287,137351,137412,137473,137525,137598,137672,137741,137816,137890,137964,138105,182129,189404,189482,189572,189660,189756,191265,191847,191936,192183,192464,193130,193415,195224,195701,195923,196145,196421,196648,196878,197108,197338,197568,197795,198214,198440,198865,199095,199523,199742,200025,200233,200364,200591,201017,201242,201669,201890,202315,202435,202711,203012,203336,203627,203941,204078,204209,204314,204556,204723,204927,205135,205406,205518,205630,205735,207828,208042,208188,208328,208414,208762,208850,209096,209514,209763,209845,209943,210600,210700,210952,211376,211631,217521,217610,217847,219871,220113,220215,220468,356037,366718,368234,378929,380457,382214,382840,383260,384521,385786,386042,386278,386825,387319,387924,388122,388702,390070,390445,390563,391101,391258,391454,391727,391983,392153,392294,392358,392723,393090,393766,394030,394368,394721,394815,395001,395307,395569,395694,395821,396060,396271,396390,396583,396760,397215,397396,397518,397777,397890,398077,398179,398286,398415,398690,399198,399694,400571,400865,401435,401584,402316,402488,402572,402908,403000,435149,440380,446095,446157,446735,447319,447661,447774,448003,448163,448315,448486,448652,448821,448988,449151,449394,449564,449737,449908,450182,450381,450586,459952,460036,460132,460228,460326,460426,460528,460630,460732,460834,460936,461036,461132,461244,461373,461496,461627,461758,461856,461970,462064,462204,462338,462434,462546,462646,462762,462858,462970,463070,463210,463346,463510,463640,463798,463948,464089,464233,464368,464480,464630,464758,464886,465022,465154,465284,465414,465526,483184,483330,483474,483612,483678,483768,483844,483948,484038,484140,484248,484356,484456,484536,484628,484726,484836,484888,484966,485072,485164,485268,485378,485500,485663,498823,498903,499003,499093,499203,499293,499534,499628,499734,539613,539713,539825,539939,540055,540171,540265,540379,540491,540593,540713,540835,540917,541021,541141,541267,541365,541459,541547,541659,541775,541897,542009,542184,542300,542386,542478,542590,542714,542781,542907,542975,543103,543247,543375,543444,543539,543654,543767,543866,543975,544086,544197,544298,544403,544503,544633,544724,544847,544941,545053,545139,545243,545339,545427,545545,545649,545753,545879,545967,546075,546175,546265,546375,546459,546561,546645,546699,546763,546869,546955,547065,547149", "endLines": "120,208,215,513,557,558,570,571,572,573,574,575,576,579,580,581,582,584,585,586,587,588,589,590,591,641,642,643,644,645,646,647,648,649,650,862,863,864,865,866,867,868,869,870,871,872,873,930,931,932,933,934,935,936,937,945,946,947,948,949,950,951,952,953,954,957,958,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1126,1127,1131,1132,1133,1134,1135,1136,1137,1795,1796,1797,1798,1799,1800,1801,1802,1863,1864,1865,1866,1876,1909,1910,1919,1953,1962,1963,1966,1967,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2510,2604,2605,2606,2607,2608,2637,2638,2642,2646,2650,2662,2668,2698,2702,2706,2711,2715,2719,2723,2727,2731,2735,2741,2745,2751,2755,2761,2765,2770,2774,2777,2781,2787,2791,2797,2801,2807,2810,2814,2818,2822,2826,2830,2831,2832,2833,2836,2839,2842,2845,2849,2850,2851,2852,2853,2896,2898,2900,2902,2907,2908,2912,2918,2922,2923,2925,2937,2938,2942,2948,2952,2953,3030,3034,3061,3065,3066,3070,3098,5044,5070,5241,5267,5298,5306,5312,5328,5350,5355,5360,5370,5379,5388,5392,5399,5418,5425,5426,5435,5438,5441,5445,5449,5453,5456,5457,5462,5467,5477,5482,5489,5495,5496,5499,5503,5508,5510,5512,5515,5518,5520,5524,5527,5534,5537,5540,5544,5546,5550,5552,5554,5556,5560,5568,5576,5588,5594,5603,5606,5617,5620,5621,5626,5627,5632,6201,6271,6276,6286,6295,6296,6302,6306,6309,6312,6315,6318,6321,6324,6327,6331,6334,6337,6340,6344,6347,6351,6355,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6512,6513,6514,6515,6516,6517,6518,6519,6521,6523,6524,6525,6526,6527,6528,6529,6530,6532,6533,6535,6536,6538,6540,6541,6543,6544,6545,6546,6547,6548,6550,6551,6552,6553,6554,6555,6841,6843,6845,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6859,6860,6861,6862,6863,6864,6865,6867,6871,6875,7044,7045,7046,7047,7048,7052,7053,7054,7055,7597,7599,7601,7603,7605,7606,7607,7608,7610,7612,7614,7615,7616,7617,7618,7619,7620,7621,7622,7623,7624,7625,7628,7629,7630,7631,7633,7635,7636,7638,7639,7641,7643,7645,7646,7647,7648,7649,7650,7651,7652,7653,7654,7655,7656,7658,7659,7660,7661,7663,7664,7665,7666,7667,7669,7671,7673,7675,7676,7677,7678,7679,7680,7681,7682,7683,7684,7685,7686,7687,7688,7689,7690", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6144,10588,10897,26726,28557,28619,29396,29466,29527,29602,29678,29755,29833,30078,30160,30236,30312,30431,30509,30615,30721,30800,30880,30937,30995,34543,34618,34683,34749,34809,34870,34942,35015,35082,35150,50700,50759,50818,50877,50936,50990,51044,51097,51151,51205,51259,51313,55725,55804,55877,55951,56022,56094,56166,56239,56646,56704,56777,56851,56925,57000,57072,57145,57215,57286,57436,57497,59360,59429,59499,59573,59649,59713,59790,59866,59943,60008,60077,60154,60229,60298,60366,60443,60509,60570,60667,60732,60801,60900,60971,61030,61088,61145,61204,61268,61339,61411,61483,61555,61627,61694,61762,61830,61889,61952,62016,62106,62197,62257,62323,62390,62456,62526,62590,62643,62710,62771,62838,62951,63009,63072,63137,63202,63277,63350,63422,63466,63513,63559,63608,63669,63730,63791,63853,63917,63981,64045,64110,64173,64233,64294,64360,64419,64479,64541,64612,64672,64740,68756,68843,69096,69183,69271,69353,69436,69526,69617,121139,121197,121242,121308,121372,121429,121486,121540,125484,125532,125581,125632,126165,128093,128142,128572,130343,130822,130884,131072,131129,136378,136448,136526,136580,136650,136735,136783,136829,136890,136953,137019,137083,137154,137217,137282,137346,137407,137468,137520,137593,137667,137736,137811,137885,137959,138100,138170,182177,189477,189567,189655,189751,189841,191842,191931,192178,192459,192711,193410,193803,195696,195918,196140,196416,196643,196873,197103,197333,197563,197790,198209,198435,198860,199090,199518,199737,200020,200228,200359,200586,201012,201237,201664,201885,202310,202430,202706,203007,203331,203622,203936,204073,204204,204309,204551,204718,204922,205130,205401,205513,205625,205730,205847,208037,208183,208323,208409,208757,208845,209091,209509,209758,209840,209938,210595,210695,210947,211371,211626,211720,217605,217842,219866,220108,220210,220463,222619,366713,368229,378924,380452,382209,382835,383255,384516,385781,386037,386273,386820,387314,387919,388117,388697,390065,390440,390558,391096,391253,391449,391722,391978,392148,392289,392353,392718,393085,393761,394025,394363,394716,394810,394996,395302,395564,395689,395816,396055,396266,396385,396578,396755,397210,397391,397513,397772,397885,398072,398174,398281,398410,398685,399193,399689,400566,400860,401430,401579,402311,402483,402567,402903,402995,403273,440375,445746,446152,446730,447314,447405,447769,447998,448158,448310,448481,448647,448816,448983,449146,449389,449559,449732,449903,450177,450376,450581,450911,460031,460127,460223,460321,460421,460523,460625,460727,460829,460931,461031,461127,461239,461368,461491,461622,461753,461851,461965,462059,462199,462333,462429,462541,462641,462757,462853,462965,463065,463205,463341,463505,463635,463793,463943,464084,464228,464363,464475,464625,464753,464881,465017,465149,465279,465409,465521,465661,483325,483469,483607,483673,483763,483839,483943,484033,484135,484243,484351,484451,484531,484623,484721,484831,484883,484961,485067,485159,485263,485373,485495,485658,485815,498898,498998,499088,499198,499288,499529,499623,499729,499821,539708,539820,539934,540050,540166,540260,540374,540486,540588,540708,540830,540912,541016,541136,541262,541360,541454,541542,541654,541770,541892,542004,542179,542295,542381,542473,542585,542709,542776,542902,542970,543098,543242,543370,543439,543534,543649,543762,543861,543970,544081,544192,544293,544398,544498,544628,544719,544842,544936,545048,545134,545238,545334,545422,545540,545644,545748,545874,545962,546070,546170,546260,546370,546454,546556,546640,546694,546758,546864,546950,547060,547144,547264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4ee0524a935fe4c924df4ba2e4e24000\\transformed\\leakcanary-android-core-2.14\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "562,563,564,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1880,1881,1882,1883,1884,1885,1886,1887,2033,2037,2041,2227,2228,2229,2230,2231,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,9750,9754,9761", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28823,28885,28944,35155,35218,35275,35337,35393,35474,35526,35577,35631,35685,35744,35809,35874,35931,35991,36046,36111,36173,36235,36297,36361,36424,36488,36545,36605,36672,36733,36795,36857,36921,36978,37041,37104,37162,37213,37264,37320,37372,37425,37485,69845,69907,69973,70040,70104,70163,70222,70275,70335,70390,70441,70507,70573,70644,70713,70772,126345,126415,126484,126551,126631,126700,126771,126842,135488,135650,135818,151930,152017,152096,152173,152241,152968,153040,153120,153197,153337,153406,153460,153522,153628,153697,153761,153837,153916,154009,154095,154202,154320,154400,154477,154573,154682,154800,154912,155021,155140,155237,155313,155387,155458,155533,155655,155737,155819,155911,155981,156044,156127,156216,156302,156376,156458,156553,156675,156786,156880,156994,157100,157217,157308,157448,157535,157679,157791,157892,157991,158054,158115,158204,158297,158389,158496,158600,158751,681294,681548,681933", "endLines": "562,563,564,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1880,1881,1882,1883,1884,1885,1886,1887,2036,2040,2044,2227,2228,2229,2230,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,9753,9760,9764", "endColumns": "61,58,71,62,56,61,55,80,51,50,53,53,58,64,64,56,59,54,64,61,61,61,63,62,63,56,59,66,60,61,61,63,56,62,62,57,50,50,55,51,52,59,67,61,65,66,63,58,58,52,59,54,50,65,65,70,68,58,64,69,68,66,79,68,70,70,60,12,12,12,86,78,76,67,12,71,79,76,139,68,53,61,105,68,63,75,78,92,85,106,117,79,76,95,108,117,111,108,118,96,75,73,70,74,121,81,81,91,69,62,82,88,85,73,81,94,121,110,93,113,105,116,90,139,86,143,111,100,98,62,60,88,92,91,106,103,150,170,10,10,10", "endOffsets": "28880,28939,29011,35213,35270,35332,35388,35469,35521,35572,35626,35680,35739,35804,35869,35926,35986,36041,36106,36168,36230,36292,36356,36419,36483,36540,36600,36667,36728,36790,36852,36916,36973,37036,37099,37157,37208,37259,37315,37367,37420,37480,37548,69902,69968,70035,70099,70158,70217,70270,70330,70385,70436,70502,70568,70639,70708,70767,70832,126410,126479,126546,126626,126695,126766,126837,126898,135645,135813,135984,152012,152091,152168,152236,152963,153035,153115,153192,153332,153401,153455,153517,153623,153692,153756,153832,153911,154004,154090,154197,154315,154395,154472,154568,154677,154795,154907,155016,155135,155232,155308,155382,155453,155528,155650,155732,155814,155906,155976,156039,156122,156211,156297,156371,156453,156548,156670,156781,156875,156989,157095,157212,157303,157443,157530,157674,157786,157887,157986,158049,158110,158199,158292,158384,158491,158595,158746,158917,681543,681928,682164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\887c9c1e0bbe1248378b1eec22ea3bb5\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1911,1957", "startColumns": "4,4", "startOffsets": "128147,130482", "endColumns": "41,59", "endOffsets": "128184,130537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\aaf9750c511786fa108e7a3c496402ad\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1970,2193", "startColumns": "4,4", "startOffsets": "131269,148615", "endColumns": "67,166", "endOffsets": "131332,148777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\303c2166503144bea901100ae0f040af\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2130,2580,2581", "startColumns": "4,4,4", "startOffsets": "142982,187608,187664", "endColumns": "45,55,54", "endOffsets": "143023,187659,187714"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\build\\generated\\res\\injectCrashlyticsMappingFileIdDebug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2184", "startColumns": "4", "startOffsets": "147399", "endColumns": "175", "endOffsets": "147570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\4e69add8b1376897dea52ac0f7720f60\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1867,1869,1870,1875,1877,1961,2180,2181,2205,2206,2207,2224,2225,2495,2496,2507,2508,2516,2566,2567,2568,2569,2572,2573,2575,6014,6031,6034", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125637,125762,125820,126085,126170,130710,147171,147236,150002,150068,150169,151759,151811,180746,180808,181975,182025,182690,186858,186912,186958,187000,187149,187196,187286,427314,428357,428468", "endLines": "1867,1869,1870,1875,1877,1961,2180,2181,2205,2206,2207,2224,2225,2495,2496,2507,2508,2516,2566,2567,2568,2569,2572,2573,2575,6016,6033,6038", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "125706,125815,125870,126131,126220,130758,147231,147285,150063,150164,150222,151806,151866,180803,180857,182020,182074,182731,186907,186953,186995,187035,187191,187227,187371,427421,428463,428720"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\search\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2132,2179", "startColumns": "4,4", "startOffsets": "143066,147101", "endColumns": "123,69", "endOffsets": "143185,147166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\7e3f2c5e9beccbf3f25b116a07f38db6\\transformed\\leakcanary-object-watcher-android-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "566", "startColumns": "4", "startOffsets": "29078", "endColumns": "61", "endOffsets": "29135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6100cf79e00b0bf6c9c6362128e7edd6\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1900", "startColumns": "4", "startOffsets": "127500", "endColumns": "52", "endOffsets": "127548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\48ffa0e1b44e6c5d14d02c946f3f174d\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,4,10,11,19,27,33,91,92,93,95,99,100,101,104,112,122,150,151,156,157,162,167,168,169,174,175,180,181,186,187,188,194,195,196,201,206,224,225,229,230,231,232,235,236,239,242,243,244,245,246,249,252,253,254,255,260,263,266,267,268,273,274,275,278,281,282,285,288,291,294,295,296,299,302,303,308,309,314,317,320,321,322,323,324,325,326,327,328,329,416,417,418,419,424,430,431,432,435,469,476,516,517,528,534,537,541,542,543,544,553,1893", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,263,498,559,850,1306,1613,4857,4909,4970,5077,5210,5262,5312,5473,5782,6205,8147,8206,8403,8460,8655,8837,8891,8948,9140,9198,9394,9450,9644,9701,9752,9974,10026,10081,10271,10447,11346,11402,11562,11623,11683,11753,11886,11954,12083,12209,12271,12336,12404,12471,12594,12719,12786,12851,12916,13097,13218,13339,13405,13472,13682,13751,13817,13942,14068,14135,14261,14388,14513,14640,14696,14761,14887,15010,15075,15283,15350,15530,15650,15770,15835,15897,15959,16021,16080,16140,16201,16262,16321,21481,21532,21581,21629,21916,22146,22193,22253,22359,24013,24360,26840,26892,27328,27566,27744,27883,27929,27984,28029,28370,127122", "endLines": "2,8,10,18,19,27,33,91,92,93,98,99,100,101,111,119,122,150,155,156,161,166,167,168,173,174,179,180,185,186,187,193,194,195,200,205,206,224,228,229,230,231,234,235,238,241,242,243,244,245,248,251,252,253,254,259,262,265,266,267,272,273,274,277,280,281,284,287,290,293,294,295,298,301,302,307,308,313,316,319,320,321,322,323,324,325,326,327,328,340,416,417,418,419,429,430,431,434,439,469,476,516,525,533,534,540,541,542,543,552,556,1893", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40", "endOffsets": "202,444,554,845,897,1349,1659,4904,4965,5011,5205,5257,5307,5358,5777,6089,6245,8201,8398,8455,8650,8832,8886,8943,9135,9193,9389,9445,9639,9696,9747,9969,10021,10076,10266,10442,10492,11397,11557,11618,11678,11748,11881,11949,12078,12204,12266,12331,12399,12466,12589,12714,12781,12846,12911,13092,13213,13334,13400,13467,13677,13746,13812,13937,14063,14130,14256,14383,14508,14635,14691,14756,14882,15005,15070,15278,15345,15525,15645,15765,15830,15892,15954,16016,16075,16135,16196,16257,16316,16784,21527,21576,21624,21682,22141,22188,22248,22354,22534,24054,24402,26887,27217,27561,27616,27878,27924,27979,28024,28365,28502,127158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d7ab330357aa20624881ed94e7768b51\\transformed\\plumber-android-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "565", "startColumns": "4", "startOffsets": "29016", "endColumns": "61", "endOffsets": "29073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\755b5a3a3bd78573c96197752104b9b1\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "121,124,1077", "startColumns": "4,4,4", "startOffsets": "6149,6313,65523", "endColumns": "55,47,51", "endOffsets": "6200,6356,65570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\02a224bf1e10a1e6b2e19a748804e152\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "26,594,595,596,597,1066,1067,1068,2651,6006,6008,6011", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,31131,31192,31254,31316,64810,64869,64926,192716,426996,427060,427186", "endLines": "26,594,595,596,597,1066,1067,1068,2657,6007,6010,6013", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1301,31187,31249,31311,31375,64864,64921,64975,193125,427055,427181,427309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\1ba516b7f7e560d6a18c60f8777e4347\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1868", "startColumns": "4", "startOffsets": "125711", "endColumns": "50", "endOffsets": "125757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f3e154f6c2797116b6a57a346ec405cd\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1878,1908", "startColumns": "4,4", "startOffsets": "126225,127984", "endColumns": "53,66", "endOffsets": "126274,128046"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "583,938,939,940,955,956,983", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "30317,56244,56291,56338,57291,57336,59254", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "30354,56286,56333,56380,57331,57376,59291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a91968b4dc7cf6f01038e7dd4d63f60d\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "441,1128,1129,1130,1138,1139,1140,1879", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22597,68848,68907,68955,69622,69697,69773,126279", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "22648,68902,68950,69006,69692,69768,69840,126340"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2192,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,78,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2266,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2077,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2131,2139,2147,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2182,2183,2203,2204,2208,2209,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2497,2503,2509,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2571,2574,2576,2577,2578,2579,2582,2583,2584,2585,2586,2587,2588,2589,2590,2603", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "138258,138454,138505,138566,138631,138718,138779,138840,138912,138969,139040,139130,139390,139475,139536,139605,139678,139757,139814,139881,139952,140019,140088,140171,140405,140478,140560,140637,140694,140761,141027,141090,141167,141220,141291,141401,141458,141529,141597,141658,141720,141790,141857,141934,142009,142084,142299,142375,142670,142738,142793,142858,143028,144279,144845,145195,145277,145329,145395,145474,145554,145624,145676,145736,145836,145902,145965,146063,146128,146198,146257,146325,146381,146443,146515,146587,146657,146705,146777,146855,146905,146963,147290,147344,149908,149946,150227,150265,169171,169255,169321,169401,169475,169545,169625,169712,169762,169826,169895,169944,170003,170061,170163,170228,180862,181743,182079,182736,182774,182918,183019,183147,183244,183369,183458,183569,183650,183722,183791,183859,183938,184019,184072,184139,184200,184296,184363,184444,184507,184569,184633,184699,184757,184880,184969,185038,185134,185208,185294,185363,185441,185516,185598,185673,185734,185813,185884,185967,186020,186100,186230,186310,186394,186464,187111,187232,187376,187435,187491,187549,187719,187777,187866,187932,188002,188103,188180,188241,188305,189346", "endColumns": "45,50,60,64,86,60,60,71,56,70,89,259,84,60,68,72,78,56,66,70,66,68,82,233,72,81,76,56,66,265,62,76,52,70,109,56,70,67,60,61,69,66,76,74,74,214,75,294,67,54,64,123,37,54,51,81,51,65,78,79,69,51,59,99,65,62,97,64,69,58,67,55,61,71,71,69,47,71,77,49,57,58,53,54,37,55,37,43,83,65,79,73,69,79,86,49,63,68,48,58,57,101,64,53,78,39,49,37,143,100,127,96,124,88,110,80,71,68,67,78,80,52,66,60,95,66,80,62,61,63,65,57,122,88,68,95,73,85,68,77,74,81,74,60,78,70,82,52,79,129,79,83,69,45,37,53,58,55,57,58,57,88,65,69,100,76,60,63,35,57", "endOffsets": "138299,138500,138561,138626,138713,138774,138835,138907,138964,139035,139125,139385,139470,139531,139600,139673,139752,139809,139876,139947,140014,140083,140166,140400,140473,140555,140632,140689,140756,141022,141085,141162,141215,141286,141396,141453,141524,141592,141653,141715,141785,141852,141929,142004,142079,142294,142370,142665,142733,142788,142853,142977,143061,144329,144892,145272,145324,145390,145469,145549,145619,145671,145731,145831,145897,145960,146058,146123,146193,146252,146320,146376,146438,146510,146582,146652,146700,146772,146850,146900,146958,147017,147339,147394,149941,149997,150260,150304,169250,169316,169396,169470,169540,169620,169707,169757,169821,169890,169939,169998,170056,170158,170223,170277,180936,181778,182124,182769,182913,183014,183142,183239,183364,183453,183564,183645,183717,183786,183854,183933,184014,184067,184134,184195,184291,184358,184439,184502,184564,184628,184694,184752,184875,184964,185033,185129,185203,185289,185358,185436,185511,185593,185668,185729,185808,185879,185962,186015,186095,186225,186305,186389,186459,186505,187144,187281,187430,187486,187544,187603,187772,187861,187927,187997,188098,188175,188236,188300,188336,189399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\e6e7ee247c83f269d48cbfd5490c1df4\\transformed\\transition-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1872,1873,1907,1913,1914,1945,1946,1947,1948,1949,1950,1951,1952", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125932,125972,127941,128232,128287,129897,129942,129996,130052,130104,130156,130205,130266", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "125967,126014,127979,128282,128329,129937,129991,130047,130099,130151,130200,130261,130311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\3e808f1588c4ca551309bafece8a7467\\transformed\\camera-view-1.5.0-beta01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "211,443", "startColumns": "4,4", "startOffsets": "10701,22699", "endLines": "214,450", "endColumns": "11,11", "endOffsets": "10848,23001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8bf40832b048d1f4d2354372c996192d\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2001,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,6017,6028", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "133270,160839,160927,161013,161094,161178,161247,161312,161395,161501,161587,161707,161761,161830,161891,161960,162049,162144,162218,162315,162408,162506,162655,162746,162834,162930,163028,163092,163160,163247,163341,163408,163480,163552,163653,163762,163838,163907,163955,164021,164085,164159,164234,164305,164362,164419,164477,164549,164616,164666,164735,164789,164860,164931,165001,165070,165128,165204,165275,165349,165435,165485,165590,165687,165757,165822,165899,427426,428204", "endLines": "2001,2318,2319,2320,2321,2322,2323,2324,2325,2326,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,6027,6030", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,74,70,56,56,57,71,66,49,68,53,70,70,69,68,57,75,70,73,85,49,104,96,69,64,76,81,12,12", "endOffsets": "133338,160922,161008,161089,161173,161242,161307,161390,161496,161582,161702,161756,161825,161886,161955,162044,162139,162213,162310,162403,162501,162650,162741,162829,162925,163023,163087,163155,163242,163336,163403,163475,163547,163648,163757,163833,163902,163950,164016,164080,164154,164229,164300,164357,164414,164472,164544,164611,164661,164730,164784,164855,164926,164996,165065,165123,165199,165270,165344,165430,165480,165585,165682,165752,165817,165894,165976,428199,428352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c95c368326577e60c73af6c9ba10d0f5\\transformed\\leakcanary-object-watcher-android-core-2.14\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "567", "startColumns": "4", "startOffsets": "29140", "endColumns": "73", "endOffsets": "29209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d2e6b12382a4dadf34d77e521366ccff\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "598,599,600,601,602,603,604,605,2185,2186,2187,2188,2189,2190,2191,2192,2194,2195,2196,2197,2198,2199,2200,2201,2202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31380,31470,31550,31640,31730,31810,31891,31971,147575,147680,147861,147986,148093,148273,148396,148512,148782,148970,149075,149256,149381,149556,149704,149767,149829", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "31465,31545,31635,31725,31805,31886,31966,32046,147675,147856,147981,148088,148268,148391,148507,148610,148965,149070,149251,149376,149551,149699,149762,149824,149903"}}, {"source": "D:\\FromTX3\\MyCode\\rikkahub\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7043", "startColumns": "4", "startOffsets": "498738", "endColumns": "84", "endOffsets": "498818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dfed636760b934f8795b2c5bd3acd456\\transformed\\fragment-1.8.5\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1871,1918,1960", "startColumns": "4,4,4", "startOffsets": "125875,128466,130646", "endColumns": "56,64,63", "endOffsets": "125927,128526,130705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\511d54b94fbbdd77f26be08a0883aa32\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1955", "startColumns": "4", "startOffsets": "130396", "endColumns": "42", "endOffsets": "130434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\835032a51b5d40afa0ab30aa91122c7e\\transformed\\quickie-bundled-1.10.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "941,942,943,944,2505,2506,6297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "56385,56443,56490,56546,181852,181913,447410", "endLines": "941,942,943,944,2505,2506,6300", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "56438,56485,56541,56589,181908,181970,447656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\d642686678dc9e4dc645c444fc2595b8\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "423,577,578,592,593,928,929,1070,1071,1072,1073,1074,1075,1076,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1888,1889,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1944,2031,2140,2141,2142,2143,2144,2145,2146,2570,6556,6557,6561,6562,6566,7691,7692", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21856,29838,29910,31000,31065,55524,55593,65035,65105,65173,65245,65315,65376,65450,120114,120175,120236,120298,120362,120424,120485,120553,120653,120713,120779,120852,120921,120978,121030,123309,123381,123457,123522,123581,123640,123700,123760,123820,123880,123940,124000,124060,124120,124180,124240,124299,124359,124419,124479,124539,124599,124659,124719,124779,124839,124899,124958,125018,125078,125137,125196,125255,125314,125373,126903,126938,128577,128632,128695,128750,128808,128864,128922,128983,129046,129103,129154,129212,129262,129323,129380,129446,129480,129862,135350,144334,144401,144473,144542,144611,144685,144757,187040,465666,465783,465984,466094,466295,547269,547341", "endLines": "423,577,578,592,593,928,929,1070,1071,1072,1073,1074,1075,1076,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1888,1889,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1944,2031,2140,2141,2142,2143,2144,2145,2146,2570,6556,6560,6561,6565,6566,7691,7692", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "21911,29905,29993,31060,31126,55588,55651,65100,65168,65240,65310,65371,65445,65518,120170,120231,120293,120357,120419,120480,120548,120648,120708,120774,120847,120916,120973,121025,121087,123376,123452,123517,123576,123635,123695,123755,123815,123875,123935,123995,124055,124115,124175,124235,124294,124354,124414,124474,124534,124594,124654,124714,124774,124834,124894,124953,125013,125073,125132,125191,125250,125309,125368,125427,126933,126968,128627,128690,128745,128803,128859,128917,128978,129041,129098,129149,129207,129257,129318,129375,129441,129475,129510,129892,135415,144396,144468,144537,144606,144680,144752,144840,187106,465778,465979,466089,466290,466419,547336,547403"}}]}]}