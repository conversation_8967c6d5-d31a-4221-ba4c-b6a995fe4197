me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f120276
me.rerere.rikkahub.debug:dimen/design_bottom_sheet_elevation = 0x7f06006b
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c002f
me.rerere.rikkahub.debug:styleable/MaterialCardView = 0x7f130054
me.rerere.rikkahub.debug:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601ce
me.rerere.rikkahub.debug:style/Widget.Material3.TabLayout.Secondary = 0x7f1203dd
me.rerere.rikkahub.debug:string/mtrl_picker_announce_current_selection = 0x7f11017c
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1202e0
me.rerere.rikkahub.debug:string/chat_page_edit_title_warning = 0x7f11006d
me.rerere.rikkahub.debug:style/Platform.AppCompat.Light = 0x7f12013c
me.rerere.rikkahub.debug:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f12010a
me.rerere.rikkahub.debug:styleable/Slider = 0x7f130086
me.rerere.rikkahub.debug:layout/quickie_overlay_view = 0x7f0b0076
me.rerere.rikkahub.debug:styleable/ClockFaceView = 0x7f130021
me.rerere.rikkahub.debug:layout/mtrl_search_view = 0x7f0b006f
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1202ad
me.rerere.rikkahub.debug:style/TextAppearance.Compat.Notification.Info = 0x7f1201ca
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f120036
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1203b3
me.rerere.rikkahub.debug:dimen/material_clock_number_text_size = 0x7f06023a
me.rerere.rikkahub.debug:styleable/ViewPager2 = 0x7f13009d
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1202b1
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.SeekBar = 0x7f1200f4
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f12038a
me.rerere.rikkahub.debug:string/mermaid_export_success = 0x7f110163
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1203a6
me.rerere.rikkahub.debug:style/Widget.AppCompat.EditText = 0x7f120305
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c005f
me.rerere.rikkahub.debug:style/Widget.AppCompat.ImageButton = 0x7f120306
me.rerere.rikkahub.debug:id/reverseSawtooth = 0x7f08019e
me.rerere.rikkahub.debug:styleable/JLatexMathView = 0x7f13003e
me.rerere.rikkahub.debug:string/leak_canary_notification_channel_low = 0x7f1100d6
me.rerere.rikkahub.debug:style/Widget.Design.Snackbar = 0x7f12033f
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.None = 0x7f120179
me.rerere.rikkahub.debug:dimen/clock_face_margin_start = 0x7f060055
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f120195
me.rerere.rikkahub.debug:integer/m3_card_anim_duration_ms = 0x7f09000e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f12044e
me.rerere.rikkahub.debug:string/assistant_page_body_value = 0x7f110024
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_disabled_from_ui = 0x7f1100c6
me.rerere.rikkahub.debug:styleable/MaterialAlertDialogTheme = 0x7f13004e
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_short1 = 0x7f09001c
me.rerere.rikkahub.debug:string/indeterminate = 0x7f1100ab
me.rerere.rikkahub.debug:id/edit_query = 0x7f0800a1
me.rerere.rikkahub.debug:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700cd
me.rerere.rikkahub.debug:styleable/Toolbar = 0x7f130096
me.rerere.rikkahub.debug:string/m3c_bottom_sheet_collapse_description = 0x7f110101
me.rerere.rikkahub.debug:string/translator_page_translate = 0x7f110204
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1202dd
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.SideSheetDialog = 0x7f120234
me.rerere.rikkahub.debug:string/ucrop_label_edit_photo = 0x7f11020a
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_long2 = 0x7f090015
me.rerere.rikkahub.debug:styleable/RangeSlider = 0x7f13007a
me.rerere.rikkahub.debug:styleable/MaterialDivider = 0x7f130057
me.rerere.rikkahub.debug:styleable/SnackbarLayout = 0x7f130088
me.rerere.rikkahub.debug:string/notification_channel_chat_completed = 0x7f1101aa
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1202b0
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_icon_path_name = 0x7f11016e
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight = 0x7f12024f
me.rerere.rikkahub.debug:style/Widget.Material3.Button = 0x7f120355
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f120406
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120273
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1201b4
me.rerere.rikkahub.debug:id/tag_unhandled_key_event_manager = 0x7f0801f4
me.rerere.rikkahub.debug:style/Base.Widget.Material3.Chip = 0x7f120101
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f12008e
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f12007d
me.rerere.rikkahub.debug:styleable/MotionLayout = 0x7f130065
me.rerere.rikkahub.debug:string/assistant_page_context_message_desc = 0x7f110028
me.rerere.rikkahub.debug:id/aligned = 0x7f080047
me.rerere.rikkahub.debug:styleable/Capability = 0x7f13001a
me.rerere.rikkahub.debug:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0109
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f12026e
me.rerere.rikkahub.debug:color/quickie_accent_fallback = 0x7f050330
me.rerere.rikkahub.debug:macro/m3_comp_elevated_button_container_color = 0x7f0c0029
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f12016b
me.rerere.rikkahub.debug:styleable/BaseProgressIndicator = 0x7f130015
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1201ad
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1202e7
me.rerere.rikkahub.debug:styleable/AppCompatTheme = 0x7f130013
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f120264
me.rerere.rikkahub.debug:string/setting_page_config_api_title = 0x7f1101d6
me.rerere.rikkahub.debug:styleable/AppCompatTextView = 0x7f130012
me.rerere.rikkahub.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120013
me.rerere.rikkahub.debug:styleable/AppBarLayout = 0x7f13000b
me.rerere.rikkahub.debug:styleable/MaterialTextAppearance = 0x7f13005b
me.rerere.rikkahub.debug:style/Widget.Support.CoordinatorLayout = 0x7f12046c
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f12041d
me.rerere.rikkahub.debug:id/transition_position = 0x7f080221
me.rerere.rikkahub.debug:string/m3c_tooltip_pane_description = 0x7f110140
me.rerere.rikkahub.debug:styleable/ViewStubCompat = 0x7f13009e
me.rerere.rikkahub.debug:string/leak_canary_toast_heap_dump = 0x7f1100ed
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06016d
me.rerere.rikkahub.debug:string/mtrl_checkbox_state_description_unchecked = 0x7f110175
me.rerere.rikkahub.debug:styleable/StateListDrawableItem = 0x7f13008c
me.rerere.rikkahub.debug:id/mtrl_picker_text_input_range_start = 0x7f08015f
me.rerere.rikkahub.debug:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f120105
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomSheet = 0x7f120401
me.rerere.rikkahub.debug:string/leak_canary_about_title = 0x7f1100b2
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f120459
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1202cc
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Surface = 0x7f120399
me.rerere.rikkahub.debug:styleable/PopupWindow = 0x7f130075
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
me.rerere.rikkahub.debug:drawable/ic_call_decline = 0x7f0700a0
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Dark = 0x7f12029a
me.rerere.rikkahub.debug:styleable/TextInputLayout = 0x7f130094
me.rerere.rikkahub.debug:id/accessibility_custom_action_15 = 0x7f080017
me.rerere.rikkahub.debug:string/take_picture = 0x7f1101f5
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f120064
me.rerere.rikkahub.debug:styleable/MaterialAutoCompleteTextView = 0x7f13004f
me.rerere.rikkahub.debug:id/leak_canary_list = 0x7f080101
me.rerere.rikkahub.debug:style/AlertDialog.AppCompat.Light = 0x7f120001
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f120450
me.rerere.rikkahub.debug:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0058
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0066
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f120111
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1202ac
me.rerere.rikkahub.debug:id/accessibility_custom_action_20 = 0x7f08001d
me.rerere.rikkahub.debug:dimen/material_bottom_sheet_max_width = 0x7f060231
me.rerere.rikkahub.debug:macro/m3_comp_fab_surface_icon_color = 0x7f0c003e
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f120396
me.rerere.rikkahub.debug:layout/mtrl_calendar_vertical = 0x7f0b005f
me.rerere.rikkahub.debug:string/leak_canary_notification_retained_debugger_attached = 0x7f1100dc
me.rerere.rikkahub.debug:styleable/NavGraphNavigator = 0x7f13006b
me.rerere.rikkahub.debug:string/m3c_date_picker_no_selection_description = 0x7f110111
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1201be
me.rerere.rikkahub.debug:layout/mtrl_calendar_month_labeled = 0x7f0b005c
me.rerere.rikkahub.debug:id/leak_canary_search_button = 0x7f080116
me.rerere.rikkahub.debug:style/Widget.Design.FloatingActionButton = 0x7f12033c
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1202d8
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f12043f
me.rerere.rikkahub.debug:style/Widget.Material3.CollapsingToolbar = 0x7f12037f
me.rerere.rikkahub.debug:styleable/RecyclerView = 0x7f13007c
me.rerere.rikkahub.debug:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1200bf
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f120395
me.rerere.rikkahub.debug:id/image_view_state_rotate = 0x7f0800db
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f120175
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f12043a
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f120451
me.rerere.rikkahub.debug:id/open_search_view_dummy_toolbar = 0x7f080179
me.rerere.rikkahub.debug:styleable/Navigator = 0x7f130072
me.rerere.rikkahub.debug:style/Widget.Material3.Button.OutlinedButton = 0x7f12035d
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Subhead = 0x7f1201b1
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f120250
me.rerere.rikkahub.debug:dimen/material_clock_hand_stroke_width = 0x7f060239
me.rerere.rikkahub.debug:id/open_search_view_edit_text = 0x7f08017a
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f12040e
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1200c9
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1203f4
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120270
me.rerere.rikkahub.debug:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f120383
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f12039a
me.rerere.rikkahub.debug:string/setting_page_theme_type_high_contrast = 0x7f1101e7
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActivityChooserView = 0x7f1202f6
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1202a4
me.rerere.rikkahub.debug:id/triangle = 0x7f080224
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f120408
me.rerere.rikkahub.debug:string/mtrl_picker_invalid_format_example = 0x7f110186
me.rerere.rikkahub.debug:styleable/LinearProgressIndicator = 0x7f13004a
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009a
me.rerere.rikkahub.debug:style/Widget.Material3.Toolbar.Surface = 0x7f1203ec
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1203f0
me.rerere.rikkahub.debug:id/marquee = 0x7f080127
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f120165
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f120438
me.rerere.rikkahub.debug:dimen/m3_card_elevated_elevation = 0x7f0600f8
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.Light = 0x7f120283
me.rerere.rikkahub.debug:styleable/RadialViewGroup = 0x7f130079
me.rerere.rikkahub.debug:drawable/leak_canary_icon_monochrome = 0x7f0700b3
me.rerere.rikkahub.debug:id/layout_contrast_bar = 0x7f0800ed
me.rerere.rikkahub.debug:dimen/abc_text_size_headline_material = 0x7f060047
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
me.rerere.rikkahub.debug:id/text_input_end_icon = 0x7f0801fe
me.rerere.rikkahub.debug:string/setting_page_default_model = 0x7f1101d7
me.rerere.rikkahub.debug:string/leak_canary_help_title = 0x7f1100cc
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.NoActionBar = 0x7f12023b
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1202e1
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton.Dialog = 0x7f120360
me.rerere.rikkahub.debug:style/TextAppearance.Design.Counter = 0x7f1201cf
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f120465
me.rerere.rikkahub.debug:id/rotate_scroll_wheel = 0x7f0801a3
me.rerere.rikkahub.debug:string/cancel_edit = 0x7f110062
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c1
me.rerere.rikkahub.debug:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f12039d
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c008e
me.rerere.rikkahub.debug:style/Widget.Design.TabLayout = 0x7f120340
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1203c0
me.rerere.rikkahub.debug:styleable/BottomNavigationView = 0x7f130017
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1203bf
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1203a1
me.rerere.rikkahub.debug:drawable/notification_bg = 0x7f0700fa
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f12014f
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_path_pressed = 0x7f1101a2
me.rerere.rikkahub.debug:dimen/abc_search_view_preferred_width = 0x7f060037
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker = 0x7f1203b8
me.rerere.rikkahub.debug:dimen/m3_btn_icon_only_default_padding = 0x7f0600e3
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f12028d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Tooltip = 0x7f12046b
me.rerere.rikkahub.debug:style/Widget.Material3.CompoundButton.CheckBox = 0x7f120382
me.rerere.rikkahub.debug:style/Widget.Material3.Button.IconButton.Outlined = 0x7f12035c
me.rerere.rikkahub.debug:drawable/ucrop_ic_reset = 0x7f07011b
me.rerere.rikkahub.debug:styleable/Badge = 0x7f130014
me.rerere.rikkahub.debug:color/mtrl_btn_text_btn_ripple_color = 0x7f0502f0
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker = 0x7f12045d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f120425
me.rerere.rikkahub.debug:color/vector_tint_theme_color = 0x7f05035e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Chip.Choice = 0x7f120412
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f120094
me.rerere.rikkahub.debug:style/Widget.Material3.CardView.Elevated = 0x7f120368
me.rerere.rikkahub.debug:styleable/NavigationBarActiveIndicator = 0x7f13006e
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f120136
me.rerere.rikkahub.debug:dimen/m3_navigation_item_horizontal_padding = 0x7f0601d0
me.rerere.rikkahub.debug:dimen/highlight_alpha_material_light = 0x7f060095
me.rerere.rikkahub.debug:id/outward = 0x7f080183
me.rerere.rikkahub.debug:id/accessibility_custom_action_18 = 0x7f08001a
me.rerere.rikkahub.debug:dimen/m3_btn_translation_z_hovered = 0x7f0600f3
me.rerere.rikkahub.debug:styleable/MaterialCalendarItem = 0x7f130053
me.rerere.rikkahub.debug:style/Widget.Material3.Button.UnelevatedButton = 0x7f120367
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f120313
me.rerere.rikkahub.debug:drawable/ucrop_ic_brightness_unselected = 0x7f070113
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_standard_decelerate = 0x7f110100
me.rerere.rikkahub.debug:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f12034d
me.rerere.rikkahub.debug:string/mtrl_picker_announce_current_selection_none = 0x7f11017d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f120456
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f120439
me.rerere.rikkahub.debug:styleable/Insets = 0x7f13003d
me.rerere.rikkahub.debug:styleable/ConstraintLayout_Layout = 0x7f130028
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Display4 = 0x7f12001e
me.rerere.rikkahub.debug:macro/m3_comp_filled_card_container_shape = 0x7f0c0047
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f12044c
me.rerere.rikkahub.debug:style/Widget.Design.CollapsingToolbar = 0x7f12033b
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionBar.Solid = 0x7f1202ee
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f120417
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1203df
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1201df
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f12023c
me.rerere.rikkahub.debug:string/leak_canary_notification_dumping = 0x7f1100d8
me.rerere.rikkahub.debug:style/Widget.Design.BottomNavigationView = 0x7f120339
me.rerere.rikkahub.debug:color/material_personalized_color_error_container = 0x7f0502b4
me.rerere.rikkahub.debug:string/mtrl_switch_track_path = 0x7f1101a5
me.rerere.rikkahub.debug:string/chat_page_search_placeholder = 0x7f11007a
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Button = 0x7f120186
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f120070
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070018
me.rerere.rikkahub.debug:style/Widget.AppCompat.ListView = 0x7f12031f
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1203a4
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1201c1
me.rerere.rikkahub.debug:styleable/FloatingActionButton_Behavior_Layout = 0x7f130033
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f120222
me.rerere.rikkahub.debug:string/abc_capital_on = 0x7f110007
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1202a2
me.rerere.rikkahub.debug:style/Widget.Material3.ActionBar.Solid = 0x7f120343
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f12002a
me.rerere.rikkahub.debug:layout/abc_search_view = 0x7f0b0019
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f120083
me.rerere.rikkahub.debug:style/TextAppearance.Compat.Notification.Line2 = 0x7f1201cb
me.rerere.rikkahub.debug:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f120357
me.rerere.rikkahub.debug:styleable/Snackbar = 0x7f130087
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f120308
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f12018e
me.rerere.rikkahub.debug:id/leak_canary_notification_retained_objects = 0x7f08010f
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_previous_month = 0x7f110118
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1203e5
me.rerere.rikkahub.debug:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f120247
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationView = 0x7f120442
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1202aa
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f12037c
me.rerere.rikkahub.debug:styleable/ColorStateListItem = 0x7f130025
me.rerere.rikkahub.debug:style/Widget.Material3.Badge = 0x7f12034a
me.rerere.rikkahub.debug:color/ucrop_color_default_dimmed = 0x7f05034b
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button = 0x7f1202f8
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f120238
me.rerere.rikkahub.debug:styleable/KeyTimeCycle = 0x7f130045
me.rerere.rikkahub.debug:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1202f7
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f120466
me.rerere.rikkahub.debug:string/translator_page_result_placeholder = 0x7f110201
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1202f4
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1202e5
me.rerere.rikkahub.debug:styleable/Layout = 0x7f130047
me.rerere.rikkahub.debug:color/mtrl_switch_thumb_icon_tint = 0x7f050314
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c008f
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Bridge = 0x7f12026a
me.rerere.rikkahub.debug:dimen/m3_btn_dialog_btn_min_width = 0x7f0600db
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f12015f
me.rerere.rikkahub.debug:style/Base.V22.Theme.AppCompat = 0x7f1200ad
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.BottomAppBar = 0x7f12028c
me.rerere.rikkahub.debug:style/Widget.Material3.Light.ActionBar.Solid = 0x7f12039b
me.rerere.rikkahub.debug:style/Widget.Material3.DrawerLayout = 0x7f120386
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1202e4
me.rerere.rikkahub.debug:string/m3c_time_picker_toggle_keyboard = 0x7f11013d
me.rerere.rikkahub.debug:id/navigation_bar_item_labels_group = 0x7f080167
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1202e3
me.rerere.rikkahub.debug:style/Widget.AppCompat.TextView = 0x7f120332
me.rerere.rikkahub.debug:dimen/ucrop_progress_size = 0x7f060343
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight = 0x7f120235
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f120187
me.rerere.rikkahub.debug:string/ucrop_contrast = 0x7f110207
me.rerere.rikkahub.debug:layout/ucrop_layout_saturation_wheel = 0x7f0b0083
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1202d6
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1202d4
me.rerere.rikkahub.debug:id/item_touch_helper_previous_elevation = 0x7f0800e6
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.IconButton = 0x7f120292
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1202d0
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f120409
me.rerere.rikkahub.debug:id/accessibility_custom_action_8 = 0x7f08002e
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f12016a
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f12003f
me.rerere.rikkahub.debug:styleable/NavArgument = 0x7f130069
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomNavigationView = 0x7f1203fe
me.rerere.rikkahub.debug:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007b
me.rerere.rikkahub.debug:string/fab_transformation_scrim_behavior = 0x7f1100a1
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1202c7
me.rerere.rikkahub.debug:style/TextAppearance.Compat.Notification = 0x7f1201c9
me.rerere.rikkahub.debug:drawable/ucrop_ic_contrast = 0x7f070114
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1202c2
me.rerere.rikkahub.debug:style/Widget.Material3.LinearProgressIndicator = 0x7f12039c
me.rerere.rikkahub.debug:id/parallax = 0x7f080186
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Snackbar = 0x7f1202bc
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f12044f
me.rerere.rikkahub.debug:id/top = 0x7f080216
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f120098
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1202bb
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_text_size = 0x7f0602e3
me.rerere.rikkahub.debug:string/abc_shareactionprovider_share_with = 0x7f110018
me.rerere.rikkahub.debug:styleable/ScrollingViewBehavior_Layout = 0x7f13007f
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1202b7
me.rerere.rikkahub.debug:id/line3 = 0x7f080122
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f12029c
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Chip.Assist = 0x7f120299
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f120297
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f120288
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f060208
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1202c4
me.rerere.rikkahub.debug:id/tag_window_insets_animation_callback = 0x7f0801f6
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f120287
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0091
me.rerere.rikkahub.debug:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200ab
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1201e6
me.rerere.rikkahub.debug:string/theme_name_spring = 0x7f1101fa
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1203b7
me.rerere.rikkahub.debug:string/setting_page_share_desc = 0x7f1101e5
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f1202fa
me.rerere.rikkahub.debug:dimen/mtrl_calendar_year_width = 0x7f0602aa
me.rerere.rikkahub.debug:dimen/m3_comp_slider_active_handle_width = 0x7f060193
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f120419
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Slider = 0x7f120449
me.rerere.rikkahub.debug:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1201f9
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d1
me.rerere.rikkahub.debug:styleable/ucrop_UCropView = 0x7f1300a1
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f120279
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f120436
me.rerere.rikkahub.debug:style/TextAppearance.Material3.LabelSmall = 0x7f1201f5
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f12026b
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_date_range_start_hint = 0x7f110195
me.rerere.rikkahub.debug:style/Base.TextAppearance.Material3.Search = 0x7f120043
me.rerere.rikkahub.debug:id/action_bar_subtitle = 0x7f080035
me.rerere.rikkahub.debug:color/material_dynamic_secondary0 = 0x7f05027e
me.rerere.rikkahub.debug:styleable/NavDeepLink = 0x7f13006a
me.rerere.rikkahub.debug:string/m3c_time_picker_minute_suffix = 0x7f110139
me.rerere.rikkahub.debug:style/Widget.Design.TextInputEditText = 0x7f120341
me.rerere.rikkahub.debug:styleable/ActionBarLayout = 0x7f130001
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f120265
me.rerere.rikkahub.debug:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602d6
me.rerere.rikkahub.debug:id/snap = 0x7f0801c9
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1202e9
me.rerere.rikkahub.debug:attr/circularProgressIndicatorStyle = 0x7f0300c6
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602ef
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.Dialog = 0x7f12022f
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f12038f
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Tooltip = 0x7f1201b5
me.rerere.rikkahub.debug:styleable/FontFamily = 0x7f130035
me.rerere.rikkahub.debug:style/Widget.AppCompat.SeekBar.Discrete = 0x7f12032d
me.rerere.rikkahub.debug:string/quickie_please_wait = 0x7f1101b2
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f120240
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f120232
me.rerere.rikkahub.debug:id/mtrl_calendar_text_input_frame = 0x7f080152
me.rerere.rikkahub.debug:style/QuickieScannerActivity = 0x7f120149
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.NoActionBar = 0x7f120278
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f12029f
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1203f8
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f120219
me.rerere.rikkahub.debug:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
me.rerere.rikkahub.debug:dimen/abc_action_button_min_width_material = 0x7f06000e
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1200f7
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f120217
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1202de
me.rerere.rikkahub.debug:string/assistant_page_tab_prompt = 0x7f110044
me.rerere.rikkahub.debug:string/m3c_time_picker_hour_suffix = 0x7f110135
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f120041
me.rerere.rikkahub.debug:macro/m3_comp_snackbar_container_color = 0x7f0c0113
me.rerere.rikkahub.debug:layout/design_layout_tab_text = 0x7f0b0022
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline5 = 0x7f120207
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline2 = 0x7f120204
me.rerere.rikkahub.debug:id/parentPanel = 0x7f080188
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_icon_path_group_name = 0x7f11016c
me.rerere.rikkahub.debug:id/dragUp = 0x7f08009b
me.rerere.rikkahub.debug:string/character_counter_content_description = 0x7f110063
me.rerere.rikkahub.debug:layout/leak_canary_leak_row = 0x7f0b0038
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1202c3
me.rerere.rikkahub.debug:style/TextAppearance.Material3.TitleLarge = 0x7f1201fa
me.rerere.rikkahub.debug:style/Widget.AppCompat.DrawerArrowToggle = 0x7f120303
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f12041c
me.rerere.rikkahub.debug:id/packed = 0x7f080185
me.rerere.rikkahub.debug:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f120323
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Body1 = 0x7f1201fe
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f12045a
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f12040b
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0119
me.rerere.rikkahub.debug:style/TextAppearance.Material3.SearchView = 0x7f1201f8
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600da
me.rerere.rikkahub.debug:string/m3c_wide_navigation_rail_pane_title = 0x7f110142
me.rerere.rikkahub.debug:styleable/leak_canary_MoreDetailsView = 0x7f13009f
me.rerere.rikkahub.debug:color/ucrop_color_toolbar = 0x7f050353
me.rerere.rikkahub.debug:style/TextAppearance.Material3.DisplayMedium = 0x7f1201ee
me.rerere.rikkahub.debug:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f120103
me.rerere.rikkahub.debug:styleable/MaterialToolbar = 0x7f13005e
me.rerere.rikkahub.debug:dimen/abc_dialog_padding_top_material = 0x7f060025
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120071
me.rerere.rikkahub.debug:style/TextAppearance.Design.Prefix = 0x7f1201d5
me.rerere.rikkahub.debug:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f0601a2
me.rerere.rikkahub.debug:id/leak_canary_stacktrace = 0x7f080119
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1201dd
me.rerere.rikkahub.debug:styleable/ConstraintLayout_placeholder = 0x7f130029
me.rerere.rikkahub.debug:layout/leak_canary_heap_dump_toast = 0x7f0b0031
me.rerere.rikkahub.debug:drawable/notification_bg_low_normal = 0x7f0700fc
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1201b9
me.rerere.rikkahub.debug:layout/m3_side_sheet_dialog = 0x7f0b0041
me.rerere.rikkahub.debug:string/setting_page_theme_type_medium_contrast = 0x7f1101e8
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f120431
me.rerere.rikkahub.debug:id/scrollable = 0x7f0801b1
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1201db
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Item = 0x7f1203af
me.rerere.rikkahub.debug:xml/file_paths = 0x7f140003
me.rerere.rikkahub.debug:layout/abc_action_mode_close_item_material = 0x7f0b0005
me.rerere.rikkahub.debug:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1200a8
me.rerere.rikkahub.debug:style/TextAppearance.Design.Tab = 0x7f1201d8
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0122
me.rerere.rikkahub.debug:style/Theme.AppCompat.NoActionBar = 0x7f120226
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button = 0x7f120403
me.rerere.rikkahub.debug:style/TextAppearance.Design.Snackbar.Message = 0x7f1201d6
me.rerere.rikkahub.debug:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120048
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06016a
me.rerere.rikkahub.debug:id/view_tree_view_model_store_owner = 0x7f080233
me.rerere.rikkahub.debug:layout/leak_canary_ref_row = 0x7f0b003b
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b5
me.rerere.rikkahub.debug:integer/mtrl_view_visible = 0x7f090043
me.rerere.rikkahub.debug:integer/mtrl_card_anim_duration_ms = 0x7f090036
me.rerere.rikkahub.debug:style/TextAppearance.Compat.Notification.Time = 0x7f1201cc
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1201a7
me.rerere.rikkahub.debug:id/mtrl_picker_header_title_and_selection = 0x7f08015b
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1201a5
me.rerere.rikkahub.debug:integer/mtrl_switch_track_viewport_width = 0x7f09003f
me.rerere.rikkahub.debug:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f12014a
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Large = 0x7f1201a4
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1200c6
me.rerere.rikkahub.debug:string/leak_canary_display_activity_label = 0x7f1100ba
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Menu = 0x7f120027
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Display3 = 0x7f1201a0
me.rerere.rikkahub.debug:layout/abc_alert_dialog_material = 0x7f0b0009
me.rerere.rikkahub.debug:style/Base.DialogWindowTitle.AppCompat = 0x7f120011
me.rerere.rikkahub.debug:id/consume_window_insets_tag = 0x7f080074
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f12028f
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.Full = 0x7f120176
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f12028e
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat = 0x7f120199
me.rerere.rikkahub.debug:layout/mtrl_calendar_year = 0x7f0b0060
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f120194
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1202d1
me.rerere.rikkahub.debug:id/spread_inside = 0x7f0801d0
me.rerere.rikkahub.debug:styleable/ForegroundLinearLayout = 0x7f130037
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f120418
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f120188
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f12046a
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bd
me.rerere.rikkahub.debug:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0142
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1203a7
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f120185
me.rerere.rikkahub.debug:style/Theme.Material3.Light = 0x7f120243
me.rerere.rikkahub.debug:id/wrapped_composition_tag = 0x7f08023c
me.rerere.rikkahub.debug:id/checkbox = 0x7f080065
me.rerere.rikkahub.debug:dimen/mtrl_navigation_item_icon_padding = 0x7f0602d7
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Tooltip = 0x7f12017f
me.rerere.rikkahub.debug:dimen/mtrl_tooltip_minWidth = 0x7f060319
me.rerere.rikkahub.debug:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1203c9
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f12025a
me.rerere.rikkahub.debug:id/cut = 0x7f080082
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.LargeComponent = 0x7f12017b
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.Medium = 0x7f120178
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f120173
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionMode = 0x7f1200ca
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f120170
me.rerere.rikkahub.debug:style/ucrop_TextViewWidgetText = 0x7f120473
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f12016f
me.rerere.rikkahub.debug:drawable/ic_mtrl_chip_checked_circle = 0x7f0700aa
me.rerere.rikkahub.debug:id/mtrl_view_tag_bottom_padding = 0x7f080161
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f120168
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f120164
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionButton = 0x7f120310
me.rerere.rikkahub.debug:id/peekHeight = 0x7f08018e
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_radius = 0x7f0602e8
me.rerere.rikkahub.debug:attr/maxVelocity = 0x7f0302f9
me.rerere.rikkahub.debug:color/material_on_surface_disabled = 0x7f0502a9
me.rerere.rikkahub.debug:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f120326
me.rerere.rikkahub.debug:id/password_toggle = 0x7f08018b
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f120162
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f120160
me.rerere.rikkahub.debug:style/Theme.AppCompat.Dialog = 0x7f12021a
me.rerere.rikkahub.debug:string/ucrop_error_input_data_is_absent = 0x7f110209
me.rerere.rikkahub.debug:string/leak_canary_import_from_title = 0x7f1100cd
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c000f
me.rerere.rikkahub.debug:style/Theme.AppCompat.Empty = 0x7f12021e
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f120153
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.DayNight = 0x7f12023f
me.rerere.rikkahub.debug:style/Platform.V25.AppCompat = 0x7f120146
me.rerere.rikkahub.debug:style/Platform.MaterialComponents = 0x7f12013d
me.rerere.rikkahub.debug:drawable/leak_canary_tab_background = 0x7f0700ba
me.rerere.rikkahub.debug:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f120300
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.TextButton = 0x7f120295
me.rerere.rikkahub.debug:style/Platform.AppCompat = 0x7f12013b
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00f8
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f12013a
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1203f7
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f120116
me.rerere.rikkahub.debug:id/stretch = 0x7f0801e4
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c0068
me.rerere.rikkahub.debug:styleable/ChipGroup = 0x7f13001f
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents = 0x7f120131
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Text = 0x7f12012f
me.rerere.rikkahub.debug:string/abc_action_bar_up_description = 0x7f110001
me.rerere.rikkahub.debug:style/FloatingDialogTheme = 0x7f120125
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f120161
me.rerere.rikkahub.debug:style/EdgeToEdgeFloatingDialogTheme = 0x7f120123
me.rerere.rikkahub.debug:dimen/m3_chip_dragged_translation_z = 0x7f060106
me.rerere.rikkahub.debug:style/DialogWindowTheme = 0x7f120122
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f12020c
me.rerere.rikkahub.debug:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f120301
me.rerere.rikkahub.debug:style/CardView.Light = 0x7f120121
me.rerere.rikkahub.debug:style/CardView = 0x7f12011f
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.TextView = 0x7f12011e
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.Slider = 0x7f12011a
me.rerere.rikkahub.debug:string/mtrl_picker_navigate_to_current_year_description = 0x7f110189
me.rerere.rikkahub.debug:style/Widget.Material3.BottomNavigationView = 0x7f120350
me.rerere.rikkahub.debug:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f120110
me.rerere.rikkahub.debug:styleable/MaterialRadioButton = 0x7f130058
me.rerere.rikkahub.debug:style/Base.Widget.Material3.Snackbar = 0x7f12010d
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1202ec
me.rerere.rikkahub.debug:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f120106
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.TextView = 0x7f1200f8
me.rerere.rikkahub.debug:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120349
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.ActionBar = 0x7f12027c
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_today_stroke = 0x7f060289
me.rerere.rikkahub.debug:color/material_personalized_color_on_surface = 0x7f0502bc
me.rerere.rikkahub.debug:color/design_dark_default_color_on_background = 0x7f05003f
me.rerere.rikkahub.debug:string/mtrl_picker_toggle_to_day_selection = 0x7f11019b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f120405
me.rerere.rikkahub.debug:style/Base.Widget.Material3.ActionMode = 0x7f1200fe
me.rerere.rikkahub.debug:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1201ce
me.rerere.rikkahub.debug:id/leak_canary_chip_new = 0x7f0800f7
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f12009e
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1200f9
me.rerere.rikkahub.debug:dimen/abc_progress_bar_height_material = 0x7f060035
me.rerere.rikkahub.debug:style/TextAppearance.Material3.BodyLarge = 0x7f1201ea
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1200eb
me.rerere.rikkahub.debug:macro/m3_comp_filled_button_label_text_color = 0x7f0c0044
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ListMenuView = 0x7f1200e5
me.rerere.rikkahub.debug:id/right_side = 0x7f0801a2
me.rerere.rikkahub.debug:string/common_google_play_services_install_text = 0x7f110089
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120118
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06011d
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1200de
me.rerere.rikkahub.debug:string/setting_display_page_show_token_usage_desc = 0x7f1101c1
me.rerere.rikkahub.debug:styleable/MaterialButton = 0x7f130050
me.rerere.rikkahub.debug:id/animateToStart = 0x7f08004c
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1200d9
me.rerere.rikkahub.debug:style/Theme.Material3.Light.NoActionBar = 0x7f120249
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f120163
me.rerere.rikkahub.debug:style/Theme.AppCompat = 0x7f120211
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1200d6
me.rerere.rikkahub.debug:id/enterAlways = 0x7f0800a7
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1200d5
me.rerere.rikkahub.debug:dimen/notification_action_icon_size = 0x7f06031c
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_height = 0x7f060291
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1202ca
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionButton = 0x7f1200c7
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1201b0
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0170
me.rerere.rikkahub.debug:macro/m3_comp_slider_label_label_text_color = 0x7f0c0112
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1201bb
me.rerere.rikkahub.debug:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060313
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1200c4
me.rerere.rikkahub.debug:style/TextAppearance.Material3.DisplaySmall = 0x7f1201ef
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1201dc
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1200d8
me.rerere.rikkahub.debug:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1200bb
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f120317
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1202b4
me.rerere.rikkahub.debug:id/transitionToEnd = 0x7f08021a
me.rerere.rikkahub.debug:style/Base.V28.Theme.AppCompat = 0x7f1200b8
me.rerere.rikkahub.debug:styleable/Constraint = 0x7f130027
me.rerere.rikkahub.debug:style/Base.V24.Theme.Material3.Dark = 0x7f1200b1
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f120155
me.rerere.rikkahub.debug:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0602a0
me.rerere.rikkahub.debug:color/secondary_text_default_material_light = 0x7f050337
me.rerere.rikkahub.debug:style/Widget.AppCompat.Spinner.DropDown = 0x7f12032f
me.rerere.rikkahub.debug:style/Widget.AppCompat.CompoundButton.Switch = 0x7f120302
me.rerere.rikkahub.debug:dimen/m3_navigation_item_shape_inset_top = 0x7f0601d5
me.rerere.rikkahub.debug:attr/dividerVertical = 0x7f030172
me.rerere.rikkahub.debug:style/Base.V23.Theme.AppCompat.Light = 0x7f1200b0
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1200c5
me.rerere.rikkahub.debug:id/motion_base = 0x7f08014a
me.rerere.rikkahub.debug:styleable/NavigationView = 0x7f130071
me.rerere.rikkahub.debug:id/dragEnd = 0x7f080097
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton.Icon = 0x7f120363
me.rerere.rikkahub.debug:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200ac
me.rerere.rikkahub.debug:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200aa
me.rerere.rikkahub.debug:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c00d4
me.rerere.rikkahub.debug:string/chat_page_new_chat = 0x7f110074
me.rerere.rikkahub.debug:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1200a6
me.rerere.rikkahub.debug:style/Base.V21.Theme.AppCompat.Light = 0x7f1200a3
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f12030b
me.rerere.rikkahub.debug:style/Base.Animation.AppCompat.DropDownUp = 0x7f12000e
me.rerere.rikkahub.debug:styleable/StateSet = 0x7f13008d
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f12009f
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f120157
me.rerere.rikkahub.debug:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1200fd
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f120033
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1202df
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1201e4
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f120093
me.rerere.rikkahub.debug:attr/counterMaxLength = 0x7f030144
me.rerere.rikkahub.debug:attr/jlmv_alignVertical = 0x7f030251
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant100 = 0x7f050266
me.rerere.rikkahub.debug:layout/leak_canary_leak_header = 0x7f0b0037
me.rerere.rikkahub.debug:layout/design_bottom_sheet_dialog = 0x7f0b001e
me.rerere.rikkahub.debug:style/TextAppearance.Material3.ActionBar.Title = 0x7f1201e9
me.rerere.rikkahub.debug:id/linear = 0x7f080123
me.rerere.rikkahub.debug:string/material_timepicker_am = 0x7f110152
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f120086
me.rerere.rikkahub.debug:color/ucrop_color_active_aspect_ratio = 0x7f050344
me.rerere.rikkahub.debug:string/call_notification_hang_up_action = 0x7f11005e
me.rerere.rikkahub.debug:string/setting_page_about_desc = 0x7f1101cb
me.rerere.rikkahub.debug:string/leak_canary_test_class_name = 0x7f1100ec
me.rerere.rikkahub.debug:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1203d3
me.rerere.rikkahub.debug:layout/design_navigation_item = 0x7f0b0024
me.rerere.rikkahub.debug:dimen/notification_main_column_padding_top = 0x7f060322
me.rerere.rikkahub.debug:style/Platform.MaterialComponents.Light.Dialog = 0x7f120140
me.rerere.rikkahub.debug:string/menu_page_evening_greeting = 0x7f11015c
me.rerere.rikkahub.debug:drawable/ucrop_scrollbar_thumb_horizontal = 0x7f070127
me.rerere.rikkahub.debug:id/mini = 0x7f080143
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1201e7
me.rerere.rikkahub.debug:id/coil3_request_manager = 0x7f08006e
me.rerere.rikkahub.debug:string/send = 0x7f1101bc
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon = 0x7f0700d6
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f120080
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1201d9
me.rerere.rikkahub.debug:id/elastic = 0x7f0800a3
me.rerere.rikkahub.debug:id/leak_canary_notification_analyzing_heap = 0x7f08010b
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120076
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f12011d
me.rerere.rikkahub.debug:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0040
me.rerere.rikkahub.debug:style/Widget.Material3.Toolbar.OnSurface = 0x7f1203eb
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Headline = 0x7f1201a2
me.rerere.rikkahub.debug:id/month_navigation_next = 0x7f080147
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light = 0x7f12006e
me.rerere.rikkahub.debug:id/image = 0x7f0800d5
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1203a3
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f12006a
me.rerere.rikkahub.debug:string/chat_page_date_format_different_year = 0x7f110069
me.rerere.rikkahub.debug:style/Theme.Design.Light.BottomSheetDialog = 0x7f12022a
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f12018b
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f120067
me.rerere.rikkahub.debug:id/inspection_slot_table_set = 0x7f0800e1
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark.Dialog = 0x7f12005b
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f12005a
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060223
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f06020b
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060155
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light = 0x7f120052
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f120085
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f120458
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f120051
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1203bd
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Dialog = 0x7f12004d
me.rerere.rikkahub.debug:string/ucrop_menu_crop = 0x7f11020c
me.rerere.rikkahub.debug:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1200c1
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e8
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat = 0x7f12004b
me.rerere.rikkahub.debug:string/leak_canary_explore_heap_dump = 0x7f1100bc
me.rerere.rikkahub.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f12020f
me.rerere.rikkahub.debug:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f120046
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1202d5
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c5
me.rerere.rikkahub.debug:style/Base.TextAppearance.MaterialComponents.Button = 0x7f120045
me.rerere.rikkahub.debug:string/bottomsheet_drag_handle_clicked = 0x7f110058
me.rerere.rikkahub.debug:string/setting_page_assistant = 0x7f1101cc
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f12003a
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f12029b
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_motion_duration = 0x7f090038
me.rerere.rikkahub.debug:string/assistant_page_add_header = 0x7f110020
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f120282
me.rerere.rikkahub.debug:id/leak_canary_chip_library_leak = 0x7f0800f6
me.rerere.rikkahub.debug:id/parent = 0x7f080187
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f120421
me.rerere.rikkahub.debug:string/setting_display_page_auto_collapse_thinking_desc = 0x7f1101bd
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f120032
me.rerere.rikkahub.debug:dimen/ucrop_height_wrapper_controls = 0x7f06033d
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f12002e
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1200da
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat = 0x7f120078
me.rerere.rikkahub.debug:id/exitUntilCollapsed = 0x7f0800aa
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f120388
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1201e3
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f120029
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f120087
me.rerere.rikkahub.debug:string/theme_name_black = 0x7f1101f7
me.rerere.rikkahub.debug:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060312
me.rerere.rikkahub.debug:dimen/m3_comp_search_bar_container_height = 0x7f060180
me.rerere.rikkahub.debug:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f120107
me.rerere.rikkahub.debug:styleable/View = 0x7f13009b
me.rerere.rikkahub.debug:string/quickie_scan_qr_code = 0x7f1101b3
me.rerere.rikkahub.debug:styleable/Carousel = 0x7f13001c
me.rerere.rikkahub.debug:string/setting_page_color_mode_system = 0x7f1101d3
me.rerere.rikkahub.debug:string/setting_page_dynamic_color = 0x7f1101db
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f12030a
me.rerere.rikkahub.debug:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c0041
me.rerere.rikkahub.debug:string/material_motion_easing_accelerated = 0x7f11014a
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f120184
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Display2 = 0x7f12001c
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f120469
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Caption = 0x7f12001a
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Body1 = 0x7f120017
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f12031a
me.rerere.rikkahub.debug:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600c7
me.rerere.rikkahub.debug:styleable/Fragment = 0x7f130038
me.rerere.rikkahub.debug:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f120012
me.rerere.rikkahub.debug:style/Base.CardView = 0x7f120010
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f120216
me.rerere.rikkahub.debug:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1200a4
me.rerere.rikkahub.debug:drawable/test_level_drawable = 0x7f07010b
me.rerere.rikkahub.debug:style/Base.Animation.AppCompat.Tooltip = 0x7f12000f
me.rerere.rikkahub.debug:id/contentPanel = 0x7f080077
me.rerere.rikkahub.debug:color/material_personalized_color_on_primary_container = 0x7f0502b9
me.rerere.rikkahub.debug:id/tag_accessibility_clickable_spans = 0x7f0801e9
me.rerere.rikkahub.debug:style/Base.Animation.AppCompat.Dialog = 0x7f12000d
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.DarkActionBar = 0x7f120220
me.rerere.rikkahub.debug:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f12000a
me.rerere.rikkahub.debug:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601f9
me.rerere.rikkahub.debug:style/Animation.Material3.SideSheetDialog = 0x7f120007
me.rerere.rikkahub.debug:layout/abc_screen_simple = 0x7f0b0015
me.rerere.rikkahub.debug:style/Animation.Material3.BottomSheetDialog = 0x7f120006
me.rerere.rikkahub.debug:style/TextAppearance.Material3.BodySmall = 0x7f1201ec
me.rerere.rikkahub.debug:drawable/material_ic_clear_black_24dp = 0x7f0700cb
me.rerere.rikkahub.debug:id/select_dialog_listview = 0x7f0801bc
me.rerere.rikkahub.debug:style/Animation.AppCompat.Tooltip = 0x7f120004
me.rerere.rikkahub.debug:dimen/mtrl_shape_corner_size_small_component = 0x7f0602f4
me.rerere.rikkahub.debug:string/mtrl_picker_start_date_description = 0x7f110192
me.rerere.rikkahub.debug:string/bottomsheet_action_expand_halfway = 0x7f110057
me.rerere.rikkahub.debug:style/Animation.AppCompat.Dialog = 0x7f120002
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f12038c
me.rerere.rikkahub.debug:style/AlertDialog.AppCompat = 0x7f120000
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomAppBar = 0x7f1203fb
me.rerere.rikkahub.debug:dimen/abc_button_inset_horizontal_material = 0x7f060012
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011e
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f120392
me.rerere.rikkahub.debug:integer/mtrl_view_invisible = 0x7f090042
me.rerere.rikkahub.debug:string/search_menu_title = 0x7f1101b7
me.rerere.rikkahub.debug:string/ucrop_mutate_exception_hint = 0x7f11020d
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f120183
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f120172
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f12031c
me.rerere.rikkahub.debug:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060144
me.rerere.rikkahub.debug:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1203c6
me.rerere.rikkahub.debug:string/switch_role = 0x7f1101f3
me.rerere.rikkahub.debug:string/state_on = 0x7f1101f0
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Search = 0x7f1202ba
me.rerere.rikkahub.debug:dimen/m3_chip_checked_hovered_translation_z = 0x7f060103
me.rerere.rikkahub.debug:string/common_google_play_services_install_title = 0x7f11008a
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_extra_long2 = 0x7f090011
me.rerere.rikkahub.debug:string/state_off = 0x7f1101ef
me.rerere.rikkahub.debug:dimen/abc_dialog_fixed_width_major = 0x7f06001e
me.rerere.rikkahub.debug:string/mtrl_picker_date_header_unselected = 0x7f110182
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f120266
me.rerere.rikkahub.debug:string/snackbar_pane_title = 0x7f1101ed
me.rerere.rikkahub.debug:string/side_sheet_behavior = 0x7f1101ec
me.rerere.rikkahub.debug:id/text_view_contrast = 0x7f080202
me.rerere.rikkahub.debug:string/setting_page_share = 0x7f1101e4
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f12009a
me.rerere.rikkahub.debug:style/Platform.V21.AppCompat.Light = 0x7f120145
me.rerere.rikkahub.debug:string/setting_page_providers_desc = 0x7f1101e1
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1202a5
me.rerere.rikkahub.debug:string/setting_page_no_share_app = 0x7f1101df
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012b
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f6
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f12025d
me.rerere.rikkahub.debug:string/setting_page_config_api_desc = 0x7f1101d5
me.rerere.rikkahub.debug:styleable/MenuGroup = 0x7f13005f
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1200f5
me.rerere.rikkahub.debug:string/setting_page_config = 0x7f1101d4
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f120252
me.rerere.rikkahub.debug:string/setting_page_chat_storage_desc = 0x7f1101cf
me.rerere.rikkahub.debug:string/setting_page_chat_storage = 0x7f1101ce
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.Dialog = 0x7f120221
me.rerere.rikkahub.debug:string/setting_model_page_title = 0x7f1101c7
me.rerere.rikkahub.debug:style/TextAppearance.Material3.HeadlineLarge = 0x7f1201f0
me.rerere.rikkahub.debug:id/controls_shadow = 0x7f08007a
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0013
me.rerere.rikkahub.debug:dimen/design_snackbar_padding_horizontal = 0x7f060085
me.rerere.rikkahub.debug:string/setting_model_page_chat_model = 0x7f1101c6
me.rerere.rikkahub.debug:style/Base.V24.Theme.Material3.Light = 0x7f1200b3
me.rerere.rikkahub.debug:string/setting_display_page_show_updates_desc = 0x7f1101c3
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1203f1
me.rerere.rikkahub.debug:string/searchview_navigation_content_description = 0x7f1101ba
me.rerere.rikkahub.debug:string/searchbar_scrolling_view_behavior = 0x7f1101b8
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f120022
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.PopupMenu = 0x7f120443
me.rerere.rikkahub.debug:dimen/leak_canary_connector_leak_dash_line = 0x7f06009f
me.rerere.rikkahub.debug:string/path_password_strike_through = 0x7f1101af
me.rerere.rikkahub.debug:style/Animation.Material3.SideSheetDialog.Left = 0x7f120008
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f12002c
me.rerere.rikkahub.debug:dimen/abc_control_padding_material = 0x7f06001a
me.rerere.rikkahub.debug:string/leak_canary_analysis_deleted_title = 0x7f1100b3
me.rerere.rikkahub.debug:string/leak_canary_tv_toast_retained_objects = 0x7f1100f0
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Body.Text = 0x7f120129
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.MediumComponent = 0x7f12017c
me.rerere.rikkahub.debug:string/project_id = 0x7f1101b1
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f120398
me.rerere.rikkahub.debug:string/m3c_bottom_sheet_pane_title = 0x7f110105
me.rerere.rikkahub.debug:style/Base.V7.Theme.AppCompat = 0x7f1200ba
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f120441
me.rerere.rikkahub.debug:string/assistant_page_strict = 0x7f110040
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_month_abbr = 0x7f110197
me.rerere.rikkahub.debug:string/common_google_play_services_install_button = 0x7f110088
me.rerere.rikkahub.debug:styleable/MaterialCalendar = 0x7f130052
me.rerere.rikkahub.debug:drawable/small_icon = 0x7f07010a
me.rerere.rikkahub.debug:string/chat_page_regenerate_title = 0x7f110077
me.rerere.rikkahub.debug:string/ucrop_label_original = 0x7f11020b
me.rerere.rikkahub.debug:string/mtrl_picker_end_date_description = 0x7f110184
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark = 0x7f120059
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.NoActionBar = 0x7f120233
me.rerere.rikkahub.debug:style/Widget.Material3.BottomSheet.DragHandle = 0x7f120353
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f120253
me.rerere.rikkahub.debug:string/mtrl_switch_track_decoration_path = 0x7f1101a4
me.rerere.rikkahub.debug:string/mtrl_picker_date_header_selected = 0x7f110180
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f120257
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_short4 = 0x7f09001f
me.rerere.rikkahub.debug:string/mtrl_picker_confirm = 0x7f11017f
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextView = 0x7f12045c
me.rerere.rikkahub.debug:layout/mtrl_alert_select_dialog_item = 0x7f0b0053
me.rerere.rikkahub.debug:string/mtrl_picker_cancel = 0x7f11017e
me.rerere.rikkahub.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120014
me.rerere.rikkahub.debug:string/assistant_page_custom_bodies = 0x7f11002b
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013b
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Light = 0x7f1202ae
me.rerere.rikkahub.debug:styleable/TabLayout = 0x7f130091
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120444
me.rerere.rikkahub.debug:string/mtrl_picker_announce_current_range_selection = 0x7f11017b
me.rerere.rikkahub.debug:styleable/ActionBar = 0x7f130000
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f120082
me.rerere.rikkahub.debug:styleable/SignInButton = 0x7f130085
me.rerere.rikkahub.debug:drawable/googleg_disabled_color_18 = 0x7f070099
me.rerere.rikkahub.debug:attr/paddingTopNoTitle = 0x7f03034f
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0126
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_medium2 = 0x7f090019
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1200ee
me.rerere.rikkahub.debug:dimen/tooltip_horizontal_padding = 0x7f06032c
me.rerere.rikkahub.debug:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a5
me.rerere.rikkahub.debug:string/ucrop_brightness = 0x7f110206
me.rerere.rikkahub.debug:string/mtrl_chip_close_icon_content_description = 0x7f110176
me.rerere.rikkahub.debug:style/Animation.Design.BottomSheetDialog = 0x7f120005
me.rerere.rikkahub.debug:style/Widget.Material3.Slider.Label = 0x7f1203d5
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.Alert = 0x7f120260
me.rerere.rikkahub.debug:dimen/mtrl_slider_label_square_side = 0x7f0602f8
me.rerere.rikkahub.debug:string/mtrl_checkbox_state_description_indeterminate = 0x7f110174
me.rerere.rikkahub.debug:string/mtrl_badge_numberless_content_description = 0x7f11016a
me.rerere.rikkahub.debug:style/Widget.AppCompat.PopupMenu = 0x7f120322
me.rerere.rikkahub.debug:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06022b
me.rerere.rikkahub.debug:string/model_list_favorite = 0x7f110166
me.rerere.rikkahub.debug:string/range_end = 0x7f1101b4
me.rerere.rikkahub.debug:id/leak_canary_bottom_navigation_bar = 0x7f0800f5
me.rerere.rikkahub.debug:id/fitXY = 0x7f0800b9
me.rerere.rikkahub.debug:string/mtrl_exceed_max_badge_number_suffix = 0x7f110178
me.rerere.rikkahub.debug:string/material_timepicker_text_input_mode_description = 0x7f110158
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1202c0
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1200df
me.rerere.rikkahub.debug:id/slide = 0x7f0801c6
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_action_top_padding = 0x7f0600ae
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b1
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Filter.Elevated = 0x7f12036f
me.rerere.rikkahub.debug:string/material_timepicker_minute = 0x7f110155
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006d
me.rerere.rikkahub.debug:layout/design_navigation_item_subheader = 0x7f0b0027
me.rerere.rikkahub.debug:string/material_motion_easing_linear = 0x7f11014d
me.rerere.rikkahub.debug:string/m3c_time_picker_hour_selection = 0x7f110134
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1202c8
me.rerere.rikkahub.debug:string/material_hour_suffix = 0x7f110147
me.rerere.rikkahub.debug:string/material_clock_toggle_content_description = 0x7f110144
me.rerere.rikkahub.debug:id/middle = 0x7f080142
me.rerere.rikkahub.debug:string/m3c_wide_navigation_rail_close_rail = 0x7f110141
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button.Small = 0x7f1202fd
me.rerere.rikkahub.debug:dimen/m3_comp_slider_stop_indicator_size = 0x7f060198
me.rerere.rikkahub.debug:string/m3c_time_picker_pm = 0x7f11013c
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00c8
me.rerere.rikkahub.debug:id/right = 0x7f08019f
me.rerere.rikkahub.debug:id/visible_removing_fragment_view_tag = 0x7f080235
me.rerere.rikkahub.debug:plurals/mtrl_badge_content_description = 0x7f0f0003
me.rerere.rikkahub.debug:string/m3c_snackbar_dismiss = 0x7f11012c
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0165
me.rerere.rikkahub.debug:string/m3c_dropdown_menu_expanded = 0x7f110127
me.rerere.rikkahub.debug:id/alertTitle = 0x7f080046
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Medium = 0x7f1201aa
me.rerere.rikkahub.debug:string/m3c_dialog = 0x7f110125
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Animation = 0x7f120128
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f12005d
me.rerere.rikkahub.debug:styleable/KeyFramesAcceleration = 0x7f130042
me.rerere.rikkahub.debug:string/mtrl_picker_out_of_range = 0x7f11018b
me.rerere.rikkahub.debug:integer/mtrl_btn_anim_duration_ms = 0x7f090031
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0067
me.rerere.rikkahub.debug:string/m3c_date_range_picker_day_in_range = 0x7f11011f
me.rerere.rikkahub.debug:string/material_motion_easing_emphasized = 0x7f11014c
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1100fc
me.rerere.rikkahub.debug:id/wrapper_reset_rotate = 0x7f08023e
me.rerere.rikkahub.debug:string/m3c_date_range_input_invalid_range_input = 0x7f11011d
me.rerere.rikkahub.debug:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f120304
me.rerere.rikkahub.debug:styleable/Chip = 0x7f13001e
me.rerere.rikkahub.debug:string/password_toggle_content_description = 0x7f1101ab
me.rerere.rikkahub.debug:id/toolbar_title = 0x7f080215
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602c3
me.rerere.rikkahub.debug:string/m3c_date_picker_headline_description = 0x7f11010f
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Menu = 0x7f1201ac
me.rerere.rikkahub.debug:string/m3c_date_input_title = 0x7f11010d
me.rerere.rikkahub.debug:string/abc_searchview_description_search = 0x7f110015
me.rerere.rikkahub.debug:integer/app_bar_elevation_anim_duration = 0x7f090002
me.rerere.rikkahub.debug:string/m3c_date_input_headline_description = 0x7f110107
me.rerere.rikkahub.debug:string/m3c_date_input_label = 0x7f11010b
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c015f
me.rerere.rikkahub.debug:string/m3c_date_input_invalid_not_allowed = 0x7f110109
me.rerere.rikkahub.debug:string/mtrl_picker_date_header_title = 0x7f110181
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f12030d
me.rerere.rikkahub.debug:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060112
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1203b9
me.rerere.rikkahub.debug:style/Theme.AppCompat.DialogWhenLarge = 0x7f12021d
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TonalButton = 0x7f120365
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_standard_accelerate = 0x7f1100ff
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f120314
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1100fb
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f1100f7
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionBar = 0x7f1200c2
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline3 = 0x7f120205
me.rerere.rikkahub.debug:mipmap/ic_launcher_foreground = 0x7f0e0002
me.rerere.rikkahub.debug:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f120351
me.rerere.rikkahub.debug:string/leak_canary_tv_analysis_success = 0x7f1100ef
me.rerere.rikkahub.debug:styleable/MotionTelltales = 0x7f130067
me.rerere.rikkahub.debug:string/leak_canary_storage_permission_activity_label = 0x7f1100eb
me.rerere.rikkahub.debug:styleable/State = 0x7f13008a
me.rerere.rikkahub.debug:layout/material_clock_display_divider = 0x7f0b0044
me.rerere.rikkahub.debug:string/leak_canary_stackoverflow_share = 0x7f1100ea
me.rerere.rikkahub.debug:string/leak_canary_options_menu_generate_hq_bitmap = 0x7f1100e1
me.rerere.rikkahub.debug:string/leak_canary_notification_no_retained_object_content = 0x7f1100da
me.rerere.rikkahub.debug:string/leak_canary_notification_channel_result = 0x7f1100d7
me.rerere.rikkahub.debug:styleable/MaterialCheckBox = 0x7f130055
me.rerere.rikkahub.debug:string/leak_canary_leak_not_found = 0x7f1100d3
me.rerere.rikkahub.debug:string/model_list_embedding = 0x7f110165
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f120289
me.rerere.rikkahub.debug:layout/m3_auto_complete_simple_item = 0x7f0b0040
me.rerere.rikkahub.debug:layout/mtrl_picker_text_input_date_range = 0x7f0b006d
me.rerere.rikkahub.debug:string/leak_canary_leak_missing_browser_error = 0x7f1100d2
me.rerere.rikkahub.debug:id/wrap = 0x7f08023a
me.rerere.rikkahub.debug:id/hide_ime_id = 0x7f0800c9
me.rerere.rikkahub.debug:id/view_tree_disjoint_parent = 0x7f08022f
me.rerere.rikkahub.debug:style/Widget.Material3.Button.Icon = 0x7f120358
me.rerere.rikkahub.debug:string/leak_canary_leak_clipdata_label = 0x7f1100d0
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.Dialog = 0x7f120237
me.rerere.rikkahub.debug:string/leak_canary_import_unsupported_file_extension = 0x7f1100cf
me.rerere.rikkahub.debug:style/Widget.Material3.Slider.Legacy = 0x7f1203d6
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f120251
me.rerere.rikkahub.debug:string/leak_canary_import_hprof_file = 0x7f1100ce
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0096
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_screen_title = 0x7f1100cb
me.rerere.rikkahub.debug:id/layout_scale_wheel = 0x7f0800f0
me.rerere.rikkahub.debug:layout/abc_popup_menu_item_layout = 0x7f0b0013
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060210
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f12045b
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_not_installed_text = 0x7f1100ca
me.rerere.rikkahub.debug:string/leak_canary_heap_analysis_success_screen_row_time_format = 0x7f1100c4
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f12037a
me.rerere.rikkahub.debug:string/leak_canary_generating_hq_bitmap_toast_failure_notice = 0x7f1100c0
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f12028b
me.rerere.rikkahub.debug:styleable/KeyPosition = 0x7f130044
me.rerere.rikkahub.debug:string/leak_canary_delete = 0x7f1100b7
me.rerere.rikkahub.debug:string/leak_canary_analysis_success_notification = 0x7f1100b5
me.rerere.rikkahub.debug:id/accessibility_custom_action_6 = 0x7f08002c
me.rerere.rikkahub.debug:string/leak_canary_analysis_failed = 0x7f1100b4
me.rerere.rikkahub.debug:style/Base.V21.Theme.MaterialComponents = 0x7f1200a5
me.rerere.rikkahub.debug:string/leak_canary_loading_title = 0x7f1100d4
me.rerere.rikkahub.debug:string/leak_canary_about_message = 0x7f1100b1
me.rerere.rikkahub.debug:string/icon_content_description = 0x7f1100a9
me.rerere.rikkahub.debug:drawable/abc_item_background_holo_light = 0x7f07004b
me.rerere.rikkahub.debug:string/ucrop_sharpness = 0x7f110211
me.rerere.rikkahub.debug:macro/m3_comp_fab_primary_icon_color = 0x7f0c0038
me.rerere.rikkahub.debug:string/google_storage_bucket = 0x7f1100a7
me.rerere.rikkahub.debug:style/TextAppearance.Material3.LabelMedium = 0x7f1201f4
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f12005e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f120463
me.rerere.rikkahub.debug:id/material_minute_tv = 0x7f080136
me.rerere.rikkahub.debug:string/ucrop_scale = 0x7f110210
me.rerere.rikkahub.debug:string/google_crash_reporting_api_key = 0x7f1100a6
me.rerere.rikkahub.debug:string/fab_transformation_sheet_behavior = 0x7f1100a2
me.rerere.rikkahub.debug:string/error_a11y_label = 0x7f11009e
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1202a7
me.rerere.rikkahub.debug:string/copy = 0x7f110097
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.FloatingActionButton = 0x7f120420
me.rerere.rikkahub.debug:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
me.rerere.rikkahub.debug:string/common_google_play_services_update_text = 0x7f110090
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1202cf
me.rerere.rikkahub.debug:id/action_mode_close_button = 0x7f08003f
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f12025e
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f12006f
me.rerere.rikkahub.debug:dimen/m3_simple_item_color_selected_alpha = 0x7f0601fa
me.rerere.rikkahub.debug:string/common_google_play_services_update_button = 0x7f11008f
me.rerere.rikkahub.debug:string/range_start = 0x7f1101b5
me.rerere.rikkahub.debug:styleable/OnClick = 0x7f130073
me.rerere.rikkahub.debug:string/common_google_play_services_enable_title = 0x7f110087
me.rerere.rikkahub.debug:style/Widget.Material3.CardView.Filled = 0x7f120369
me.rerere.rikkahub.debug:string/selected = 0x7f1101bb
me.rerere.rikkahub.debug:string/common_google_play_services_enable_text = 0x7f110086
me.rerere.rikkahub.debug:string/common_google_play_services_enable_button = 0x7f110085
me.rerere.rikkahub.debug:string/code_block_copy = 0x7f110082
me.rerere.rikkahub.debug:string/close_drawer = 0x7f110080
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Day = 0x7f1203a0
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f120423
me.rerere.rikkahub.debug:string/clear_text_end_icon_content_description = 0x7f11007e
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001a
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0018
me.rerere.rikkahub.debug:styleable/CompoundButton = 0x7f130026
me.rerere.rikkahub.debug:string/chat_page_yesterday = 0x7f11007c
me.rerere.rikkahub.debug:string/translator_page_input_text = 0x7f1101ff
me.rerere.rikkahub.debug:string/setting_page_assistant_desc = 0x7f1101cd
me.rerere.rikkahub.debug:string/chat_page_today = 0x7f11007b
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f120223
me.rerere.rikkahub.debug:styleable/MaterialTextView = 0x7f13005c
me.rerere.rikkahub.debug:id/action_container = 0x7f080037
me.rerere.rikkahub.debug:string/chat_page_scroll_to_bottom = 0x7f110079
me.rerere.rikkahub.debug:string/chat_page_history = 0x7f110073
me.rerere.rikkahub.debug:color/material_on_background_emphasis_medium = 0x7f0502a5
me.rerere.rikkahub.debug:string/chat_page_edit_title = 0x7f11006c
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.Dark = 0x7f12023d
me.rerere.rikkahub.debug:style/Theme.Design.Light = 0x7f120229
me.rerere.rikkahub.debug:drawable/notification_action_background = 0x7f0700f9
me.rerere.rikkahub.debug:string/chat_page_date_format_same_year = 0x7f11006a
me.rerere.rikkahub.debug:string/chat_page_clear_context = 0x7f110068
me.rerere.rikkahub.debug:string/chat_input_placeholder = 0x7f110066
me.rerere.rikkahub.debug:string/character_counter_pattern = 0x7f110065
me.rerere.rikkahub.debug:style/EdgeToEdgeFloatingDialogWindowTheme = 0x7f120124
me.rerere.rikkahub.debug:string/call_notification_screening_text = 0x7f110061
me.rerere.rikkahub.debug:id/month_grid = 0x7f080144
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0065
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f12042f
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1200e2
me.rerere.rikkahub.debug:string/call_notification_answer_video_action = 0x7f11005c
me.rerere.rikkahub.debug:styleable/TextInputEditText = 0x7f130093
me.rerere.rikkahub.debug:string/bottom_sheet_behavior = 0x7f110054
me.rerere.rikkahub.debug:styleable/ucrop_AspectRatioTextView = 0x7f1300a0
me.rerere.rikkahub.debug:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070028
me.rerere.rikkahub.debug:string/bing_desc = 0x7f110053
me.rerere.rikkahub.debug:string/tab = 0x7f1101f4
me.rerere.rikkahub.debug:string/back = 0x7f110052
me.rerere.rikkahub.debug:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
me.rerere.rikkahub.debug:id/report_drawn = 0x7f08019d
me.rerere.rikkahub.debug:id/fillStart = 0x7f0800b1
me.rerere.rikkahub.debug:layout/abc_list_menu_item_checkbox = 0x7f0b000e
me.rerere.rikkahub.debug:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602b4
me.rerere.rikkahub.debug:id/layout_rotate_wheel = 0x7f0800ee
me.rerere.rikkahub.debug:string/assistant_page_thinking_budget_warning = 0x7f11004c
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.SearchView = 0x7f1200f2
me.rerere.rikkahub.debug:string/assistant_page_temperature_value = 0x7f110047
me.rerere.rikkahub.debug:id/wide = 0x7f080236
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f120455
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602ea
me.rerere.rikkahub.debug:attr/flow_lastVerticalStyle = 0x7f0301e4
me.rerere.rikkahub.debug:id/material_clock_period_am_button = 0x7f08012f
me.rerere.rikkahub.debug:string/assistant_page_temperature = 0x7f110046
me.rerere.rikkahub.debug:style/Widget.Material3.Badge.AdjustToBounds = 0x7f12034b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f120432
me.rerere.rikkahub.debug:string/assistant_page_tab_basic = 0x7f110042
me.rerere.rikkahub.debug:string/assistant_page_stream_output_desc = 0x7f11003f
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120073
me.rerere.rikkahub.debug:id/screen = 0x7f0801ac
me.rerere.rikkahub.debug:string/assistant_page_inject_message_time = 0x7f110033
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f120150
me.rerere.rikkahub.debug:string/assistant_page_context_message_size = 0x7f110029
me.rerere.rikkahub.debug:string/assistant_page_chaotic = 0x7f110026
me.rerere.rikkahub.debug:string/chat_page_save = 0x7f110078
me.rerere.rikkahub.debug:string/assistant_page_balanced = 0x7f110022
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0087
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_path_checked = 0x7f11016f
me.rerere.rikkahub.debug:string/androidx_startup = 0x7f11001b
me.rerere.rikkahub.debug:string/m3c_time_picker_hour_24h_suffix = 0x7f110133
me.rerere.rikkahub.debug:string/setting_model_page_translate_model = 0x7f1101c9
me.rerere.rikkahub.debug:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f120429
me.rerere.rikkahub.debug:layout/leak_canary_list = 0x7f0b003a
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f12037d
me.rerere.rikkahub.debug:string/abc_searchview_description_query = 0x7f110014
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f12005c
me.rerere.rikkahub.debug:string/abc_search_hint = 0x7f110012
me.rerere.rikkahub.debug:string/abc_shareactionprovider_share_with_application = 0x7f110019
me.rerere.rikkahub.debug:string/mc2_snackbar_pane_title = 0x7f110159
me.rerere.rikkahub.debug:string/regenerate = 0x7f1101b6
me.rerere.rikkahub.debug:string/abc_prepend_shortcut_label = 0x7f110011
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f120430
me.rerere.rikkahub.debug:id/open_search_view_toolbar_container = 0x7f080181
me.rerere.rikkahub.debug:string/abc_menu_meta_shortcut_label = 0x7f11000d
me.rerere.rikkahub.debug:style/Widget.Compat.NotificationActionContainer = 0x7f120336
me.rerere.rikkahub.debug:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060188
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0060
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f120318
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f09003a
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120137
me.rerere.rikkahub.debug:macro/m3_comp_slider_label_container_color = 0x7f0c0111
me.rerere.rikkahub.debug:string/abc_menu_ctrl_shortcut_label = 0x7f110009
me.rerere.rikkahub.debug:string/abc_capital_off = 0x7f110006
me.rerere.rikkahub.debug:color/material_divider_color = 0x7f05024e
me.rerere.rikkahub.debug:attr/flow_horizontalBias = 0x7f0301de
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060224
me.rerere.rikkahub.debug:string/abc_activity_chooser_view_see_all = 0x7f110004
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_icon_size = 0x7f06015b
me.rerere.rikkahub.debug:raw/firebase_common_keep = 0x7f100000
me.rerere.rikkahub.debug:style/Theme.Material3.Light.Dialog = 0x7f120245
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0154
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f120468
me.rerere.rikkahub.debug:plurals/leak_canary_distinct_leaks = 0x7f0f0000
me.rerere.rikkahub.debug:layout/abc_action_bar_title_item = 0x7f0b0000
me.rerere.rikkahub.debug:mipmap/ic_launcher = 0x7f0e0000
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0031
me.rerere.rikkahub.debug:macro/m3_comp_slider_handle_color = 0x7f0c010f
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0c0173
me.rerere.rikkahub.debug:string/autofill = 0x7f110051
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c3
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0172
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f120026
me.rerere.rikkahub.debug:layout/abc_list_menu_item_layout = 0x7f0b0010
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016d
me.rerere.rikkahub.debug:id/search_src_text = 0x7f0801ba
me.rerere.rikkahub.debug:id/leak_canary_notification_write_permission = 0x7f080110
me.rerere.rikkahub.debug:string/m3_ref_typeface_plain_medium = 0x7f1100f4
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.NavigationView = 0x7f1202b8
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1202b6
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0162
me.rerere.rikkahub.debug:macro/m3_comp_dialog_headline_color = 0x7f0c0024
me.rerere.rikkahub.debug:style/Base.Widget.Material3.CardView = 0x7f120100
me.rerere.rikkahub.debug:styleable/MaterialTimePicker = 0x7f13005d
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0161
me.rerere.rikkahub.debug:id/withinBounds = 0x7f080239
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015e
me.rerere.rikkahub.debug:id/fixed = 0x7f0800ba
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015d
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f12015d
me.rerere.rikkahub.debug:id/text_view_sharpness = 0x7f080207
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015c
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f12012b
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f120309
me.rerere.rikkahub.debug:string/m3c_suggestions_available = 0x7f11012e
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c0159
me.rerere.rikkahub.debug:string/setting_page_general_settings = 0x7f1101dd
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight = 0x7f120213
me.rerere.rikkahub.debug:styleable/StateListDrawable = 0x7f13008b
me.rerere.rikkahub.debug:color/material_on_surface_stroke = 0x7f0502ac
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1203be
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f12015e
me.rerere.rikkahub.debug:drawable/abc_edit_text_material = 0x7f07003c
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f0501bc
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001b
me.rerere.rikkahub.debug:id/text_view_saturation = 0x7f080205
me.rerere.rikkahub.debug:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060185
me.rerere.rikkahub.debug:color/mtrl_scrim_color = 0x7f050313
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0158
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1203ba
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001b
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0157
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0156
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014d
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014c
me.rerere.rikkahub.debug:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014a
me.rerere.rikkahub.debug:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0146
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_track_color = 0x7f0c0140
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f120390
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_leaks_icon = 0x7f080109
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013e
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fb
me.rerere.rikkahub.debug:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f11019a
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1201a9
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0138
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ListView = 0x7f1200e7
me.rerere.rikkahub.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f120210
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0130
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0125
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_track_color = 0x7f0c012e
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f12020b
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012d
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light.Dialog = 0x7f120061
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0120
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f120196
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1201c2
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c011f
me.rerere.rikkahub.debug:string/leak_canary_class_has_leaked = 0x7f1100b6
me.rerere.rikkahub.debug:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f120335
me.rerere.rikkahub.debug:layout/abc_list_menu_item_radio = 0x7f0b0011
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1203f6
me.rerere.rikkahub.debug:styleable/MaterialSwitch = 0x7f13005a
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011c
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016e
me.rerere.rikkahub.debug:style/Widget.AppCompat.Spinner = 0x7f12032e
me.rerere.rikkahub.debug:styleable/CollapsingToolbarLayout = 0x7f130023
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f120092
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1203de
me.rerere.rikkahub.debug:layout/mtrl_navigation_rail_item = 0x7f0b0063
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011a
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1201ae
me.rerere.rikkahub.debug:string/chat_page_ignore_context = 0x7f110214
me.rerere.rikkahub.debug:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0116
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Assist = 0x7f12036c
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1202ab
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f12018f
me.rerere.rikkahub.debug:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060115
me.rerere.rikkahub.debug:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010d
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013a
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f120028
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f120464
me.rerere.rikkahub.debug:string/assistant_page_body_key = 0x7f110023
me.rerere.rikkahub.debug:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c010a
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0077
me.rerere.rikkahub.debug:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f12035b
me.rerere.rikkahub.debug:string/setting_page_about = 0x7f1101ca
me.rerere.rikkahub.debug:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0105
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600ce
me.rerere.rikkahub.debug:style/Widget.Material3.SearchView.Prefix = 0x7f1203ce
me.rerere.rikkahub.debug:id/leak_canary_time_text = 0x7f08011a
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0102
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f120275
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0100
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fc
me.rerere.rikkahub.debug:string/m3c_bottom_sheet_expand_description = 0x7f110104
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f5
me.rerere.rikkahub.debug:styleable/ListPopupWindow = 0x7f13004b
me.rerere.rikkahub.debug:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f3
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00ef
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ee
me.rerere.rikkahub.debug:string/m3c_bottom_sheet_drag_handle_description = 0x7f110103
me.rerere.rikkahub.debug:id/italic = 0x7f0800e5
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00eb
me.rerere.rikkahub.debug:id/off = 0x7f080172
me.rerere.rikkahub.debug:string/item_view_role_description = 0x7f1100ac
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0075
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00ca
me.rerere.rikkahub.debug:string/assistant_page_save = 0x7f11003d
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_legacy = 0x7f1100fa
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013d
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700f3
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ec
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1200e3
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
me.rerere.rikkahub.debug:color/material_personalized_color_tertiary_container = 0x7f0502d7
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
me.rerere.rikkahub.debug:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f120333
me.rerere.rikkahub.debug:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
me.rerere.rikkahub.debug:color/mtrl_btn_transparent_bg_color = 0x7f0502f3
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1203bc
me.rerere.rikkahub.debug:xml/data_extraction_rules = 0x7f140002
me.rerere.rikkahub.debug:macro/m3_comp_progress_indicator_track_color = 0x7f0c00d5
me.rerere.rikkahub.debug:styleable/FontFamilyFont = 0x7f130036
me.rerere.rikkahub.debug:drawable/ucrop_ic_saturation = 0x7f07011e
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d0
me.rerere.rikkahub.debug:color/material_slider_halo_color = 0x7f0502e3
me.rerere.rikkahub.debug:style/Theme.Material3.Light.DialogWhenLarge = 0x7f120248
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00cf
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cd
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cb
me.rerere.rikkahub.debug:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1200a0
me.rerere.rikkahub.debug:styleable/ClockHandView = 0x7f130022
me.rerere.rikkahub.debug:layout/abc_screen_toolbar = 0x7f0b0017
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3 = 0x7f120285
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f12041b
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Display3 = 0x7f12001d
me.rerere.rikkahub.debug:string/mtrl_timepicker_cancel = 0x7f1101a6
me.rerere.rikkahub.debug:id/SHIFT = 0x7f080007
me.rerere.rikkahub.debug:string/assistant_page_manage_memory = 0x7f110036
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1202c9
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c7
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.Material3.Dialog = 0x7f120081
me.rerere.rikkahub.debug:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c6
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00bc
me.rerere.rikkahub.debug:id/title_text_view = 0x7f080212
me.rerere.rikkahub.debug:layout/mtrl_layout_snackbar_include = 0x7f0b0062
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00b9
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b3
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0103
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar = 0x7f12039f
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b0
me.rerere.rikkahub.debug:style/Theme.Rikkahub = 0x7f12027a
me.rerere.rikkahub.debug:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a7
me.rerere.rikkahub.debug:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1200a2
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f12041a
me.rerere.rikkahub.debug:string/leak_canary_options_menu_permission_toast = 0x7f1100e2
me.rerere.rikkahub.debug:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a6
me.rerere.rikkahub.debug:drawable/abc_spinner_textfield_background_material = 0x7f070066
me.rerere.rikkahub.debug:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a3
me.rerere.rikkahub.debug:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a1
me.rerere.rikkahub.debug:string/leak_canary_leak_copied = 0x7f1100d1
me.rerere.rikkahub.debug:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a2
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f120040
me.rerere.rikkahub.debug:string/abc_menu_delete_shortcut_label = 0x7f11000a
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009c
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0097
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0095
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents = 0x7f120091
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionBar.TabText = 0x7f1202f0
me.rerere.rikkahub.debug:id/state_aspect_ratio = 0x7f0801da
me.rerere.rikkahub.debug:id/navigation_bar_item_icon_container = 0x7f080165
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1202a0
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Overline = 0x7f120209
me.rerere.rikkahub.debug:dimen/material_cursor_width = 0x7f060241
me.rerere.rikkahub.debug:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301d5
me.rerere.rikkahub.debug:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060184
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008c
me.rerere.rikkahub.debug:string/chat_page_delete = 0x7f11006b
me.rerere.rikkahub.debug:dimen/abc_text_size_menu_material = 0x7f06004b
me.rerere.rikkahub.debug:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602d9
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0086
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0085
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f120224
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0084
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0083
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120271
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0082
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0080
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0076
me.rerere.rikkahub.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f12015a
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c006e
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_standard = 0x7f1100fe
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006c
me.rerere.rikkahub.debug:id/date_picker_actions = 0x7f080084
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_container_color = 0x7f0c006b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f12042e
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0063
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionMode = 0x7f1202f5
me.rerere.rikkahub.debug:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0c005e
me.rerere.rikkahub.debug:id/material_clock_display_and_toggle = 0x7f08012b
me.rerere.rikkahub.debug:macro/m3_comp_input_chip_label_text_type = 0x7f0c005c
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1202b5
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f120394
me.rerere.rikkahub.debug:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005a
me.rerere.rikkahub.debug:macro/m3_comp_filter_chip_container_shape = 0x7f0c0057
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1200e6
me.rerere.rikkahub.debug:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0054
me.rerere.rikkahub.debug:id/sin = 0x7f0801c4
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f12023a
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1202c1
me.rerere.rikkahub.debug:color/primary_material_light = 0x7f050328
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f12017d
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0050
me.rerere.rikkahub.debug:layout/leak_canary_leak_screen = 0x7f0b0039
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1201a6
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009d
me.rerere.rikkahub.debug:string/m3c_dropdown_menu_collapsed = 0x7f110126
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004e
me.rerere.rikkahub.debug:macro/m3_comp_filled_button_container_color = 0x7f0c0043
me.rerere.rikkahub.debug:id/mtrl_motion_snapshot_view = 0x7f080157
me.rerere.rikkahub.debug:style/Widget.Design.TextInputLayout = 0x7f120342
me.rerere.rikkahub.debug:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0042
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Body1 = 0x7f12019a
me.rerere.rikkahub.debug:string/m3c_date_picker_year_picker_pane_title = 0x7f11011c
me.rerere.rikkahub.debug:styleable/AppCompatEmojiHelper = 0x7f13000e
me.rerere.rikkahub.debug:integer/mtrl_btn_anim_delay_ms = 0x7f090030
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f12016e
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0035
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f12030e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f120457
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0033
me.rerere.rikkahub.debug:string/m3c_time_picker_hour_text_field = 0x7f110136
me.rerere.rikkahub.debug:macro/m3_comp_divider_color = 0x7f0c0028
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f120373
me.rerere.rikkahub.debug:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0026
me.rerere.rikkahub.debug:macro/m3_comp_dialog_container_color = 0x7f0c0022
me.rerere.rikkahub.debug:string/leak_canary_about_enable_heap_dump = 0x7f1100ad
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001d
me.rerere.rikkahub.debug:string/setting_page_color_mode_light = 0x7f1101d2
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0019
me.rerere.rikkahub.debug:id/always = 0x7f080049
me.rerere.rikkahub.debug:id/add = 0x7f080043
me.rerere.rikkahub.debug:string/leak_canary_delete_all = 0x7f1100b8
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0016
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f120023
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1200e8
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0015
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000d
me.rerere.rikkahub.debug:string/mtrl_picker_toggle_to_text_input_mode = 0x7f11019c
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Assist.Elevated = 0x7f12036d
me.rerere.rikkahub.debug:id/accessibility_custom_action_7 = 0x7f08002d
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f120166
me.rerere.rikkahub.debug:drawable/mtrl_ic_cancel = 0x7f0700e2
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120099
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
me.rerere.rikkahub.debug:id/material_clock_period_pm_button = 0x7f080130
me.rerere.rikkahub.debug:style/Base.AlertDialog.AppCompat.Light = 0x7f12000c
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f12015b
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
me.rerere.rikkahub.debug:string/error_icon_content_description = 0x7f11009f
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f120231
me.rerere.rikkahub.debug:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700d3
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
me.rerere.rikkahub.debug:style/Widget.Material3.CompoundButton.RadioButton = 0x7f120384
me.rerere.rikkahub.debug:style/Widget.AppCompat.RatingBar.Indicator = 0x7f120328
me.rerere.rikkahub.debug:attr/indicatorDirectionCircular = 0x7f030227
me.rerere.rikkahub.debug:color/mtrl_navigation_item_text_color = 0x7f05030d
me.rerere.rikkahub.debug:id/progress_horizontal = 0x7f080198
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f120454
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
me.rerere.rikkahub.debug:id/navigation_bar_item_active_indicator_view = 0x7f080164
me.rerere.rikkahub.debug:styleable/ActivityChooserView = 0x7f130005
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f12030c
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f120461
me.rerere.rikkahub.debug:layout/ucrop_view = 0x7f0b0086
me.rerere.rikkahub.debug:style/Theme.Material3.Light.Dialog.Alert = 0x7f120246
me.rerere.rikkahub.debug:layout/ucrop_controls = 0x7f0b007e
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1203e6
me.rerere.rikkahub.debug:layout/ucrop_activity_photobox = 0x7f0b007c
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f120296
me.rerere.rikkahub.debug:string/assistant_page_stream_output = 0x7f11003e
me.rerere.rikkahub.debug:layout/select_dialog_item_material = 0x7f0b0078
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1201e1
me.rerere.rikkahub.debug:layout/notification_template_icon_group = 0x7f0b0073
me.rerere.rikkahub.debug:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f130031
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1201c3
me.rerere.rikkahub.debug:id/title_template = 0x7f080211
me.rerere.rikkahub.debug:layout/notification_template_custom_big = 0x7f0b0072
me.rerere.rikkahub.debug:layout/mtrl_search_bar = 0x7f0b006e
me.rerere.rikkahub.debug:string/m3c_date_range_picker_scroll_to_next_month = 0x7f110121
me.rerere.rikkahub.debug:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0143
me.rerere.rikkahub.debug:layout/mtrl_picker_text_input_date = 0x7f0b006c
me.rerere.rikkahub.debug:layout/mtrl_picker_header_title_text = 0x7f0b006a
me.rerere.rikkahub.debug:string/m3c_date_range_picker_end_headline = 0x7f110120
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1202b9
me.rerere.rikkahub.debug:string/material_timepicker_pm = 0x7f110156
me.rerere.rikkahub.debug:layout/mtrl_picker_fullscreen = 0x7f0b0066
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Display = 0x7f120460
me.rerere.rikkahub.debug:layout/mtrl_picker_actions = 0x7f0b0064
me.rerere.rikkahub.debug:layout/mtrl_calendar_months = 0x7f0b005e
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c0092
me.rerere.rikkahub.debug:style/Widget.AppCompat.ProgressBar = 0x7f120325
me.rerere.rikkahub.debug:style/Widget.Material3.BottomAppBar.Legacy = 0x7f12034e
me.rerere.rikkahub.debug:layout/mtrl_calendar_day_of_week = 0x7f0b0058
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016c
me.rerere.rikkahub.debug:style/Theme.AppCompat.CompactMenu = 0x7f120212
me.rerere.rikkahub.debug:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b0054
me.rerere.rikkahub.debug:layout/mtrl_alert_dialog_title = 0x7f0b0052
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Body2 = 0x7f1201ff
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button.Borderless = 0x7f1202f9
me.rerere.rikkahub.debug:layout/material_time_input = 0x7f0b004c
me.rerere.rikkahub.debug:styleable/ThemeEnforcement = 0x7f130095
me.rerere.rikkahub.debug:string/m3c_time_picker_minute_selection = 0x7f110138
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_viewport_size = 0x7f09003d
me.rerere.rikkahub.debug:styleable/DrawerArrowToggle = 0x7f13002e
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002c
me.rerere.rikkahub.debug:layout/material_textinput_timepicker = 0x7f0b004a
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_day_selection = 0x7f110115
me.rerere.rikkahub.debug:string/m3c_date_picker_title = 0x7f11011a
me.rerere.rikkahub.debug:layout/material_radial_view_group = 0x7f0b0049
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_icon_path_checked = 0x7f11016b
me.rerere.rikkahub.debug:drawable/ucrop_scale = 0x7f070126
me.rerere.rikkahub.debug:layout/material_clockface_textview = 0x7f0b0047
me.rerere.rikkahub.debug:layout/material_clock_period_toggle_land = 0x7f0b0046
me.rerere.rikkahub.debug:raw/firebase_crashlytics_keep = 0x7f100001
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1200d4
me.rerere.rikkahub.debug:layout/m3_alert_dialog = 0x7f0b003d
me.rerere.rikkahub.debug:id/rectangles = 0x7f08019c
me.rerere.rikkahub.debug:layout/leak_canary_simple_row = 0x7f0b003c
me.rerere.rikkahub.debug:string/ucrop_rotate = 0x7f11020e
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f120462
me.rerere.rikkahub.debug:styleable/ActivityNavigator = 0x7f130006
me.rerere.rikkahub.debug:layout/leak_canary_leak_chips = 0x7f0b0036
me.rerere.rikkahub.debug:style/Base.Widget.Material3.FloatingActionButton = 0x7f120108
me.rerere.rikkahub.debug:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600d2
me.rerere.rikkahub.debug:layout/leak_canary_hprof_explorer = 0x7f0b0034
me.rerere.rikkahub.debug:dimen/mtrl_calendar_bottom_padding = 0x7f060284
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1200f0
me.rerere.rikkahub.debug:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f080231
me.rerere.rikkahub.debug:layout/leak_canary_heap_dump_leak_title = 0x7f0b0030
me.rerere.rikkahub.debug:integer/material_motion_duration_long_1 = 0x7f090028
me.rerere.rikkahub.debug:id/menu_loader = 0x7f080140
me.rerere.rikkahub.debug:layout/design_text_input_end_icon = 0x7f0b002a
me.rerere.rikkahub.debug:style/leak_canary_Widget.ActionBar = 0x7f12046f
me.rerere.rikkahub.debug:layout/design_menu_item_action_area = 0x7f0b0023
me.rerere.rikkahub.debug:layout/design_layout_snackbar = 0x7f0b001f
me.rerere.rikkahub.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f12004a
me.rerere.rikkahub.debug:style/Theme.Design.BottomSheetDialog = 0x7f120228
me.rerere.rikkahub.debug:layout/design_bottom_navigation_item = 0x7f0b001d
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Filter = 0x7f12036e
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog = 0x7f12025f
me.rerere.rikkahub.debug:styleable/ShapeAppearance = 0x7f130082
me.rerere.rikkahub.debug:interpolator/mtrl_linear = 0x7f0a0010
me.rerere.rikkahub.debug:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
me.rerere.rikkahub.debug:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Caption = 0x7f120201
me.rerere.rikkahub.debug:layout/abc_list_menu_item_icon = 0x7f0b000f
me.rerere.rikkahub.debug:styleable/FloatingActionButton = 0x7f130032
me.rerere.rikkahub.debug:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
me.rerere.rikkahub.debug:id/text2 = 0x7f0801f8
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f120452
me.rerere.rikkahub.debug:string/setting_page_search_service = 0x7f1101e2
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_extra_long3 = 0x7f090012
me.rerere.rikkahub.debug:layout/abc_action_menu_layout = 0x7f0b0003
me.rerere.rikkahub.debug:style/Widget.Design.BottomSheet.Modal = 0x7f12033a
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d3
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
me.rerere.rikkahub.debug:interpolator/fast_out_slow_in = 0x7f0a0006
me.rerere.rikkahub.debug:style/FloatingDialogWindowTheme = 0x7f120126
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f120272
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_container_shape = 0x7f0c014f
me.rerere.rikkahub.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f120315
me.rerere.rikkahub.debug:string/m3c_time_picker_minute = 0x7f110137
me.rerere.rikkahub.debug:string/google_app_id = 0x7f1100a5
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f120259
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602c4
me.rerere.rikkahub.debug:id/leak_canary_notification_dumping_heap = 0x7f08010c
me.rerere.rikkahub.debug:styleable/ActionMode = 0x7f130004
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1200e4
me.rerere.rikkahub.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
me.rerere.rikkahub.debug:color/mtrl_navigation_item_background_color = 0x7f05030b
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c008d
me.rerere.rikkahub.debug:styleable/AppCompatTextHelper = 0x7f130011
me.rerere.rikkahub.debug:string/assistant_page_tab_request = 0x7f110045
me.rerere.rikkahub.debug:integer/mtrl_switch_track_viewport_height = 0x7f09003e
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_pressed_duration = 0x7f09003b
me.rerere.rikkahub.debug:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0148
me.rerere.rikkahub.debug:id/leak_canary_row_text = 0x7f080114
me.rerere.rikkahub.debug:integer/mtrl_calendar_year_selector_span = 0x7f090034
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bb
me.rerere.rikkahub.debug:string/assistant_page_top_p = 0x7f11004e
me.rerere.rikkahub.debug:integer/mtrl_badge_max_character_count = 0x7f09002f
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f120039
me.rerere.rikkahub.debug:integer/material_motion_path = 0x7f09002e
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f120242
me.rerere.rikkahub.debug:styleable/ActionMenuItemView = 0x7f130002
me.rerere.rikkahub.debug:dimen/leak_canary_toast_icon_size = 0x7f0600ab
me.rerere.rikkahub.debug:integer/material_motion_duration_short_1 = 0x7f09002c
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f120437
me.rerere.rikkahub.debug:integer/material_motion_duration_medium_2 = 0x7f09002b
me.rerere.rikkahub.debug:integer/material_motion_duration_long_2 = 0x7f090029
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_small_corner_family = 0x7f090026
me.rerere.rikkahub.debug:id/match_parent = 0x7f080129
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_large_corner_family = 0x7f090024
me.rerere.rikkahub.debug:styleable/SearchView = 0x7f130081
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090022
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f120397
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f120192
me.rerere.rikkahub.debug:integer/m3_sys_motion_path = 0x7f090020
me.rerere.rikkahub.debug:id/open_search_view_root = 0x7f08017c
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_short3 = 0x7f09001e
me.rerere.rikkahub.debug:styleable/BottomAppBar = 0x7f130016
me.rerere.rikkahub.debug:string/setting_display_page_auto_collapse_thinking_title = 0x7f1101be
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_long3 = 0x7f090016
me.rerere.rikkahub.debug:string/bottomsheet_action_expand = 0x7f110056
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_long1 = 0x7f090014
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_extra_long4 = 0x7f090013
me.rerere.rikkahub.debug:styleable/GradientColorItem = 0x7f13003b
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0160
me.rerere.rikkahub.debug:integer/m3_card_anim_delay_ms = 0x7f09000d
me.rerere.rikkahub.debug:integer/m3_btn_anim_duration_ms = 0x7f09000c
me.rerere.rikkahub.debug:id/mtrl_internal_children_alpha_tag = 0x7f080156
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0094
me.rerere.rikkahub.debug:id/tag_accessibility_pane_title = 0x7f0801eb
me.rerere.rikkahub.debug:id/layout = 0x7f0800ea
me.rerere.rikkahub.debug:integer/cancel_button_image_alpha = 0x7f090004
me.rerere.rikkahub.debug:styleable/KeyCycle = 0x7f130040
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_container_shape = 0x7f0c00a9
me.rerere.rikkahub.debug:integer/abc_config_activityShortDur = 0x7f090001
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Dialog = 0x7f120068
me.rerere.rikkahub.debug:color/switch_thumb_disabled_material_dark = 0x7f05033a
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1202bf
me.rerere.rikkahub.debug:id/wrapper_states = 0x7f080240
me.rerere.rikkahub.debug:drawable/leak_canary_tv_icon = 0x7f0700bd
me.rerere.rikkahub.debug:dimen/design_appbar_elevation = 0x7f06005e
me.rerere.rikkahub.debug:id/up = 0x7f08022b
me.rerere.rikkahub.debug:id/ucrop_photobox = 0x7f080227
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f120280
me.rerere.rikkahub.debug:id/ucrop = 0x7f080225
me.rerere.rikkahub.debug:string/material_slider_range_end = 0x7f11014f
me.rerere.rikkahub.debug:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a4
me.rerere.rikkahub.debug:id/ucrop_frame = 0x7f080226
me.rerere.rikkahub.debug:id/transition_scene_layoutid_cache = 0x7f080222
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016b
me.rerere.rikkahub.debug:dimen/abc_list_item_height_small_material = 0x7f060032
me.rerere.rikkahub.debug:id/leak_canary_count_text = 0x7f0800f8
me.rerere.rikkahub.debug:string/mtrl_picker_today_description = 0x7f110199
me.rerere.rikkahub.debug:id/transition_layout_save = 0x7f08021f
me.rerere.rikkahub.debug:style/Platform.ThemeOverlay.AppCompat = 0x7f120141
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ActionMode = 0x7f1203f2
me.rerere.rikkahub.debug:id/transition_image_transform = 0x7f08021e
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f12012a
me.rerere.rikkahub.debug:id/search_button = 0x7f0801b4
me.rerere.rikkahub.debug:styleable/LinearLayoutCompat_Layout = 0x7f130049
me.rerere.rikkahub.debug:id/transition_current_scene = 0x7f08021d
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f4
me.rerere.rikkahub.debug:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f12020e
me.rerere.rikkahub.debug:dimen/m3_btn_icon_btn_padding_right = 0x7f0600e2
me.rerere.rikkahub.debug:style/Widget.Material3.SearchView = 0x7f1203cd
me.rerere.rikkahub.debug:id/torch_image_view = 0x7f080218
me.rerere.rikkahub.debug:string/settings = 0x7f1101ea
me.rerere.rikkahub.debug:dimen/m3_comp_menu_container_elevation = 0x7f060145
me.rerere.rikkahub.debug:styleable/ActionMenuView = 0x7f130003
me.rerere.rikkahub.debug:id/title = 0x7f08020f
me.rerere.rikkahub.debug:id/mtrl_calendar_months = 0x7f080150
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton = 0x7f12035f
me.rerere.rikkahub.debug:id/time = 0x7f08020e
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_day_abbr = 0x7f110196
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f1203ff
me.rerere.rikkahub.debug:layout/abc_action_menu_item_layout = 0x7f0b0002
me.rerere.rikkahub.debug:id/textinput_suffix_text = 0x7f08020d
me.rerere.rikkahub.debug:id/view_tree_saved_state_registry_owner = 0x7f080232
me.rerere.rikkahub.debug:styleable/FlowLayout = 0x7f130034
me.rerere.rikkahub.debug:id/selected = 0x7f0801bd
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c00fe
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_light_focused = 0x7f070088
me.rerere.rikkahub.debug:id/textinput_helper_text = 0x7f08020a
me.rerere.rikkahub.debug:id/postLayout = 0x7f080194
me.rerere.rikkahub.debug:id/textStart = 0x7f0801fc
me.rerere.rikkahub.debug:id/textSpacerNoTitle = 0x7f0801fb
me.rerere.rikkahub.debug:style/Widget.Material3.Snackbar = 0x7f1203d8
me.rerere.rikkahub.debug:id/text = 0x7f0801f7
me.rerere.rikkahub.debug:id/tag_unhandled_key_listeners = 0x7f0801f5
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f120378
me.rerere.rikkahub.debug:id/tag_system_bar_state_monitor = 0x7f0801f2
me.rerere.rikkahub.debug:dimen/m3_ripple_hovered_alpha = 0x7f0601e7
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060151
me.rerere.rikkahub.debug:id/tag_screen_reader_focusable = 0x7f0801f0
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1200f3
me.rerere.rikkahub.debug:id/tag_on_receive_content_mime_types = 0x7f0801ef
me.rerere.rikkahub.debug:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0053
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f120190
me.rerere.rikkahub.debug:id/tag_on_receive_content_listener = 0x7f0801ee
me.rerere.rikkahub.debug:string/setting_page_display_setting_desc = 0x7f1101da
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f12016d
me.rerere.rikkahub.debug:id/tag_accessibility_heading = 0x7f0801ea
me.rerere.rikkahub.debug:macro/m3_comp_search_view_container_color = 0x7f0c00f1
me.rerere.rikkahub.debug:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f090040
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f12007b
me.rerere.rikkahub.debug:id/submit_area = 0x7f0801e6
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1203b0
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1201e0
me.rerere.rikkahub.debug:id/staticLayout = 0x7f0801e1
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1200c3
me.rerere.rikkahub.debug:id/state_scale = 0x7f0801df
me.rerere.rikkahub.debug:id/state_rotate = 0x7f0801dd
me.rerere.rikkahub.debug:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0052
me.rerere.rikkahub.debug:id/state_contrast = 0x7f0801dc
me.rerere.rikkahub.debug:id/notification_background = 0x7f08016f
me.rerere.rikkahub.debug:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070040
me.rerere.rikkahub.debug:id/startVertical = 0x7f0801d9
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1203ae
me.rerere.rikkahub.debug:id/startHorizontal = 0x7f0801d7
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Suggestion = 0x7f120374
me.rerere.rikkahub.debug:id/start = 0x7f0801d6
me.rerere.rikkahub.debug:styleable/AlertDialog = 0x7f130007
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120446
me.rerere.rikkahub.debug:id/standard = 0x7f0801d5
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120114
me.rerere.rikkahub.debug:id/src_over = 0x7f0801d4
me.rerere.rikkahub.debug:id/spread = 0x7f0801cf
me.rerere.rikkahub.debug:styleable/MotionScene = 0x7f130066
me.rerere.rikkahub.debug:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042
me.rerere.rikkahub.debug:id/split_action_bar = 0x7f0801ce
me.rerere.rikkahub.debug:integer/hide_password_duration = 0x7f090009
me.rerere.rikkahub.debug:id/special_effects_controller_view_tag = 0x7f0801cc
me.rerere.rikkahub.debug:id/snapMargins = 0x7f0801ca
me.rerere.rikkahub.debug:id/snackbar_action = 0x7f0801c7
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_path_group_name = 0x7f110170
me.rerere.rikkahub.debug:string/calculating = 0x7f11005a
me.rerere.rikkahub.debug:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
me.rerere.rikkahub.debug:id/showCustom = 0x7f0801c1
me.rerere.rikkahub.debug:id/shortcut = 0x7f0801c0
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.Snackbar = 0x7f12011b
me.rerere.rikkahub.debug:string/call_notification_answer_action = 0x7f11005b
me.rerere.rikkahub.debug:id/sharpness_scroll_wheel = 0x7f0801bf
me.rerere.rikkahub.debug:layout/leak_canary_heap_analysis_failure_screen = 0x7f0b002f
me.rerere.rikkahub.debug:id/selection_type = 0x7f0801be
me.rerere.rikkahub.debug:string/assistant_page_delete = 0x7f11002e
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f12015c
me.rerere.rikkahub.debug:id/search_voice_btn = 0x7f0801bb
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00ce
me.rerere.rikkahub.debug:integer/m3_chip_anim_duration = 0x7f09000f
me.rerere.rikkahub.debug:id/search_go_btn = 0x7f0801b7
me.rerere.rikkahub.debug:styleable/NavHost = 0x7f13006c
me.rerere.rikkahub.debug:id/search_close_btn = 0x7f0801b5
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d2
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0123
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ImageButton = 0x7f1200dc
me.rerere.rikkahub.debug:id/search_bar = 0x7f0801b3
me.rerere.rikkahub.debug:raw/prism = 0x7f100002
me.rerere.rikkahub.debug:id/scrollIndicatorUp = 0x7f0801af
me.rerere.rikkahub.debug:id/scale = 0x7f0801aa
me.rerere.rikkahub.debug:id/useLogo = 0x7f08022c
me.rerere.rikkahub.debug:id/sawtooth = 0x7f0801a9
me.rerere.rikkahub.debug:string/mtrl_picker_range_header_title = 0x7f11018f
me.rerere.rikkahub.debug:id/save_overlay_view = 0x7f0801a8
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f120433
me.rerere.rikkahub.debug:id/saturation_scroll_wheel = 0x7f0801a6
me.rerere.rikkahub.debug:id/rounded = 0x7f0801a4
me.rerere.rikkahub.debug:id/right_icon = 0x7f0801a1
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1201ab
me.rerere.rikkahub.debug:id/rightToLeft = 0x7f0801a0
me.rerere.rikkahub.debug:id/ratio = 0x7f08019b
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f120364
me.rerere.rikkahub.debug:id/progress_circular = 0x7f080197
me.rerere.rikkahub.debug:dimen/abc_action_bar_content_inset_material = 0x7f060000
me.rerere.rikkahub.debug:string/call_notification_incoming_text = 0x7f11005f
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c009e
me.rerere.rikkahub.debug:id/preview_view = 0x7f080196
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e7
me.rerere.rikkahub.debug:string/common_google_play_services_notification_ticker = 0x7f11008c
me.rerere.rikkahub.debug:macro/m3_comp_snackbar_container_shape = 0x7f0c0114
me.rerere.rikkahub.debug:id/pressed = 0x7f080195
me.rerere.rikkahub.debug:id/position = 0x7f080193
me.rerere.rikkahub.debug:id/pathRelative = 0x7f08018d
me.rerere.rikkahub.debug:id/parentRelative = 0x7f080189
me.rerere.rikkahub.debug:string/hide_bottom_view_on_scroll_behavior = 0x7f1100a8
me.rerere.rikkahub.debug:id/open_search_view_status_bar_spacer = 0x7f08017f
me.rerere.rikkahub.debug:id/open_search_view_search_prefix = 0x7f08017e
me.rerere.rikkahub.debug:string/chat_page_new_message = 0x7f110075
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1202af
me.rerere.rikkahub.debug:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f120047
me.rerere.rikkahub.debug:drawable/ucrop_ic_contrast_unselected = 0x7f070115
me.rerere.rikkahub.debug:id/open_search_view_scrim = 0x7f08017d
me.rerere.rikkahub.debug:id/open_search_view_divider = 0x7f080178
me.rerere.rikkahub.debug:id/outline = 0x7f080182
me.rerere.rikkahub.debug:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c00a0
me.rerere.rikkahub.debug:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1203c7
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1201b2
me.rerere.rikkahub.debug:id/open_search_view_clear_button = 0x7f080176
me.rerere.rikkahub.debug:string/abc_action_mode_done = 0x7f110003
me.rerere.rikkahub.debug:styleable/Tooltip = 0x7f130097
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.CompactMenu = 0x7f12024e
me.rerere.rikkahub.debug:id/toggle = 0x7f080213
me.rerere.rikkahub.debug:id/notification_main_column_container = 0x7f080171
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f060129
me.rerere.rikkahub.debug:id/notification_main_column = 0x7f080170
me.rerere.rikkahub.debug:styleable/Motion = 0x7f130063
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f120060
me.rerere.rikkahub.debug:id/never = 0x7f08016b
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00c9
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00be
me.rerere.rikkahub.debug:id/mtrl_picker_title_text = 0x7f080160
me.rerere.rikkahub.debug:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f120044
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_about = 0x7f080104
me.rerere.rikkahub.debug:layout/design_navigation_menu_item = 0x7f0b0029
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1201da
me.rerere.rikkahub.debug:styleable/SVGImageView = 0x7f13007d
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f12008b
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light.NoActionBar = 0x7f120225
me.rerere.rikkahub.debug:style/ucrop_WrapperRotateButton = 0x7f120475
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004c
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_large_container_height = 0x7f06012b
me.rerere.rikkahub.debug:id/mtrl_card_checked_layer_id = 0x7f080154
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090021
me.rerere.rikkahub.debug:id/normal = 0x7f08016e
me.rerere.rikkahub.debug:id/mtrl_calendar_year_selector_frame = 0x7f080153
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_disabled_by_app = 0x7f1100c5
me.rerere.rikkahub.debug:string/menu_page_knowledge_base = 0x7f11015d
me.rerere.rikkahub.debug:string/assistant_page_thinking_budget_default = 0x7f110049
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f120158
me.rerere.rikkahub.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120049
me.rerere.rikkahub.debug:dimen/m3_extended_fab_end_padding = 0x7f0601c0
me.rerere.rikkahub.debug:id/mtrl_calendar_main_pane = 0x7f08014f
me.rerere.rikkahub.debug:drawable/$avd_show_password__2 = 0x7f070005
me.rerere.rikkahub.debug:color/mtrl_navigation_bar_colored_item_tint = 0x7f050307
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f12041e
me.rerere.rikkahub.debug:id/mtrl_calendar_day_selector_frame = 0x7f08014c
me.rerere.rikkahub.debug:id/design_bottom_sheet = 0x7f08008a
me.rerere.rikkahub.debug:id/mtrl_anchor_parent = 0x7f08014b
me.rerere.rikkahub.debug:id/menu_crop = 0x7f08013f
me.rerere.rikkahub.debug:style/Theme.AppCompat.Dialog.Alert = 0x7f12021b
me.rerere.rikkahub.debug:id/material_value_index = 0x7f08013d
me.rerere.rikkahub.debug:string/mermaid_export = 0x7f110161
me.rerere.rikkahub.debug:string/ucrop_crop = 0x7f110208
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f12008c
me.rerere.rikkahub.debug:id/material_timepicker_mode_button = 0x7f08013a
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat = 0x7f120016
me.rerere.rikkahub.debug:id/material_timepicker_cancel_button = 0x7f080138
me.rerere.rikkahub.debug:id/material_clock_face = 0x7f08012c
me.rerere.rikkahub.debug:string/mtrl_picker_navigate_to_year_description = 0x7f11018a
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.Light = 0x7f12007e
me.rerere.rikkahub.debug:id/m3_side_sheet = 0x7f080126
me.rerere.rikkahub.debug:drawable/m3_tabs_background = 0x7f0700c5
me.rerere.rikkahub.debug:id/list_item = 0x7f080125
me.rerere.rikkahub.debug:id/line1 = 0x7f080121
me.rerere.rikkahub.debug:id/leak_canary_row_title = 0x7f080115
me.rerere.rikkahub.debug:id/leak_canary_restored_view_state = 0x7f080111
me.rerere.rikkahub.debug:style/Widget.Material3.SearchBar = 0x7f1203cb
me.rerere.rikkahub.debug:id/leak_canary_notification_on_screen_exit = 0x7f08010e
me.rerere.rikkahub.debug:styleable/MaterialCheckBoxStates = 0x7f130056
me.rerere.rikkahub.debug:layout/design_layout_tab_icon = 0x7f0b0021
me.rerere.rikkahub.debug:id/leak_canary_notification_analysis_result = 0x7f08010a
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_about_icon = 0x7f080105
me.rerere.rikkahub.debug:id/leak_canary_leak_text = 0x7f080100
me.rerere.rikkahub.debug:style/TextAppearance.Design.Placeholder = 0x7f1201d4
me.rerere.rikkahub.debug:style/Widget.Material3.PopupMenu = 0x7f1203c5
me.rerere.rikkahub.debug:id/leak_canary_about_text = 0x7f0800f4
me.rerere.rikkahub.debug:id/leak_canary_about_heap_dump_text = 0x7f0800f3
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003c
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f120269
me.rerere.rikkahub.debug:id/leak_canary_about_heap_dump_switch_button = 0x7f0800f2
me.rerere.rikkahub.debug:id/layout_sharpness_bar = 0x7f0800f1
me.rerere.rikkahub.debug:string/default_error_message = 0x7f110099
me.rerere.rikkahub.debug:id/jumpToStart = 0x7f0800e8
me.rerere.rikkahub.debug:string/theme_name_sakura = 0x7f1101f9
me.rerere.rikkahub.debug:id/jumpToEnd = 0x7f0800e7
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c009f
me.rerere.rikkahub.debug:id/is_pooling_container_tag = 0x7f0800e4
me.rerere.rikkahub.debug:id/view_overlay = 0x7f08022e
me.rerere.rikkahub.debug:integer/mtrl_view_gone = 0x7f090041
me.rerere.rikkahub.debug:layout/abc_cascading_menu_item_layout = 0x7f0b000b
me.rerere.rikkahub.debug:id/action_image = 0x7f08003a
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1203e4
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Display1 = 0x7f12019e
me.rerere.rikkahub.debug:id/inward = 0x7f0800e3
me.rerere.rikkahub.debug:id/invisible = 0x7f0800e2
me.rerere.rikkahub.debug:id/info = 0x7f0800e0
me.rerere.rikkahub.debug:id/indeterminate = 0x7f0800df
me.rerere.rikkahub.debug:style/Widget.Material3.Button.ElevatedButton = 0x7f120356
me.rerere.rikkahub.debug:id/image_view_state_sharpness = 0x7f0800de
me.rerere.rikkahub.debug:id/image_view_state_saturation = 0x7f0800dc
me.rerere.rikkahub.debug:id/action_mode_bar = 0x7f08003d
me.rerere.rikkahub.debug:style/Widget.Material3.TabLayout.OnSurface = 0x7f1203dc
me.rerere.rikkahub.debug:dimen/ucrop_control_min_width = 0x7f060333
me.rerere.rikkahub.debug:id/icon_only = 0x7f0800d1
me.rerere.rikkahub.debug:dimen/mtrl_calendar_content_padding = 0x7f060285
me.rerere.rikkahub.debug:id/percent = 0x7f08018f
me.rerere.rikkahub.debug:id/icon_group = 0x7f0800d0
me.rerere.rikkahub.debug:id/hide_graphics_layer_in_inspector_tag = 0x7f0800c8
me.rerere.rikkahub.debug:id/header_title = 0x7f0800c7
me.rerere.rikkahub.debug:id/groups = 0x7f0800c6
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0020
me.rerere.rikkahub.debug:id/graph = 0x7f0800c3
me.rerere.rikkahub.debug:id/auto = 0x7f080050
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f12028a
me.rerere.rikkahub.debug:id/gone = 0x7f0800c2
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1202eb
me.rerere.rikkahub.debug:id/month_navigation_previous = 0x7f080148
me.rerere.rikkahub.debug:id/layout_aspect_ratio = 0x7f0800eb
me.rerere.rikkahub.debug:id/ghost_view_holder = 0x7f0800c1
me.rerere.rikkahub.debug:drawable/abc_text_select_handle_middle_mtrl = 0x7f07006f
me.rerere.rikkahub.debug:id/fullscreen_header = 0x7f0800bf
me.rerere.rikkahub.debug:style/Widget.Compat.NotificationActionText = 0x7f120337
me.rerere.rikkahub.debug:id/fragment_container_view_tag = 0x7f0800be
me.rerere.rikkahub.debug:id/floating = 0x7f0800bc
me.rerere.rikkahub.debug:id/flip = 0x7f0800bb
me.rerere.rikkahub.debug:id/fitEnd = 0x7f0800b6
me.rerere.rikkahub.debug:id/SHOW_PATH = 0x7f080009
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f120453
me.rerere.rikkahub.debug:style/Theme.Design.Light.NoActionBar = 0x7f12022b
me.rerere.rikkahub.debug:id/fitCenter = 0x7f0800b5
me.rerere.rikkahub.debug:style/Theme.Material3.Light.BottomSheetDialog = 0x7f120244
me.rerere.rikkahub.debug:style/Platform.MaterialComponents.Dialog = 0x7f12013e
me.rerere.rikkahub.debug:id/filled = 0x7f0800b4
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700f1
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f12006b
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f120294
me.rerere.rikkahub.debug:id/fill_vertical = 0x7f0800b3
me.rerere.rikkahub.debug:id/fill_horizontal = 0x7f0800b2
me.rerere.rikkahub.debug:id/fillEnd = 0x7f0800b0
me.rerere.rikkahub.debug:id/fade = 0x7f0800ad
me.rerere.rikkahub.debug:dimen/material_clock_display_height = 0x7f060232
me.rerere.rikkahub.debug:string/appbar_scrolling_view_behavior = 0x7f11001d
me.rerere.rikkahub.debug:id/expanded_menu = 0x7f0800ac
me.rerere.rikkahub.debug:style/ucrop_WrapperIconState = 0x7f120474
me.rerere.rikkahub.debug:layout/mtrl_picker_header_fullscreen = 0x7f0b0068
me.rerere.rikkahub.debug:id/expand_activities_button = 0x7f0800ab
me.rerere.rikkahub.debug:id/escape = 0x7f0800a9
me.rerere.rikkahub.debug:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070046
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0501d1
me.rerere.rikkahub.debug:styleable/PopupWindowBackgroundState = 0x7f130076
me.rerere.rikkahub.debug:id/enterAlwaysCollapsed = 0x7f0800a8
me.rerere.rikkahub.debug:id/endToStart = 0x7f0800a6
me.rerere.rikkahub.debug:id/end = 0x7f0800a5
me.rerere.rikkahub.debug:id/edit_text_id = 0x7f0800a2
me.rerere.rikkahub.debug:string/setting_page_share_text = 0x7f1101e6
me.rerere.rikkahub.debug:id/edge = 0x7f0800a0
me.rerere.rikkahub.debug:id/easeOut = 0x7f08009f
me.rerere.rikkahub.debug:color/ucrop_color_widget_text = 0x7f05035b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f12042d
me.rerere.rikkahub.debug:id/easeIn = 0x7f08009d
me.rerere.rikkahub.debug:id/dropdown_menu = 0x7f08009c
me.rerere.rikkahub.debug:string/leak_canary_permission_notification_text = 0x7f1100e5
me.rerere.rikkahub.debug:id/dragRight = 0x7f080099
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f120057
me.rerere.rikkahub.debug:id/disjoint = 0x7f080095
me.rerere.rikkahub.debug:id/disablePostScroll = 0x7f080093
me.rerere.rikkahub.debug:id/disableHome = 0x7f080092
me.rerere.rikkahub.debug:id/direct = 0x7f080091
me.rerere.rikkahub.debug:id/dialog_button = 0x7f08008f
me.rerere.rikkahub.debug:id/design_menu_item_action_area = 0x7f08008b
me.rerere.rikkahub.debug:string/material_motion_easing_decelerated = 0x7f11014b
me.rerere.rikkahub.debug:id/default_activity_button = 0x7f080088
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f12008a
me.rerere.rikkahub.debug:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c0108
me.rerere.rikkahub.debug:id/decelerateAndComplete = 0x7f080086
me.rerere.rikkahub.debug:id/decelerate = 0x7f080085
me.rerere.rikkahub.debug:id/customPanel = 0x7f080081
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Light = 0x7f12008d
me.rerere.rikkahub.debug:id/compose_view_saveable_id_tag = 0x7f080071
me.rerere.rikkahub.debug:id/custom = 0x7f080080
me.rerere.rikkahub.debug:id/cradle = 0x7f08007f
me.rerere.rikkahub.debug:id/chronometer = 0x7f080067
me.rerere.rikkahub.debug:id/cos = 0x7f08007d
me.rerere.rikkahub.debug:styleable/ViewBackgroundHelper = 0x7f13009c
me.rerere.rikkahub.debug:drawable/mtrl_ic_check_mark = 0x7f0700e3
me.rerere.rikkahub.debug:id/coordinator = 0x7f08007c
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1200dd
me.rerere.rikkahub.debug:id/container = 0x7f080075
me.rerere.rikkahub.debug:id/confirm_button = 0x7f080073
me.rerere.rikkahub.debug:id/compress = 0x7f080072
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_disabled_text = 0x7f1100c8
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600d5
me.rerere.rikkahub.debug:string/status_bar_notification_info_overflow = 0x7f1101f1
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f120189
me.rerere.rikkahub.debug:id/collapseActionView = 0x7f08006f
me.rerere.rikkahub.debug:id/tag_on_apply_window_listener = 0x7f0801ed
me.rerere.rikkahub.debug:id/close_image_view = 0x7f08006d
me.rerere.rikkahub.debug:color/ucrop_color_ebony_clay = 0x7f05034d
me.rerere.rikkahub.debug:id/scrollView = 0x7f0801b0
me.rerere.rikkahub.debug:mipmap/leak_canary_icon = 0x7f0e0004
me.rerere.rikkahub.debug:id/clockwise = 0x7f08006c
me.rerere.rikkahub.debug:id/clip_horizontal = 0x7f08006a
me.rerere.rikkahub.debug:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006c
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Input.Elevated = 0x7f120371
me.rerere.rikkahub.debug:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f12010c
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f060139
me.rerere.rikkahub.debug:id/clear_text = 0x7f080069
me.rerere.rikkahub.debug:macro/m3_comp_fab_primary_container_color = 0x7f0c0036
me.rerere.rikkahub.debug:id/chains = 0x7f080064
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.Dark = 0x7f12027d
me.rerere.rikkahub.debug:color/material_personalized_color_control_highlight = 0x7f0502b1
me.rerere.rikkahub.debug:string/assistant_page_invalid_json = 0x7f110035
me.rerere.rikkahub.debug:id/chain = 0x7f080063
me.rerere.rikkahub.debug:layout/ucrop_layout_scale_wheel = 0x7f0b0084
me.rerere.rikkahub.debug:id/center_vertical = 0x7f080062
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
me.rerere.rikkahub.debug:string/state_empty = 0x7f1101ee
me.rerere.rikkahub.debug:id/centerInside = 0x7f080060
me.rerere.rikkahub.debug:color/m3_timepicker_display_text_color = 0x7f050243
me.rerere.rikkahub.debug:dimen/disabled_alpha_material_light = 0x7f06008f
me.rerere.rikkahub.debug:color/material_blue_grey_950 = 0x7f05024a
me.rerere.rikkahub.debug:id/tag_state_description = 0x7f0801f1
me.rerere.rikkahub.debug:id/centerCrop = 0x7f08005f
me.rerere.rikkahub.debug:id/cancel_button = 0x7f08005d
me.rerere.rikkahub.debug:id/open_search_view_toolbar = 0x7f080180
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_next_month = 0x7f110117
me.rerere.rikkahub.debug:id/text_input_start_icon = 0x7f080200
me.rerere.rikkahub.debug:id/bounce = 0x7f08005a
me.rerere.rikkahub.debug:id/blocking = 0x7f080058
me.rerere.rikkahub.debug:style/Widget.Material3.Button.IconButton.Filled = 0x7f12035a
me.rerere.rikkahub.debug:id/beginning = 0x7f080057
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b2
me.rerere.rikkahub.debug:integer/show_password_duration = 0x7f090044
me.rerere.rikkahub.debug:id/autoCompleteToEnd = 0x7f080052
me.rerere.rikkahub.debug:id/async = 0x7f08004f
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0101
me.rerere.rikkahub.debug:attr/sideSheetDialogTheme = 0x7f0303af
me.rerere.rikkahub.debug:dimen/leak_canary_squiggly_span_amplitude = 0x7f0600a8
me.rerere.rikkahub.debug:id/animateToEnd = 0x7f08004b
me.rerere.rikkahub.debug:id/androidx_compose_ui_view_composition_context = 0x7f08004a
me.rerere.rikkahub.debug:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f120348
me.rerere.rikkahub.debug:id/all = 0x7f080048
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Display4 = 0x7f1201a1
me.rerere.rikkahub.debug:id/adjust_height = 0x7f080044
me.rerere.rikkahub.debug:styleable/ConstraintSet = 0x7f13002a
me.rerere.rikkahub.debug:id/activity_chooser_view_content = 0x7f080042
me.rerere.rikkahub.debug:id/action_divider = 0x7f080039
me.rerere.rikkahub.debug:id/action_context_bar = 0x7f080038
me.rerere.rikkahub.debug:id/action_bar_title = 0x7f080036
me.rerere.rikkahub.debug:id/action_bar_activity_content = 0x7f080031
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1201a8
me.rerere.rikkahub.debug:id/accessibility_custom_action_9 = 0x7f08002f
me.rerere.rikkahub.debug:dimen/compat_control_corner_material = 0x7f06005a
me.rerere.rikkahub.debug:styleable/ButtonBarLayout = 0x7f130019
me.rerere.rikkahub.debug:id/accessibility_custom_action_3 = 0x7f080027
me.rerere.rikkahub.debug:layout/mtrl_picker_header_dialog = 0x7f0b0067
me.rerere.rikkahub.debug:integer/status_bar_notification_info_maxnum = 0x7f090045
me.rerere.rikkahub.debug:id/accessibility_custom_action_29 = 0x7f080026
me.rerere.rikkahub.debug:id/row_index_key = 0x7f0801a5
me.rerere.rikkahub.debug:id/accessibility_custom_action_28 = 0x7f080025
me.rerere.rikkahub.debug:id/accessibility_custom_action_25 = 0x7f080022
me.rerere.rikkahub.debug:id/accessibility_custom_action_24 = 0x7f080021
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_icon_size = 0x7f0600b2
me.rerere.rikkahub.debug:id/accessibility_custom_action_23 = 0x7f080020
me.rerere.rikkahub.debug:id/accessibility_custom_action_21 = 0x7f08001e
me.rerere.rikkahub.debug:id/tag_accessibility_actions = 0x7f0801e8
me.rerere.rikkahub.debug:id/accessibility_custom_action_2 = 0x7f08001c
me.rerere.rikkahub.debug:id/accessibility_custom_action_19 = 0x7f08001b
me.rerere.rikkahub.debug:id/accessibility_custom_action_17 = 0x7f080019
me.rerere.rikkahub.debug:string/abc_action_bar_home_description = 0x7f110000
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060166
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060229
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0017
me.rerere.rikkahub.debug:id/accessibility_custom_action_16 = 0x7f080018
me.rerere.rikkahub.debug:id/path = 0x7f08018c
me.rerere.rikkahub.debug:id/accessibility_custom_action_13 = 0x7f080015
me.rerere.rikkahub.debug:id/accessibility_custom_action_12 = 0x7f080014
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f120291
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06018d
me.rerere.rikkahub.debug:id/accessibility_custom_action_11 = 0x7f080013
me.rerere.rikkahub.debug:id/accessibility_custom_action_10 = 0x7f080012
me.rerere.rikkahub.debug:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
me.rerere.rikkahub.debug:string/leak_canary_share_heap_dump_bitmap_screen_title = 0x7f1100e7
me.rerere.rikkahub.debug:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
me.rerere.rikkahub.debug:id/accessibility_custom_action_0 = 0x7f080010
me.rerere.rikkahub.debug:id/accessibility_action_clickable_span = 0x7f08000f
me.rerere.rikkahub.debug:id/accelerate = 0x7f08000e
me.rerere.rikkahub.debug:id/SYM = 0x7f08000b
me.rerere.rikkahub.debug:id/square = 0x7f0801d1
me.rerere.rikkahub.debug:drawable/ucrop_ic_sharpness_unselected = 0x7f070123
me.rerere.rikkahub.debug:id/SHOW_PROGRESS = 0x7f08000a
me.rerere.rikkahub.debug:id/SHOW_ALL = 0x7f080008
me.rerere.rikkahub.debug:id/FUNCTION = 0x7f080004
me.rerere.rikkahub.debug:layout/leak_canary_leak_activity = 0x7f0b0035
me.rerere.rikkahub.debug:id/BOTTOM_END = 0x7f080001
me.rerere.rikkahub.debug:drawable/ucrop_wrapper_controls_shape = 0x7f07012d
me.rerere.rikkahub.debug:drawable/ucrop_vector_loader = 0x7f07012b
me.rerere.rikkahub.debug:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f12016c
me.rerere.rikkahub.debug:dimen/abc_floating_window_z = 0x7f06002f
me.rerere.rikkahub.debug:string/theme_name_ocean = 0x7f1101f8
me.rerere.rikkahub.debug:drawable/ucrop_vector_ic_crop = 0x7f07012a
me.rerere.rikkahub.debug:drawable/ucrop_saturation = 0x7f070125
me.rerere.rikkahub.debug:style/Theme.Material3.Light.SideSheetDialog = 0x7f12024a
me.rerere.rikkahub.debug:drawable/ucrop_rotate = 0x7f070124
me.rerere.rikkahub.debug:string/leak_canary_share_with = 0x7f1100e8
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_container_color = 0x7f0c00a8
me.rerere.rikkahub.debug:style/Theme.Material3.Dark = 0x7f12022d
me.rerere.rikkahub.debug:drawable/ucrop_ic_scale_unselected = 0x7f070121
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0134
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060215
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1202b2
me.rerere.rikkahub.debug:drawable/ucrop_ic_scale = 0x7f070120
me.rerere.rikkahub.debug:drawable/ucrop_ic_rotate_unselected = 0x7f07011d
me.rerere.rikkahub.debug:drawable/ucrop_ic_rotate = 0x7f07011c
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1201c0
me.rerere.rikkahub.debug:drawable/ucrop_ic_next = 0x7f07011a
me.rerere.rikkahub.debug:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1200a9
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120075
me.rerere.rikkahub.debug:drawable/ucrop_ic_done = 0x7f070119
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070010
me.rerere.rikkahub.debug:attr/motionEasingStandardDecelerateInterpolator = 0x7f030326
me.rerere.rikkahub.debug:drawable/ucrop_ic_cross = 0x7f070118
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Display1 = 0x7f12001b
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f06016f
me.rerere.rikkahub.debug:drawable/ucrop_ic_crop_unselected = 0x7f070117
me.rerere.rikkahub.debug:drawable/ucrop_ic_angle = 0x7f070111
me.rerere.rikkahub.debug:drawable/ucrop_contrast = 0x7f07010f
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00af
me.rerere.rikkahub.debug:drawable/ucrop_brightness = 0x7f07010e
me.rerere.rikkahub.debug:attr/state_indeterminate = 0x7f0303d2
me.rerere.rikkahub.debug:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060174
me.rerere.rikkahub.debug:drawable/tooltip_frame_dark = 0x7f07010c
me.rerere.rikkahub.debug:drawable/quickie_ic_close = 0x7f070107
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f12043b
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f12030f
me.rerere.rikkahub.debug:color/purple_200 = 0x7f05032d
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0021
me.rerere.rikkahub.debug:drawable/quickie_bg_round = 0x7f070106
me.rerere.rikkahub.debug:integer/material_motion_duration_medium_1 = 0x7f09002a
me.rerere.rikkahub.debug:drawable/notification_template_icon_bg = 0x7f070102
me.rerere.rikkahub.debug:drawable/notification_oversize_large_icon_bg = 0x7f070101
me.rerere.rikkahub.debug:color/material_dynamic_primary70 = 0x7f050279
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant50 = 0x7f050155
me.rerere.rikkahub.debug:drawable/notification_icon_background = 0x7f070100
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.PopupMenu = 0x7f120319
me.rerere.rikkahub.debug:id/layout_brightness_bar = 0x7f0800ec
me.rerere.rikkahub.debug:drawable/notification_bg_low_pressed = 0x7f0700fd
me.rerere.rikkahub.debug:styleable/SwitchMaterial = 0x7f13008f
me.rerere.rikkahub.debug:drawable/navigation_empty_icon = 0x7f0700f8
me.rerere.rikkahub.debug:style/Widget.Material3.Tooltip = 0x7f1203ed
me.rerere.rikkahub.debug:drawable/mtrl_tabs_default_indicator = 0x7f0700f7
me.rerere.rikkahub.debug:drawable/mtrl_switch_track_decoration = 0x7f0700f6
me.rerere.rikkahub.debug:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06027a
me.rerere.rikkahub.debug:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0117
me.rerere.rikkahub.debug:styleable/MotionHelper = 0x7f130064
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700f4
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700f0
me.rerere.rikkahub.debug:color/material_dynamic_tertiary20 = 0x7f05028e
me.rerere.rikkahub.debug:style/Widget.Material3.BottomNavigation.Badge = 0x7f12034f
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_checked = 0x7f0700ec
me.rerere.rikkahub.debug:drawable/mtrl_popupmenu_background_overlay = 0x7f0700ea
me.rerere.rikkahub.debug:drawable/mtrl_navigation_bar_item_background = 0x7f0700e8
me.rerere.rikkahub.debug:drawable/mtrl_ic_indeterminate = 0x7f0700e7
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f12011c
me.rerere.rikkahub.debug:drawable/mtrl_ic_arrow_drop_up = 0x7f0700e1
me.rerere.rikkahub.debug:string/chat_page_export_format = 0x7f11006e
me.rerere.rikkahub.debug:drawable/mtrl_dropdown_arrow = 0x7f0700df
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700dc
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Primary = 0x7f120393
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700da
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.Dialog = 0x7f120215
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700d9
me.rerere.rikkahub.debug:macro/m3_comp_text_button_label_text_type = 0x7f0c0145
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f12007c
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700d7
me.rerere.rikkahub.debug:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700d2
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f12012c
me.rerere.rikkahub.debug:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700d0
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_default_width = 0x7f0602dd
me.rerere.rikkahub.debug:dimen/m3_card_elevated_disabled_z = 0x7f0600f6
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000e
me.rerere.rikkahub.debug:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700ce
me.rerere.rikkahub.debug:string/gcm_defaultSenderId = 0x7f1100a3
me.rerere.rikkahub.debug:drawable/material_ic_edit_black_24dp = 0x7f0700cc
me.rerere.rikkahub.debug:drawable/material_ic_calendar_black_24dp = 0x7f0700ca
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TabLayout = 0x7f12044d
me.rerere.rikkahub.debug:drawable/material_cursor_drawable = 0x7f0700c9
me.rerere.rikkahub.debug:drawable/m3_tabs_rounded_line_indicator = 0x7f0700c7
me.rerere.rikkahub.debug:drawable/m3_tabs_line_indicator = 0x7f0700c6
me.rerere.rikkahub.debug:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f120391
me.rerere.rikkahub.debug:drawable/m3_selection_control_ripple = 0x7f0700c4
me.rerere.rikkahub.debug:string/leak_canary_dump_heap = 0x7f1100bb
me.rerere.rikkahub.debug:drawable/m3_popupmenu_background_overlay = 0x7f0700c2
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602c5
me.rerere.rikkahub.debug:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f120346
me.rerere.rikkahub.debug:drawable/m3_password_eye = 0x7f0700c1
me.rerere.rikkahub.debug:drawable/leak_canary_secondary_button = 0x7f0700b9
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070021
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060263
me.rerere.rikkahub.debug:attr/transitionFlags = 0x7f03048d
me.rerere.rikkahub.debug:attr/behavior_halfExpandedRatio = 0x7f03006a
me.rerere.rikkahub.debug:drawable/leak_canary_primary_button = 0x7f0700b8
me.rerere.rikkahub.debug:drawable/leak_canary_list_selector = 0x7f0700b7
me.rerere.rikkahub.debug:drawable/leak_canary_leak = 0x7f0700b6
me.rerere.rikkahub.debug:drawable/leak_canary_icon_foreground = 0x7f0700b2
me.rerere.rikkahub.debug:attr/shapeAppearanceSmallComponent = 0x7f0303a1
me.rerere.rikkahub.debug:attr/values = 0x7f0304a8
me.rerere.rikkahub.debug:drawable/leak_canary_dump = 0x7f0700af
me.rerere.rikkahub.debug:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1200b4
me.rerere.rikkahub.debug:color/material_timepicker_button_background = 0x7f0502e7
me.rerere.rikkahub.debug:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502dc
me.rerere.rikkahub.debug:attr/maxLines = 0x7f0302f7
me.rerere.rikkahub.debug:drawable/ic_m3_chip_checked_circle = 0x7f0700a6
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601b2
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500fd
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1203e2
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c0078
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_unchecked = 0x7f0700f2
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f07000f
me.rerere.rikkahub.debug:drawable/ic_clear_black_24 = 0x7f0700a2
me.rerere.rikkahub.debug:color/leak_canary_heap_instance_string = 0x7f050085
me.rerere.rikkahub.debug:layout/material_timepicker_textinput_display = 0x7f0b004f
me.rerere.rikkahub.debug:drawable/ic_arrow_back_black_24 = 0x7f07009b
me.rerere.rikkahub.debug:dimen/m3_comp_elevated_card_container_elevation = 0x7f06011a
me.rerere.rikkahub.debug:drawable/design_ic_visibility_off = 0x7f070096
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f120112
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_light_normal = 0x7f070092
me.rerere.rikkahub.debug:dimen/design_snackbar_elevation = 0x7f060081
me.rerere.rikkahub.debug:string/setting_page_search_service_desc = 0x7f1101e3
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_light_focused = 0x7f070091
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f07008e
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_light = 0x7f070087
me.rerere.rikkahub.debug:string/mtrl_picker_invalid_format = 0x7f110185
me.rerere.rikkahub.debug:macro/m3_comp_filled_card_container_color = 0x7f0c0046
me.rerere.rikkahub.debug:attr/actionOverflowButtonStyle = 0x7f030020
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_dark_normal = 0x7f070084
me.rerere.rikkahub.debug:macro/m3_comp_fab_tertiary_container_color = 0x7f0c003f
me.rerere.rikkahub.debug:color/material_dynamic_tertiary10 = 0x7f05028c
me.rerere.rikkahub.debug:string/material_timepicker_hour = 0x7f110154
me.rerere.rikkahub.debug:style/Widget.Design.AppBarLayout = 0x7f120338
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1202e2
me.rerere.rikkahub.debug:layout/mtrl_auto_complete_simple_item = 0x7f0b0056
me.rerere.rikkahub.debug:drawable/common_full_open_on_phone = 0x7f070081
me.rerere.rikkahub.debug:string/leak_canary_generating_hq_bitmap_toast_notice = 0x7f1100c1
me.rerere.rikkahub.debug:attr/chipGroupStyle = 0x7f0300b3
me.rerere.rikkahub.debug:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070033
me.rerere.rikkahub.debug:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070080
me.rerere.rikkahub.debug:drawable/btn_radio_on_mtrl = 0x7f07007f
me.rerere.rikkahub.debug:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06013a
me.rerere.rikkahub.debug:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007e
me.rerere.rikkahub.debug:id/action_bar = 0x7f080030
me.rerere.rikkahub.debug:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007c
me.rerere.rikkahub.debug:drawable/btn_checkbox_checked_mtrl = 0x7f070079
me.rerere.rikkahub.debug:drawable/avd_show_password = 0x7f070078
me.rerere.rikkahub.debug:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06024a
me.rerere.rikkahub.debug:attr/alertDialogTheme = 0x7f03002b
me.rerere.rikkahub.debug:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070074
me.rerere.rikkahub.debug:macro/m3_comp_search_view_divider_color = 0x7f0c00f2
me.rerere.rikkahub.debug:drawable/abc_textfield_default_mtrl_alpha = 0x7f070072
me.rerere.rikkahub.debug:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700d1
me.rerere.rikkahub.debug:drawable/abc_text_select_handle_right_mtrl = 0x7f070070
me.rerere.rikkahub.debug:drawable/abc_switch_thumb_material = 0x7f070069
me.rerere.rikkahub.debug:integer/m3_badge_max_number = 0x7f09000a
me.rerere.rikkahub.debug:attr/haloRadius = 0x7f030202
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0012
me.rerere.rikkahub.debug:drawable/abc_star_black_48dp = 0x7f070067
me.rerere.rikkahub.debug:drawable/abc_ratingbar_material = 0x7f07005b
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602bb
me.rerere.rikkahub.debug:color/abc_primary_text_material_light = 0x7f05000c
me.rerere.rikkahub.debug:color/material_dynamic_tertiary100 = 0x7f05028d
me.rerere.rikkahub.debug:drawable/abc_ratingbar_indicator_material = 0x7f07005a
me.rerere.rikkahub.debug:attr/colorAccent = 0x7f0300e0
me.rerere.rikkahub.debug:animator/design_fab_hide_motion_spec = 0x7f020001
me.rerere.rikkahub.debug:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070058
me.rerere.rikkahub.debug:attr/showPaths = 0x7f0303ab
me.rerere.rikkahub.debug:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070052
me.rerere.rikkahub.debug:drawable/abc_ic_ab_back_material = 0x7f07003d
me.rerere.rikkahub.debug:drawable/abc_item_background_holo_dark = 0x7f07004a
me.rerere.rikkahub.debug:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070047
me.rerere.rikkahub.debug:drawable/abc_ic_menu_overflow_material = 0x7f070044
me.rerere.rikkahub.debug:drawable/abc_ic_clear_material = 0x7f07003f
me.rerere.rikkahub.debug:drawable/leak_canary_info_rectangle = 0x7f0700b5
me.rerere.rikkahub.debug:attr/region_heightMoreThan = 0x7f03037f
me.rerere.rikkahub.debug:drawable/abc_dialog_material_background = 0x7f07003b
me.rerere.rikkahub.debug:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070036
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral10 = 0x7f050138
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_medium3 = 0x7f09001a
me.rerere.rikkahub.debug:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070035
me.rerere.rikkahub.debug:drawable/abc_btn_radio_material = 0x7f070031
me.rerere.rikkahub.debug:drawable/abc_btn_colored_material = 0x7f07002f
me.rerere.rikkahub.debug:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002d
me.rerere.rikkahub.debug:attr/colorOnSecondaryFixedVariant = 0x7f0300f6
me.rerere.rikkahub.debug:drawable/abc_btn_check_material_anim = 0x7f07002c
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_headline_color = 0x7f0c0150
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070022
me.rerere.rikkahub.debug:attr/chipSpacing = 0x7f0300bb
me.rerere.rikkahub.debug:style/TextAppearance.Material3.HeadlineMedium = 0x7f1201f1
me.rerere.rikkahub.debug:attr/spinBars = 0x7f0303be
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001a
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070014
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000c
me.rerere.rikkahub.debug:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f05030e
me.rerere.rikkahub.debug:drawable/$m3_avd_hide_password__2 = 0x7f070008
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_outline_color = 0x7f0c00ae
me.rerere.rikkahub.debug:drawable/$avd_show_password__1 = 0x7f070004
me.rerere.rikkahub.debug:attr/titleTextColor = 0x7f03046d
me.rerere.rikkahub.debug:color/m3_ref_palette_primary10 = 0x7f05015d
me.rerere.rikkahub.debug:color/material_dynamic_secondary10 = 0x7f05027f
me.rerere.rikkahub.debug:drawable/$avd_hide_password__2 = 0x7f070002
me.rerere.rikkahub.debug:string/menu_page_ai_translator = 0x7f11015b
me.rerere.rikkahub.debug:dimen/ucrop_text_size_widget_text = 0x7f060347
me.rerere.rikkahub.debug:dimen/ucrop_margin_horizontal_wheel_progress_line = 0x7f06033f
me.rerere.rikkahub.debug:dimen/abc_dialog_padding_material = 0x7f060024
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c001f
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_elevation = 0x7f0602de
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral22 = 0x7f05013d
me.rerere.rikkahub.debug:string/code_block_preview = 0x7f110083
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_rect_corner_touch_area_line_length = 0x7f060337
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601e4
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_grid_stoke_width = 0x7f060335
me.rerere.rikkahub.debug:attr/actionTextColorAlpha = 0x7f030023
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012a
me.rerere.rikkahub.debug:dimen/tooltip_vertical_padding = 0x7f060330
me.rerere.rikkahub.debug:dimen/tooltip_corner_radius = 0x7f06032b
me.rerere.rikkahub.debug:string/assistant_page_thinking_budget_tokens = 0x7f11004b
me.rerere.rikkahub.debug:dimen/notification_small_icon_background_padding = 0x7f060326
me.rerere.rikkahub.debug:dimen/notification_right_side_padding_top = 0x7f060325
me.rerere.rikkahub.debug:dimen/notification_right_icon_size = 0x7f060324
me.rerere.rikkahub.debug:dimen/notification_large_icon_width = 0x7f060321
me.rerere.rikkahub.debug:color/m3_textfield_input_text_color = 0x7f05023a
me.rerere.rikkahub.debug:id/search_edit_frame = 0x7f0801b6
me.rerere.rikkahub.debug:dimen/notification_big_circle_margin = 0x7f06031e
me.rerere.rikkahub.debug:dimen/notification_action_text_size = 0x7f06031d
me.rerere.rikkahub.debug:attr/toolbarSurfaceStyle = 0x7f030474
me.rerere.rikkahub.debug:style/Base.Widget.Material3.BottomNavigationView = 0x7f1200ff
me.rerere.rikkahub.debug:id/accessibility_custom_action_27 = 0x7f080024
me.rerere.rikkahub.debug:dimen/mtrl_tooltip_padding = 0x7f06031a
me.rerere.rikkahub.debug:color/m3_ref_palette_primary20 = 0x7f05015f
me.rerere.rikkahub.debug:dimen/mtrl_toolbar_default_height = 0x7f060315
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c007e
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1201c5
me.rerere.rikkahub.debug:dimen/mtrl_textinput_counter_margin_start = 0x7f060311
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_long4 = 0x7f090017
me.rerere.rikkahub.debug:drawable/$m3_avd_show_password__0 = 0x7f070009
me.rerere.rikkahub.debug:dimen/mtrl_switch_thumb_size = 0x7f060309
me.rerere.rikkahub.debug:color/m3_tabs_text_color_secondary = 0x7f050234
me.rerere.rikkahub.debug:color/material_dynamic_tertiary30 = 0x7f05028f
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_padding_horizontal = 0x7f060305
me.rerere.rikkahub.debug:attr/motionEasingDecelerated = 0x7f03031d
me.rerere.rikkahub.debug:dimen/material_clock_face_margin_top = 0x7f060236
me.rerere.rikkahub.debug:attr/layout_keyline = 0x7f03029e
me.rerere.rikkahub.debug:attr/transitionEasing = 0x7f03048c
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f060304
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f060300
me.rerere.rikkahub.debug:string/leak_canary_notification_message = 0x7f1100d9
me.rerere.rikkahub.debug:attr/materialClockStyle = 0x7f0302df
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_frame_stoke_width = 0x7f060334
me.rerere.rikkahub.debug:dimen/mtrl_slider_track_height = 0x7f0602fd
me.rerere.rikkahub.debug:attr/tabTextColor = 0x7f03040a
me.rerere.rikkahub.debug:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601cf
me.rerere.rikkahub.debug:dimen/mtrl_slider_tick_radius = 0x7f0602fc
me.rerere.rikkahub.debug:string/bottomsheet_action_collapse = 0x7f110055
me.rerere.rikkahub.debug:dimen/mtrl_shape_corner_size_large_component = 0x7f0602f2
me.rerere.rikkahub.debug:attr/actionModeSplitBackground = 0x7f03001c
me.rerere.rikkahub.debug:dimen/design_snackbar_padding_vertical = 0x7f060086
me.rerere.rikkahub.debug:style/Widget.AppCompat.ButtonBar = 0x7f1202fe
me.rerere.rikkahub.debug:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060196
me.rerere.rikkahub.debug:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602f0
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000e
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_inset_small = 0x7f0602e7
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060228
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_inset_medium = 0x7f0602e6
me.rerere.rikkahub.debug:style/Widget.Material3.TabLayout = 0x7f1203db
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_inset = 0x7f0602e4
me.rerere.rikkahub.debug:styleable/CustomAttribute = 0x7f13002d
me.rerere.rikkahub.debug:attr/liftOnScrollTargetViewId = 0x7f0302a6
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602e2
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_icon_size = 0x7f0602e0
me.rerere.rikkahub.debug:dimen/mtrl_navigation_elevation = 0x7f0602d5
me.rerere.rikkahub.debug:dimen/mtrl_min_touch_target_size = 0x7f0602d2
me.rerere.rikkahub.debug:id/ignoreRequest = 0x7f0800d4
me.rerere.rikkahub.debug:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602d1
me.rerere.rikkahub.debug:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301d4
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1201c8
me.rerere.rikkahub.debug:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602cf
me.rerere.rikkahub.debug:attr/startIconContentDescription = 0x7f0303c7
me.rerere.rikkahub.debug:attr/motionPathRotate = 0x7f03032a
me.rerere.rikkahub.debug:dimen/mtrl_low_ripple_default_alpha = 0x7f0602ce
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_date_hint = 0x7f110193
me.rerere.rikkahub.debug:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602cd
me.rerere.rikkahub.debug:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06013c
me.rerere.rikkahub.debug:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602cc
me.rerere.rikkahub.debug:dimen/mtrl_fab_translation_z_pressed = 0x7f0602c9
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f12025b
me.rerere.rikkahub.debug:attr/dragDirection = 0x7f030173
me.rerere.rikkahub.debug:dimen/mtrl_fab_min_touch_target = 0x7f0602c7
me.rerere.rikkahub.debug:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060261
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06021b
me.rerere.rikkahub.debug:dimen/tooltip_y_offset_non_touch = 0x7f060331
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f12020a
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_start_padding = 0x7f0602c0
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_icon_size = 0x7f0602bc
me.rerere.rikkahub.debug:dimen/mtrl_tooltip_minHeight = 0x7f060318
me.rerere.rikkahub.debug:attr/selectionRequired = 0x7f030396
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_elevation = 0x7f0602b9
me.rerere.rikkahub.debug:dimen/abc_list_item_height_large_material = 0x7f060030
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_rect_corner_touch_threshold = 0x7f060338
me.rerere.rikkahub.debug:attr/editTextColor = 0x7f030189
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f120214
me.rerere.rikkahub.debug:dimen/design_snackbar_text_size = 0x7f060088
me.rerere.rikkahub.debug:dimen/mtrl_chip_text_size = 0x7f0602b2
me.rerere.rikkahub.debug:style/Widget.Material3.Snackbar.TextView = 0x7f1203da
me.rerere.rikkahub.debug:id/material_timepicker_ok_button = 0x7f08013b
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120135
me.rerere.rikkahub.debug:anim/m3_side_sheet_enter_from_right = 0x7f01002c
me.rerere.rikkahub.debug:dimen/mtrl_card_corner_radius = 0x7f0602ad
me.rerere.rikkahub.debug:string/abc_searchview_description_clear = 0x7f110013
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_container_height = 0x7f060149
me.rerere.rikkahub.debug:layout/leak_canary_heap_render = 0x7f0b0033
me.rerere.rikkahub.debug:dimen/mtrl_calendar_year_height = 0x7f0602a7
me.rerere.rikkahub.debug:drawable/ucrop_ic_crop = 0x7f070116
me.rerere.rikkahub.debug:color/material_dynamic_secondary60 = 0x7f050285
me.rerere.rikkahub.debug:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0602a5
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070027
me.rerere.rikkahub.debug:attr/motionInterpolator = 0x7f030328
me.rerere.rikkahub.debug:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0602a4
me.rerere.rikkahub.debug:dimen/mtrl_calendar_text_input_padding_top = 0x7f0602a3
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_tertiary = 0x7f050210
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060295
me.rerere.rikkahub.debug:drawable/abc_action_bar_item_background_material = 0x7f070029
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_selection_line_height = 0x7f060293
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c4
me.rerere.rikkahub.debug:id/textTop = 0x7f0801fd
me.rerere.rikkahub.debug:string/mtrl_checkbox_state_description_checked = 0x7f110173
me.rerere.rikkahub.debug:dimen/mtrl_calendar_dialog_background_inset = 0x7f06028d
me.rerere.rikkahub.debug:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007a
me.rerere.rikkahub.debug:drawable/abc_tab_indicator_material = 0x7f07006b
me.rerere.rikkahub.debug:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601ee
me.rerere.rikkahub.debug:dimen/mtrl_calendar_action_padding = 0x7f060283
me.rerere.rikkahub.debug:attr/materialCardViewOutlinedStyle = 0x7f0302dc
me.rerere.rikkahub.debug:dimen/mtrl_btn_z = 0x7f060280
me.rerere.rikkahub.debug:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0500cb
me.rerere.rikkahub.debug:style/Base.V22.Theme.AppCompat.Light = 0x7f1200ae
me.rerere.rikkahub.debug:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06027c
me.rerere.rikkahub.debug:string/material_motion_easing_standard = 0x7f11014e
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06017c
me.rerere.rikkahub.debug:drawable/leak_canary_gray_fill = 0x7f0700b0
me.rerere.rikkahub.debug:dimen/mtrl_btn_padding_left = 0x7f060276
me.rerere.rikkahub.debug:color/m3_radiobutton_button_tint = 0x7f0500d0
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c0
me.rerere.rikkahub.debug:drawable/leak_canary_icon = 0x7f0700b1
me.rerere.rikkahub.debug:dimen/mtrl_btn_padding_bottom = 0x7f060275
me.rerere.rikkahub.debug:id/pooling_container_listener_holder_tag = 0x7f080192
me.rerere.rikkahub.debug:dimen/design_tab_text_size_2line = 0x7f06008c
me.rerere.rikkahub.debug:id/leak_canary_row_connector = 0x7f080112
me.rerere.rikkahub.debug:dimen/mtrl_btn_icon_padding = 0x7f060271
me.rerere.rikkahub.debug:color/teal_700 = 0x7f050341
me.rerere.rikkahub.debug:color/leak_canary_gray_lightest = 0x7f05007a
me.rerere.rikkahub.debug:dimen/mtrl_btn_hovered_z = 0x7f06026f
me.rerere.rikkahub.debug:dimen/mtrl_badge_with_text_size = 0x7f060262
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline6 = 0x7f120208
me.rerere.rikkahub.debug:string/m3_ref_typeface_plain_regular = 0x7f1100f5
me.rerere.rikkahub.debug:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060260
me.rerere.rikkahub.debug:dimen/mtrl_badge_size = 0x7f06025d
me.rerere.rikkahub.debug:dimen/material_time_picker_minimum_screen_width = 0x7f060255
me.rerere.rikkahub.debug:id/textSpacerNoButtons = 0x7f0801fa
me.rerere.rikkahub.debug:dimen/material_textinput_max_width = 0x7f060252
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f120255
me.rerere.rikkahub.debug:attr/layout = 0x7f030261
me.rerere.rikkahub.debug:dimen/material_textinput_default_width = 0x7f060251
me.rerere.rikkahub.debug:dimen/notification_content_margin_start = 0x7f06031f
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501f1
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator = 0x7f120377
me.rerere.rikkahub.debug:dimen/material_helper_text_font_1_3_padding_top = 0x7f06024f
me.rerere.rikkahub.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120015
me.rerere.rikkahub.debug:attr/badgeWithTextWidth = 0x7f030060
me.rerere.rikkahub.debug:dimen/mtrl_slider_label_padding = 0x7f0602f6
me.rerere.rikkahub.debug:attr/tickMark = 0x7f030458
me.rerere.rikkahub.debug:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06024b
me.rerere.rikkahub.debug:string/mtrl_picker_range_header_selected = 0x7f11018e
me.rerere.rikkahub.debug:dimen/material_divider_thickness = 0x7f060242
me.rerere.rikkahub.debug:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700cf
me.rerere.rikkahub.debug:dimen/material_cursor_inset = 0x7f060240
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1203ee
me.rerere.rikkahub.debug:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0602a8
me.rerere.rikkahub.debug:attr/drawerLayoutCornerSize = 0x7f030181
me.rerere.rikkahub.debug:dimen/material_clock_size = 0x7f06023f
me.rerere.rikkahub.debug:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060314
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700d8
me.rerere.rikkahub.debug:color/material_personalized_color_on_background = 0x7f0502b5
me.rerere.rikkahub.debug:dimen/material_clock_period_toggle_height = 0x7f06023b
me.rerere.rikkahub.debug:layout/mtrl_alert_dialog = 0x7f0b0050
me.rerere.rikkahub.debug:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601d2
me.rerere.rikkahub.debug:dimen/m3_comp_switch_disabled_track_opacity = 0x7f0601a1
me.rerere.rikkahub.debug:dimen/material_clock_hand_padding = 0x7f060238
me.rerere.rikkahub.debug:string/path_password_eye_mask_visible = 0x7f1101ae
me.rerere.rikkahub.debug:dimen/material_clock_display_width = 0x7f060234
me.rerere.rikkahub.debug:attr/tickRadiusActive = 0x7f03045b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501dc
me.rerere.rikkahub.debug:dimen/material_clock_display_padding = 0x7f060233
me.rerere.rikkahub.debug:color/design_fab_stroke_top_outer_color = 0x7f05005e
me.rerere.rikkahub.debug:style/Widget.Material3.BottomAppBar = 0x7f12034c
me.rerere.rikkahub.debug:attr/backgroundTint = 0x7f03004e
me.rerere.rikkahub.debug:dimen/m3_timepicker_display_stroke_width = 0x7f06022e
me.rerere.rikkahub.debug:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06022d
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602df
me.rerere.rikkahub.debug:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06022a
me.rerere.rikkahub.debug:id/deltaRelative = 0x7f080089
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060225
me.rerere.rikkahub.debug:id/clip_vertical = 0x7f08006b
me.rerere.rikkahub.debug:drawable/ic_clock_black_24dp = 0x7f0700a3
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060222
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c012f
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060221
me.rerere.rikkahub.debug:attr/measureWithLargestChild = 0x7f0302fb
me.rerere.rikkahub.debug:string/assistant_page_creative = 0x7f11002a
me.rerere.rikkahub.debug:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060310
me.rerere.rikkahub.debug:mipmap/ic_launcher_background = 0x7f0e0001
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06021c
me.rerere.rikkahub.debug:id/navigation_bar_item_small_label_view = 0x7f080169
me.rerere.rikkahub.debug:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_dark_default = 0x7f050031
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060219
me.rerere.rikkahub.debug:drawable/$m3_avd_show_password__1 = 0x7f07000a
me.rerere.rikkahub.debug:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602b3
me.rerere.rikkahub.debug:drawable/notify_panel_notification_icon_bg = 0x7f070105
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060217
me.rerere.rikkahub.debug:style/Base.V26.Theme.AppCompat = 0x7f1200b5
me.rerere.rikkahub.debug:string/chat_page_export_markdown_desc = 0x7f110070
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060211
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_emphasized = 0x7f1100f6
me.rerere.rikkahub.debug:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
me.rerere.rikkahub.debug:id/dimensions = 0x7f080090
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level0 = 0x7f060200
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1201b6
me.rerere.rikkahub.debug:id/adjust_width = 0x7f080045
me.rerere.rikkahub.debug:dimen/m3_snackbar_margin = 0x7f0601ff
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ed
me.rerere.rikkahub.debug:color/m3_tabs_text_color = 0x7f050233
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_elevation = 0x7f060062
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TabLayout = 0x7f1202bd
me.rerere.rikkahub.debug:layout/m3_alert_dialog_actions = 0x7f0b003e
me.rerere.rikkahub.debug:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601fe
me.rerere.rikkahub.debug:styleable/NavAction = 0x7f130068
me.rerere.rikkahub.debug:dimen/m3_small_fab_max_image_size = 0x7f0601fc
me.rerere.rikkahub.debug:dimen/mtrl_slider_halo_radius = 0x7f0602f5
me.rerere.rikkahub.debug:color/material_personalized_color_surface_container = 0x7f0502ce
me.rerere.rikkahub.debug:dimen/design_fab_image_size = 0x7f060070
me.rerere.rikkahub.debug:drawable/abc_list_pressed_holo_dark = 0x7f070050
me.rerere.rikkahub.debug:dimen/m3_slider_thumb_elevation = 0x7f0601fb
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f12006c
me.rerere.rikkahub.debug:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
me.rerere.rikkahub.debug:attr/layout_goneMarginLeft = 0x7f030299
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral30 = 0x7f05013f
me.rerere.rikkahub.debug:attr/thumbStrokeWidth = 0x7f03044f
me.rerere.rikkahub.debug:attr/colorBackgroundFloating = 0x7f0300e1
me.rerere.rikkahub.debug:drawable/abc_list_divider_material = 0x7f07004c
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012c
me.rerere.rikkahub.debug:dimen/m3_comp_elevated_button_container_elevation = 0x7f060118
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001e
me.rerere.rikkahub.debug:color/abc_search_url_text = 0x7f05000d
me.rerere.rikkahub.debug:dimen/m3_side_sheet_width = 0x7f0601f8
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f1203fc
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary30 = 0x7f050121
me.rerere.rikkahub.debug:dimen/m3_side_sheet_modal_elevation = 0x7f0601f6
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary40 = 0x7f05016e
me.rerere.rikkahub.debug:dimen/m3_searchview_height = 0x7f0601f4
me.rerere.rikkahub.debug:dimen/m3_searchview_elevation = 0x7f0601f3
me.rerere.rikkahub.debug:dimen/m3_searchview_divider_size = 0x7f0601f2
me.rerere.rikkahub.debug:style/Platform.V25.AppCompat.Light = 0x7f120147
me.rerere.rikkahub.debug:dimen/m3_searchbar_text_size = 0x7f0601f1
me.rerere.rikkahub.debug:dimen/m3_searchbar_padding_start = 0x7f0601ef
me.rerere.rikkahub.debug:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601f0
me.rerere.rikkahub.debug:dimen/m3_searchbar_margin_vertical = 0x7f0601ed
me.rerere.rikkahub.debug:color/abc_tint_edittext = 0x7f050015
me.rerere.rikkahub.debug:string/leak_canary_delete_all_leaks_title = 0x7f1100b9
me.rerere.rikkahub.debug:attr/colorSurfaceContainerLowest = 0x7f030114
me.rerere.rikkahub.debug:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070060
me.rerere.rikkahub.debug:dimen/m3_searchbar_elevation = 0x7f0601ea
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f120055
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06015a
me.rerere.rikkahub.debug:dimen/notification_top_pad = 0x7f060329
me.rerere.rikkahub.debug:id/light = 0x7f080120
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0133
me.rerere.rikkahub.debug:dimen/m3_ripple_pressed_alpha = 0x7f0601e8
me.rerere.rikkahub.debug:dimen/m3_ripple_focused_alpha = 0x7f0601e6
me.rerere.rikkahub.debug:dimen/m3_ripple_default_alpha = 0x7f0601e5
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601e3
me.rerere.rikkahub.debug:color/m3_dynamic_default_color_primary_text = 0x7f0500b6
me.rerere.rikkahub.debug:attr/colorSurfaceDim = 0x7f030115
me.rerere.rikkahub.debug:color/m3_navigation_item_ripple_color = 0x7f0500c9
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601e0
me.rerere.rikkahub.debug:color/material_dynamic_primary100 = 0x7f050273
me.rerere.rikkahub.debug:attr/chipIconEnabled = 0x7f0300b5
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_min_height = 0x7f0601df
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601de
me.rerere.rikkahub.debug:style/TextAppearance.Material3.TitleSmall = 0x7f1201fc
me.rerere.rikkahub.debug:attr/cornerSize = 0x7f03013e
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601dd
me.rerere.rikkahub.debug:color/mtrl_calendar_item_stroke_color = 0x7f0502f4
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602e5
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant0 = 0x7f050264
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_icon_size = 0x7f0601db
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c013f
me.rerere.rikkahub.debug:dimen/m3_menu_elevation = 0x7f0601cc
me.rerere.rikkahub.debug:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601cb
me.rerere.rikkahub.debug:dimen/m3_fab_translation_z_pressed = 0x7f0601c8
me.rerere.rikkahub.debug:attr/materialDividerStyle = 0x7f0302e2
me.rerere.rikkahub.debug:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06023c
me.rerere.rikkahub.debug:dimen/m3_fab_border_width = 0x7f0601c5
me.rerere.rikkahub.debug:color/m3_sys_color_light_error_container = 0x7f050203
me.rerere.rikkahub.debug:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060249
me.rerere.rikkahub.debug:attr/drawPath = 0x7f030176
me.rerere.rikkahub.debug:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601bc
me.rerere.rikkahub.debug:string/in_progress = 0x7f1100aa
me.rerere.rikkahub.debug:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601bb
me.rerere.rikkahub.debug:layout/mtrl_calendar_horizontal = 0x7f0b005a
me.rerere.rikkahub.debug:integer/mtrl_calendar_header_orientation = 0x7f090032
me.rerere.rikkahub.debug:color/m3_sys_color_light_error = 0x7f050202
me.rerere.rikkahub.debug:dimen/m3_comp_slider_active_handle_height = 0x7f060191
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_logo_size = 0x7f060336
me.rerere.rikkahub.debug:string/citations_count = 0x7f11007d
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601b5
me.rerere.rikkahub.debug:attr/materialCalendarHeaderSelection = 0x7f0302d2
me.rerere.rikkahub.debug:dimen/m3_appbar_size_large = 0x7f0600ba
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601b4
me.rerere.rikkahub.debug:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0601ae
me.rerere.rikkahub.debug:dimen/m3_navigation_item_shape_inset_start = 0x7f0601d4
me.rerere.rikkahub.debug:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0601ac
me.rerere.rikkahub.debug:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003e
me.rerere.rikkahub.debug:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070043
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary80 = 0x7f050126
me.rerere.rikkahub.debug:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060248
me.rerere.rikkahub.debug:id/state_brightness = 0x7f0801db
me.rerere.rikkahub.debug:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0601aa
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1202fb
me.rerere.rikkahub.debug:string/assistant_page_default_assistant = 0x7f11002d
me.rerere.rikkahub.debug:dimen/m3_comp_switch_track_width = 0x7f0601a8
me.rerere.rikkahub.debug:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06019c
me.rerere.rikkahub.debug:attr/autoSizeTextType = 0x7f030042
me.rerere.rikkahub.debug:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06019b
me.rerere.rikkahub.debug:attr/contentPaddingLeft = 0x7f03012f
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0137
me.rerere.rikkahub.debug:id/accessibility_custom_action_26 = 0x7f080023
me.rerere.rikkahub.debug:attr/materialAlertDialogTitleIconStyle = 0x7f0302c5
me.rerere.rikkahub.debug:dimen/m3_comp_suggestion_chip_container_height = 0x7f06019a
me.rerere.rikkahub.debug:string/material_slider_value = 0x7f110151
me.rerere.rikkahub.debug:attr/tooltipText = 0x7f030478
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06018c
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06018a
me.rerere.rikkahub.debug:styleable/MenuItem = 0x7f130060
me.rerere.rikkahub.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
me.rerere.rikkahub.debug:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060189
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f120254
me.rerere.rikkahub.debug:color/material_personalized_hint_foreground_inverse = 0x7f0502de
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_dark = 0x7f07008b
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f120311
me.rerere.rikkahub.debug:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0055
me.rerere.rikkahub.debug:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06019e
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f06021d
me.rerere.rikkahub.debug:color/notification_icon_bg_color = 0x7f050324
me.rerere.rikkahub.debug:attr/layout_constraintGuide_percent = 0x7f030279
me.rerere.rikkahub.debug:dimen/m3_comp_search_view_container_elevation = 0x7f060183
me.rerere.rikkahub.debug:drawable/abc_ic_voice_search_api_material = 0x7f070049
me.rerere.rikkahub.debug:dimen/mtrl_btn_elevation = 0x7f06026d
me.rerere.rikkahub.debug:color/design_default_color_secondary_variant = 0x7f050055
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500f3
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06017b
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06017a
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary100 = 0x7f05016b
me.rerere.rikkahub.debug:id/leak_canary_toast_icon = 0x7f08011b
me.rerere.rikkahub.debug:attr/thumbIconTint = 0x7f03044b
me.rerere.rikkahub.debug:id/leak_canary_explorer_list = 0x7f0800fa
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060179
me.rerere.rikkahub.debug:attr/motionDurationShort4 = 0x7f03031b
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_path_unchecked = 0x7f1101a3
me.rerere.rikkahub.debug:attr/layout_constraintHeight_percent = 0x7f03027d
me.rerere.rikkahub.debug:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060172
me.rerere.rikkahub.debug:color/abc_secondary_text_material_dark = 0x7f050011
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060171
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700db
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06016c
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06016b
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_padding_top = 0x7f0601e2
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_card_outline_width = 0x7f060163
me.rerere.rikkahub.debug:attr/suffixTextAppearance = 0x7f0303e7
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_card_icon_size = 0x7f060162
me.rerere.rikkahub.debug:attr/dropDownListViewStyle = 0x7f030184
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_button_outline_width = 0x7f06015f
me.rerere.rikkahub.debug:style/Base.Widget.Design.TabLayout = 0x7f1200fc
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f06015e
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06012e
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f060159
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070016
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060157
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_margin = 0x7f060303
me.rerere.rikkahub.debug:color/material_personalized_color_on_surface_variant = 0x7f0502be
me.rerere.rikkahub.debug:layout/notification_template_part_chronometer = 0x7f0b0074
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060156
me.rerere.rikkahub.debug:string/default_popup_window_title = 0x7f11009a
me.rerere.rikkahub.debug:attr/boxBackgroundMode = 0x7f03007a
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.CompactMenu = 0x7f12004c
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_container_elevation = 0x7f060148
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060146
me.rerere.rikkahub.debug:string/leak_canary_notification_retained_dump_failed = 0x7f1100dd
me.rerere.rikkahub.debug:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060143
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1200e0
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1201ba
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_icon_size = 0x7f060138
me.rerere.rikkahub.debug:attr/placeholder_emptyVisibility = 0x7f030364
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060135
me.rerere.rikkahub.debug:color/abc_search_url_text_normal = 0x7f05000e
me.rerere.rikkahub.debug:string/ucrop_saturation = 0x7f11020f
me.rerere.rikkahub.debug:dimen/mtrl_badge_text_size = 0x7f06025f
me.rerere.rikkahub.debug:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700e5
me.rerere.rikkahub.debug:attr/motionDurationMedium1 = 0x7f030314
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_container_elevation = 0x7f060134
me.rerere.rikkahub.debug:layout/abc_activity_chooser_view = 0x7f0b0006
me.rerere.rikkahub.debug:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060133
me.rerere.rikkahub.debug:layout/design_navigation_item_header = 0x7f0b0025
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060130
me.rerere.rikkahub.debug:attr/badgeText = 0x7f030056
me.rerere.rikkahub.debug:color/m3_slider_inactive_track_color = 0x7f050189
me.rerere.rikkahub.debug:string/call_notification_decline_action = 0x7f11005d
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601e1
me.rerere.rikkahub.debug:id/circle_center = 0x7f080068
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06012c
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0032
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060124
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
me.rerere.rikkahub.debug:id/ifRoom = 0x7f0800d2
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060123
me.rerere.rikkahub.debug:attr/tickColor = 0x7f030455
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060122
me.rerere.rikkahub.debug:id/mtrl_calendar_selection_frame = 0x7f080151
me.rerere.rikkahub.debug:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_container_width = 0x7f06014e
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f06020a
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06011c
me.rerere.rikkahub.debug:dimen/m3_comp_divider_thickness = 0x7f060117
me.rerere.rikkahub.debug:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060114
me.rerere.rikkahub.debug:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060113
me.rerere.rikkahub.debug:attr/alertDialogCenterButtons = 0x7f030029
me.rerere.rikkahub.debug:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060111
me.rerere.rikkahub.debug:attr/materialCalendarFullscreenTheme = 0x7f0302cd
me.rerere.rikkahub.debug:dimen/material_emphasis_high_type = 0x7f060245
me.rerere.rikkahub.debug:dimen/m3_comp_badge_size = 0x7f060110
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary70 = 0x7f05010b
me.rerere.rikkahub.debug:dimen/mtrl_textinput_box_stroke_width_default = 0x7f06030f
me.rerere.rikkahub.debug:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f06010c
me.rerere.rikkahub.debug:attr/foregroundInsidePadding = 0x7f0301fb
me.rerere.rikkahub.debug:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060192
me.rerere.rikkahub.debug:dimen/m3_chip_icon_size = 0x7f060109
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0030
me.rerere.rikkahub.debug:dimen/m3_chip_hovered_translation_z = 0x7f060108
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant99 = 0x7f050270
me.rerere.rikkahub.debug:dimen/m3_chip_disabled_translation_z = 0x7f060105
me.rerere.rikkahub.debug:id/startToEnd = 0x7f0801d8
me.rerere.rikkahub.debug:dimen/m3_carousel_gone_size = 0x7f0600ff
me.rerere.rikkahub.debug:color/secondary_text_disabled_material_light = 0x7f050339
me.rerere.rikkahub.debug:dimen/mtrl_calendar_landscape_header_width = 0x7f060297
me.rerere.rikkahub.debug:dimen/tooltip_y_offset_touch = 0x7f060332
me.rerere.rikkahub.debug:dimen/m3_btn_translation_z_base = 0x7f0600f2
me.rerere.rikkahub.debug:attr/dividerThickness = 0x7f030171
me.rerere.rikkahub.debug:string/leak_canary_permission_not_granted = 0x7f1100e4
me.rerere.rikkahub.debug:dimen/m3_btn_text_btn_padding_left = 0x7f0600f0
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Snackbar = 0x7f12044a
me.rerere.rikkahub.debug:attr/materialSearchViewToolbarHeight = 0x7f0302ea
me.rerere.rikkahub.debug:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600ef
me.rerere.rikkahub.debug:drawable/abc_vector_test = 0x7f070076
me.rerere.rikkahub.debug:color/material_personalized_color_tertiary = 0x7f0502d6
me.rerere.rikkahub.debug:id/scrollIndicatorDown = 0x7f0801ae
me.rerere.rikkahub.debug:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f06010d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f12041f
me.rerere.rikkahub.debug:attr/saturation = 0x7f030389
me.rerere.rikkahub.debug:string/chat_page_restore_context = 0x7f110215
me.rerere.rikkahub.debug:dimen/m3_btn_padding_right = 0x7f0600eb
me.rerere.rikkahub.debug:dimen/m3_btn_padding_left = 0x7f0600ea
me.rerere.rikkahub.debug:dimen/m3_btn_max_width = 0x7f0600e8
me.rerere.rikkahub.debug:dimen/material_clock_face_margin_bottom = 0x7f060235
me.rerere.rikkahub.debug:id/image_view_state_aspect_ratio = 0x7f0800d8
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant30 = 0x7f050153
me.rerere.rikkahub.debug:dimen/mtrl_btn_disabled_elevation = 0x7f06026b
me.rerere.rikkahub.debug:color/ucrop_color_widget_active = 0x7f050357
me.rerere.rikkahub.debug:string/leak_canary_options_menu_render_heap_dump = 0x7f1100e3
me.rerere.rikkahub.debug:drawable/abc_list_focused_holo = 0x7f07004e
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Small = 0x7f1201af
me.rerere.rikkahub.debug:dimen/m3_btn_icon_btn_padding_left = 0x7f0600e1
me.rerere.rikkahub.debug:id/view_tree_lifecycle_owner = 0x7f080230
me.rerere.rikkahub.debug:color/material_dynamic_secondary80 = 0x7f050287
me.rerere.rikkahub.debug:dimen/m3_datepicker_elevation = 0x7f0601bd
me.rerere.rikkahub.debug:id/navigation_header_container = 0x7f08016a
me.rerere.rikkahub.debug:id/text_view_rotate = 0x7f080204
me.rerere.rikkahub.debug:id/src_atop = 0x7f0801d2
me.rerere.rikkahub.debug:dimen/m3_btn_disabled_translation_z = 0x7f0600de
me.rerere.rikkahub.debug:dimen/m3_btn_disabled_elevation = 0x7f0600dd
me.rerere.rikkahub.debug:string/edit = 0x7f11009c
me.rerere.rikkahub.debug:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
me.rerere.rikkahub.debug:dimen/m3_btn_dialog_btn_spacing = 0x7f0600dc
me.rerere.rikkahub.debug:color/m3_timepicker_secondary_text_button_text_color = 0x7f050245
me.rerere.rikkahub.debug:attr/materialDividerHeavyStyle = 0x7f0302e1
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_vertical_padding = 0x7f06028a
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1202a6
me.rerere.rikkahub.debug:dimen/m3_navigation_item_icon_padding = 0x7f0601d1
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_input_mode = 0x7f110116
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_height = 0x7f0600d9
me.rerere.rikkahub.debug:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f06013f
me.rerere.rikkahub.debug:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600d4
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_min_height = 0x7f0600d1
me.rerere.rikkahub.debug:attr/activityChooserViewStyle = 0x7f030026
me.rerere.rikkahub.debug:attr/textColorSearchUrl = 0x7f030438
me.rerere.rikkahub.debug:dimen/m3_navigation_item_vertical_padding = 0x7f0601d6
me.rerere.rikkahub.debug:attr/itemPaddingBottom = 0x7f03023d
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_item_padding_top = 0x7f0600d0
me.rerere.rikkahub.debug:string/leak_canary_about_enable_heap_dump_textOff = 0x7f1100ae
me.rerere.rikkahub.debug:attr/autoShowKeyboard = 0x7f03003d
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600cd
me.rerere.rikkahub.debug:drawable/abc_btn_check_material = 0x7f07002b
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600cc
me.rerere.rikkahub.debug:string/assistant_page_delete_body = 0x7f11002f
me.rerere.rikkahub.debug:dimen/m3_badge_size = 0x7f0600c5
me.rerere.rikkahub.debug:attr/actionModeCloseDrawable = 0x7f030014
me.rerere.rikkahub.debug:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600c2
me.rerere.rikkahub.debug:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600bf
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060266
me.rerere.rikkahub.debug:dimen/mtrl_calendar_navigation_height = 0x7f06029c
me.rerere.rikkahub.debug:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600be
me.rerere.rikkahub.debug:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600bc
me.rerere.rikkahub.debug:color/bright_foreground_disabled_material_light = 0x7f050023
me.rerere.rikkahub.debug:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600b8
me.rerere.rikkahub.debug:attr/drawableEndCompat = 0x7f030178
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Light = 0x7f1202d7
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0051
me.rerere.rikkahub.debug:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600b5
me.rerere.rikkahub.debug:attr/percentX = 0x7f03035d
me.rerere.rikkahub.debug:color/material_dynamic_tertiary40 = 0x7f050290
me.rerere.rikkahub.debug:color/material_personalized_color_on_primary = 0x7f0502b8
me.rerere.rikkahub.debug:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0500c4
me.rerere.rikkahub.debug:color/material_dynamic_neutral30 = 0x7f05025b
me.rerere.rikkahub.debug:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600b4
me.rerere.rikkahub.debug:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1200be
me.rerere.rikkahub.debug:style/Widget.Design.NavigationView = 0x7f12033d
me.rerere.rikkahub.debug:drawable/abc_switch_track_mtrl_alpha = 0x7f07006a
me.rerere.rikkahub.debug:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0601af
me.rerere.rikkahub.debug:dimen/leak_canary_squiggly_span_period_degrees = 0x7f0600a9
me.rerere.rikkahub.debug:string/material_slider_range_start = 0x7f110150
me.rerere.rikkahub.debug:dimen/leak_canary_row_min = 0x7f0600a6
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060165
me.rerere.rikkahub.debug:dimen/leak_canary_more_stroke_width = 0x7f0600a4
me.rerere.rikkahub.debug:dimen/abc_dialog_min_width_minor = 0x7f060023
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0090
me.rerere.rikkahub.debug:dimen/leak_canary_more_margin_top = 0x7f0600a2
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary100 = 0x7f05011f
me.rerere.rikkahub.debug:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c0049
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface = 0x7f0501c8
me.rerere.rikkahub.debug:dimen/leak_canary_connector_stroke_size = 0x7f0600a0
me.rerere.rikkahub.debug:color/primary_text_default_material_light = 0x7f05032a
me.rerere.rikkahub.debug:color/m3_tabs_icon_color_secondary = 0x7f050230
me.rerere.rikkahub.debug:dimen/leak_canary_connector_leak_dash_gap = 0x7f06009e
me.rerere.rikkahub.debug:attr/theme = 0x7f030444
me.rerere.rikkahub.debug:dimen/material_emphasis_disabled = 0x7f060243
me.rerere.rikkahub.debug:dimen/hint_pressed_alpha_material_dark = 0x7f060098
me.rerere.rikkahub.debug:drawable/ucrop_shadow_upside = 0x7f070128
me.rerere.rikkahub.debug:color/design_dark_default_color_primary = 0x7f050044
me.rerere.rikkahub.debug:dimen/highlight_alpha_material_dark = 0x7f060094
me.rerere.rikkahub.debug:dimen/mtrl_calendar_action_height = 0x7f060282
me.rerere.rikkahub.debug:attr/expandActivityOverflowButtonDrawable = 0x7f0301a8
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f120267
me.rerere.rikkahub.debug:dimen/disabled_alpha_material_dark = 0x7f06008e
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Dialog = 0x7f12029e
me.rerere.rikkahub.debug:attr/motion_triggerOnCollision = 0x7f03032f
me.rerere.rikkahub.debug:attr/defaultQueryHint = 0x7f030160
me.rerere.rikkahub.debug:style/Widget.Material3.CollapsingToolbar.Large = 0x7f120380
me.rerere.rikkahub.debug:dimen/design_textinput_caption_translate_y = 0x7f06008d
me.rerere.rikkahub.debug:dimen/mtrl_btn_max_width = 0x7f060274
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f120038
me.rerere.rikkahub.debug:attr/ucrop_aspect_ratio_x = 0x7f030497
me.rerere.rikkahub.debug:dimen/design_tab_scrollable_min_width = 0x7f06008a
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013c
me.rerere.rikkahub.debug:attr/logo = 0x7f0302ba
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_disabled = 0x7f07008f
me.rerere.rikkahub.debug:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0115
me.rerere.rikkahub.debug:dimen/design_snackbar_max_width = 0x7f060083
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_container_width = 0x7f060158
me.rerere.rikkahub.debug:string/abc_toolbar_collapse_description = 0x7f11001a
me.rerere.rikkahub.debug:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_container_elevation = 0x7f0601b0
me.rerere.rikkahub.debug:id/baseline = 0x7f080055
me.rerere.rikkahub.debug:dimen/design_snackbar_background_corner_radius = 0x7f060080
me.rerere.rikkahub.debug:attr/placeholderText = 0x7f030361
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CheckedTextView = 0x7f120410
me.rerere.rikkahub.debug:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
me.rerere.rikkahub.debug:dimen/mtrl_calendar_year_vertical_padding = 0x7f0602a9
me.rerere.rikkahub.debug:dimen/design_navigation_max_width = 0x7f06007b
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
me.rerere.rikkahub.debug:dimen/design_navigation_item_icon_padding = 0x7f060079
me.rerere.rikkahub.debug:attr/boxCornerRadiusTopEnd = 0x7f03007e
me.rerere.rikkahub.debug:dimen/design_navigation_icon_size = 0x7f060077
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Chip.Action = 0x7f120411
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.DayNight = 0x7f12027f
me.rerere.rikkahub.debug:dimen/design_fab_translation_z_pressed = 0x7f060074
me.rerere.rikkahub.debug:string/m3c_date_input_no_input_description = 0x7f11010c
me.rerere.rikkahub.debug:dimen/design_fab_size_mini = 0x7f060071
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f120182
me.rerere.rikkahub.debug:dimen/design_fab_elevation = 0x7f06006f
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1202a9
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline4 = 0x7f120206
me.rerere.rikkahub.debug:color/material_on_primary_emphasis_high_type = 0x7f0502a7
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_shadow_height = 0x7f060069
me.rerere.rikkahub.debug:attr/stackFromEnd = 0x7f0303c3
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_margin = 0x7f060068
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_label_padding = 0x7f060067
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
me.rerere.rikkahub.debug:attr/listPreferredItemHeight = 0x7f0302b3
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
me.rerere.rikkahub.debug:dimen/def_drawer_elevation = 0x7f06005d
me.rerere.rikkahub.debug:attr/controlBackground = 0x7f030135
me.rerere.rikkahub.debug:dimen/compat_button_padding_horizontal_material = 0x7f060058
me.rerere.rikkahub.debug:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.EditText = 0x7f1200db
me.rerere.rikkahub.debug:dimen/compat_button_inset_vertical_material = 0x7f060057
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1203e3
me.rerere.rikkahub.debug:dimen/cardview_compat_inset_shadow = 0x7f060052
me.rerere.rikkahub.debug:color/m3_slider_inactive_track_color_legacy = 0x7f05018a
me.rerere.rikkahub.debug:dimen/abc_text_size_title_material_toolbar = 0x7f060050
me.rerere.rikkahub.debug:dimen/abc_text_size_small_material = 0x7f06004c
me.rerere.rikkahub.debug:dimen/fastscroll_minimum_range = 0x7f060092
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f06028f
me.rerere.rikkahub.debug:attr/keyPositionType = 0x7f030256
me.rerere.rikkahub.debug:dimen/abc_text_size_menu_header_material = 0x7f06004a
me.rerere.rikkahub.debug:id/textinput_error = 0x7f080209
me.rerere.rikkahub.debug:string/abc_menu_sym_shortcut_label = 0x7f110010
me.rerere.rikkahub.debug:styleable/CardView = 0x7f13001b
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f12009c
me.rerere.rikkahub.debug:dimen/abc_text_size_display_2_material = 0x7f060044
me.rerere.rikkahub.debug:dimen/mtrl_progress_track_thickness = 0x7f0602f1
me.rerere.rikkahub.debug:attr/haloColor = 0x7f030201
me.rerere.rikkahub.debug:drawable/abc_btn_radio_material_anim = 0x7f070032
me.rerere.rikkahub.debug:dimen/abc_text_size_button_material = 0x7f060041
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500f0
me.rerere.rikkahub.debug:string/setting_model_page_title_model = 0x7f1101c8
me.rerere.rikkahub.debug:layout/design_navigation_item_separator = 0x7f0b0026
me.rerere.rikkahub.debug:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f06024e
me.rerere.rikkahub.debug:color/m3_calendar_item_stroke_color = 0x7f05009f
me.rerere.rikkahub.debug:dimen/abc_star_small = 0x7f06003d
me.rerere.rikkahub.debug:drawable/abc_list_pressed_holo_light = 0x7f070051
me.rerere.rikkahub.debug:dimen/abc_star_medium = 0x7f06003c
me.rerere.rikkahub.debug:attr/textInputStyle = 0x7f030441
me.rerere.rikkahub.debug:dimen/abc_star_big = 0x7f06003b
me.rerere.rikkahub.debug:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
me.rerere.rikkahub.debug:id/CTRL = 0x7f080003
me.rerere.rikkahub.debug:attr/customIntegerValue = 0x7f030154
me.rerere.rikkahub.debug:attr/layout_goneMarginStart = 0x7f03029b
me.rerere.rikkahub.debug:dimen/abc_seekbar_track_background_height_material = 0x7f060038
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TonalButton.Icon = 0x7f120366
me.rerere.rikkahub.debug:color/material_dynamic_tertiary0 = 0x7f05028b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501f0
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_min_height = 0x7f0602be
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500f7
me.rerere.rikkahub.debug:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
me.rerere.rikkahub.debug:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
me.rerere.rikkahub.debug:string/m3c_time_picker_am = 0x7f110130
me.rerere.rikkahub.debug:dimen/abc_disabled_alpha_material_light = 0x7f060028
me.rerere.rikkahub.debug:id/showHome = 0x7f0801c2
me.rerere.rikkahub.debug:dimen/abc_disabled_alpha_material_dark = 0x7f060027
me.rerere.rikkahub.debug:dimen/abc_dialog_title_divider_material = 0x7f060026
me.rerere.rikkahub.debug:attr/limitBoundsTo = 0x7f0302a7
me.rerere.rikkahub.debug:attr/trackDecorationTintMode = 0x7f030484
me.rerere.rikkahub.debug:dimen/mtrl_btn_inset = 0x7f060272
me.rerere.rikkahub.debug:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f120435
me.rerere.rikkahub.debug:dimen/abc_dialog_corner_radius_material = 0x7f06001b
me.rerere.rikkahub.debug:attr/errorIconTint = 0x7f0301a2
me.rerere.rikkahub.debug:attr/restoreState = 0x7f030383
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001e
me.rerere.rikkahub.debug:layout/notification_template_part_time = 0x7f0b0075
me.rerere.rikkahub.debug:dimen/compat_notification_large_icon_max_height = 0x7f06005b
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007a
me.rerere.rikkahub.debug:dimen/abc_control_inset_material = 0x7f060019
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0073
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f12009b
me.rerere.rikkahub.debug:attr/state_above_anchor = 0x7f0303cd
me.rerere.rikkahub.debug:dimen/abc_button_padding_vertical_material = 0x7f060015
me.rerere.rikkahub.debug:string/assistant_page_cancel = 0x7f110025
me.rerere.rikkahub.debug:color/design_default_color_on_error = 0x7f05004d
me.rerere.rikkahub.debug:dimen/abc_button_padding_horizontal_material = 0x7f060014
me.rerere.rikkahub.debug:id/embed = 0x7f0800a4
me.rerere.rikkahub.debug:dimen/leak_canary_squiggly_span_stroke_width = 0x7f0600aa
me.rerere.rikkahub.debug:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
me.rerere.rikkahub.debug:id/buttonPanel = 0x7f08005c
me.rerere.rikkahub.debug:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f0501bd
me.rerere.rikkahub.debug:layout/support_simple_spinner_dropdown_item = 0x7f0b007b
me.rerere.rikkahub.debug:string/setting_display_page_chat_list_model_icon_desc = 0x7f1101bf
me.rerere.rikkahub.debug:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
me.rerere.rikkahub.debug:attr/shapeAppearanceOverlay = 0x7f0303a0
me.rerere.rikkahub.debug:dimen/mtrl_btn_text_btn_padding_right = 0x7f06027e
me.rerere.rikkahub.debug:dimen/abc_action_bar_stacked_max_height = 0x7f060009
me.rerere.rikkahub.debug:layout/mtrl_picker_header_toggle = 0x7f0b006b
me.rerere.rikkahub.debug:dimen/ucrop_height_horizontal_wheel_progress_line = 0x7f06033c
me.rerere.rikkahub.debug:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
me.rerere.rikkahub.debug:color/leak_canary_heap_instance = 0x7f050084
me.rerere.rikkahub.debug:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0602a1
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501fa
me.rerere.rikkahub.debug:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
me.rerere.rikkahub.debug:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
me.rerere.rikkahub.debug:menu/ucrop_menu_activity = 0x7f0d0000
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060190
me.rerere.rikkahub.debug:attr/backgroundOverlayColorAlpha = 0x7f03004b
me.rerere.rikkahub.debug:color/vector_tint_color = 0x7f05035d
me.rerere.rikkahub.debug:color/material_grey_850 = 0x7f05029d
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f12014c
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f12014b
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602c1
me.rerere.rikkahub.debug:attr/colorOnBackground = 0x7f0300e9
me.rerere.rikkahub.debug:color/ucrop_scale_text_view_selector = 0x7f05035c
me.rerere.rikkahub.debug:string/navigation_menu = 0x7f1101a8
me.rerere.rikkahub.debug:color/ucrop_color_widget_background = 0x7f050358
me.rerere.rikkahub.debug:string/m3c_bottom_sheet_dismiss_description = 0x7f110102
me.rerere.rikkahub.debug:id/home = 0x7f0800cc
me.rerere.rikkahub.debug:attr/textAppearanceBodyMedium = 0x7f030415
me.rerere.rikkahub.debug:color/ucrop_color_widget = 0x7f050356
me.rerere.rikkahub.debug:id/action_bar_container = 0x7f080032
me.rerere.rikkahub.debug:drawable/mtrl_switch_track = 0x7f0700f5
me.rerere.rikkahub.debug:color/ucrop_color_white = 0x7f050355
me.rerere.rikkahub.debug:styleable/CircularProgressIndicator = 0x7f130020
me.rerere.rikkahub.debug:color/ucrop_color_progress_wheel_line = 0x7f050351
me.rerere.rikkahub.debug:string/photo = 0x7f1101b0
me.rerere.rikkahub.debug:dimen/abc_action_bar_elevation_material = 0x7f060005
me.rerere.rikkahub.debug:id/material_clock_display = 0x7f08012a
me.rerere.rikkahub.debug:color/ucrop_color_default_logo = 0x7f05034c
me.rerere.rikkahub.debug:string/assistant_page_memory_count = 0x7f110039
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1203f5
me.rerere.rikkahub.debug:id/textinput_placeholder = 0x7f08020b
me.rerere.rikkahub.debug:attr/ucrop_show_oval_crop_frame = 0x7f0304a3
me.rerere.rikkahub.debug:color/ucrop_color_crop_background = 0x7f050348
me.rerere.rikkahub.debug:dimen/cardview_default_elevation = 0x7f060053
me.rerere.rikkahub.debug:string/character_counter_overflowed_content_description = 0x7f110064
me.rerere.rikkahub.debug:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301d1
me.rerere.rikkahub.debug:color/ucrop_color_blaze_orange = 0x7f050347
me.rerere.rikkahub.debug:color/tooltip_background_dark = 0x7f050342
me.rerere.rikkahub.debug:color/switch_thumb_disabled_material_light = 0x7f05033b
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00ad
me.rerere.rikkahub.debug:color/secondary_text_disabled_material_dark = 0x7f050338
me.rerere.rikkahub.debug:attr/waveDecay = 0x7f0304af
me.rerere.rikkahub.debug:attr/state_error = 0x7f0303d1
me.rerere.rikkahub.debug:color/quickie_transparent = 0x7f050332
me.rerere.rikkahub.debug:color/quickie_gray = 0x7f050331
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0074
me.rerere.rikkahub.debug:dimen/mtrl_tooltip_cornerSize = 0x7f060317
me.rerere.rikkahub.debug:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c0149
me.rerere.rikkahub.debug:color/primary_text_disabled_material_dark = 0x7f05032b
me.rerere.rikkahub.debug:color/primary_text_default_material_dark = 0x7f050329
me.rerere.rikkahub.debug:attr/motionEasingLinear = 0x7f030322
me.rerere.rikkahub.debug:attr/marginLeftSystemWindowInsets = 0x7f0302bf
me.rerere.rikkahub.debug:color/primary_material_dark = 0x7f050327
me.rerere.rikkahub.debug:attr/floatingActionButtonSecondaryStyle = 0x7f0301d0
me.rerere.rikkahub.debug:color/primary_dark_material_light = 0x7f050326
me.rerere.rikkahub.debug:dimen/mtrl_switch_track_width = 0x7f06030b
me.rerere.rikkahub.debug:color/mtrl_tabs_icon_color_selector = 0x7f050319
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_heap_dumps_icon = 0x7f080107
me.rerere.rikkahub.debug:dimen/mtrl_btn_text_btn_padding_left = 0x7f06027d
me.rerere.rikkahub.debug:layout/ime_base_split_test_activity = 0x7f0b002c
me.rerere.rikkahub.debug:color/primary_dark_material_dark = 0x7f050325
me.rerere.rikkahub.debug:color/material_dynamic_neutral0 = 0x7f050257
me.rerere.rikkahub.debug:color/notification_action_color_filter = 0x7f050323
me.rerere.rikkahub.debug:color/mtrl_tabs_icon_color_selector_colored = 0x7f05031a
me.rerere.rikkahub.debug:color/m3_ref_palette_error70 = 0x7f050132
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_dark_focused = 0x7f070083
me.rerere.rikkahub.debug:string/searchview_clear_text_content_description = 0x7f1101b9
me.rerere.rikkahub.debug:drawable/m3_tabs_transparent_background = 0x7f0700c8
me.rerere.rikkahub.debug:color/mtrl_tabs_colored_ripple_color = 0x7f050318
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary99 = 0x7f050182
me.rerere.rikkahub.debug:color/mtrl_switch_track_decoration_tint = 0x7f050316
me.rerere.rikkahub.debug:id/action_bar_spinner = 0x7f080034
me.rerere.rikkahub.debug:drawable/notification_bg_normal_pressed = 0x7f0700ff
me.rerere.rikkahub.debug:attr/actionModeFindDrawable = 0x7f030017
me.rerere.rikkahub.debug:color/switch_thumb_normal_material_dark = 0x7f05033e
me.rerere.rikkahub.debug:color/mtrl_popupmenu_overlay_color = 0x7f050312
me.rerere.rikkahub.debug:attr/background = 0x7f030045
me.rerere.rikkahub.debug:id/search_badge = 0x7f0801b2
me.rerere.rikkahub.debug:color/mtrl_outlined_stroke_color = 0x7f050311
me.rerere.rikkahub.debug:dimen/m3_divider_heavy_thickness = 0x7f0601be
me.rerere.rikkahub.debug:color/mtrl_outlined_icon_tint = 0x7f050310
me.rerere.rikkahub.debug:string/side_sheet_accessibility_pane_title = 0x7f1101eb
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0088
me.rerere.rikkahub.debug:id/stop = 0x7f0801e3
me.rerere.rikkahub.debug:color/mtrl_on_surface_ripple_color = 0x7f05030f
me.rerere.rikkahub.debug:id/action_menu_presenter = 0x7f08003c
me.rerere.rikkahub.debug:xml/_generated_res_locale_config = 0x7f140000
me.rerere.rikkahub.debug:color/mtrl_navigation_item_icon_tint = 0x7f05030c
me.rerere.rikkahub.debug:string/m3c_date_picker_navigate_to_year_description = 0x7f110110
me.rerere.rikkahub.debug:drawable/m3_radiobutton_ripple = 0x7f0700c3
me.rerere.rikkahub.debug:color/mtrl_navigation_bar_colored_ripple_color = 0x7f050308
me.rerere.rikkahub.debug:dimen/leak_canary_toast_icon_tv_padding = 0x7f0600ac
me.rerere.rikkahub.debug:styleable/OnSwipe = 0x7f130074
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_light = 0x7f070090
me.rerere.rikkahub.debug:dimen/leak_canary_connector_width = 0x7f0600a1
me.rerere.rikkahub.debug:color/mtrl_btn_stroke_color_selector = 0x7f0502ee
me.rerere.rikkahub.debug:attr/behavior_skipCollapsed = 0x7f030070
me.rerere.rikkahub.debug:color/mtrl_filled_stroke_color = 0x7f050305
me.rerere.rikkahub.debug:drawable/design_ic_visibility = 0x7f070095
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602b7
me.rerere.rikkahub.debug:color/mtrl_filled_background_color = 0x7f050303
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_disabled_running_tests = 0x7f1100c7
me.rerere.rikkahub.debug:attr/floatingActionButtonLargeStyle = 0x7f0301cc
me.rerere.rikkahub.debug:color/mtrl_fab_ripple_color = 0x7f050302
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_dim = 0x7f0501ad
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant40 = 0x7f050269
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_secondary_container = 0x7f05020d
me.rerere.rikkahub.debug:color/mtrl_fab_icon_text_color_selector = 0x7f050301
me.rerere.rikkahub.debug:attr/cornerFamilyBottomRight = 0x7f03013a
me.rerere.rikkahub.debug:drawable/ic_call_answer = 0x7f07009c
me.rerere.rikkahub.debug:attr/uri = 0x7f0304a4
me.rerere.rikkahub.debug:color/mtrl_choice_chip_text_color = 0x7f0502fe
me.rerere.rikkahub.debug:dimen/m3_timepicker_window_elevation = 0x7f06022f
me.rerere.rikkahub.debug:color/mtrl_choice_chip_ripple_color = 0x7f0502fd
me.rerere.rikkahub.debug:color/mtrl_chip_text_color = 0x7f0502fb
me.rerere.rikkahub.debug:attr/thumbHeight = 0x7f030448
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500f6
me.rerere.rikkahub.debug:color/mtrl_chip_surface_color = 0x7f0502fa
me.rerere.rikkahub.debug:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
me.rerere.rikkahub.debug:color/mtrl_chip_background_color = 0x7f0502f8
me.rerere.rikkahub.debug:layout/material_clockface_view = 0x7f0b0048
me.rerere.rikkahub.debug:color/mtrl_card_view_foreground = 0x7f0502f6
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1201de
me.rerere.rikkahub.debug:color/material_slider_thumb_color = 0x7f0502e6
me.rerere.rikkahub.debug:color/mtrl_calendar_selected_range = 0x7f0502f5
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500e4
me.rerere.rikkahub.debug:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302c3
me.rerere.rikkahub.debug:color/material_on_primary_emphasis_medium = 0x7f0502a8
me.rerere.rikkahub.debug:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0502ef
me.rerere.rikkahub.debug:drawable/ucrop_ic_brightness = 0x7f070112
me.rerere.rikkahub.debug:color/mtrl_btn_bg_color_selector = 0x7f0502ec
me.rerere.rikkahub.debug:dimen/mtrl_btn_stroke_size = 0x7f06027b
me.rerere.rikkahub.debug:color/material_timepicker_modebutton_tint = 0x7f0502eb
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f120174
me.rerere.rikkahub.debug:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f0601a5
me.rerere.rikkahub.debug:attr/passwordToggleEnabled = 0x7f030356
me.rerere.rikkahub.debug:color/material_timepicker_clockface = 0x7f0502ea
me.rerere.rikkahub.debug:id/hideable = 0x7f0800cb
me.rerere.rikkahub.debug:color/material_timepicker_button_stroke = 0x7f0502e8
me.rerere.rikkahub.debug:attr/contentPadding = 0x7f03012c
me.rerere.rikkahub.debug:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06026a
me.rerere.rikkahub.debug:string/common_google_play_services_unknown_issue = 0x7f11008d
me.rerere.rikkahub.debug:color/material_slider_inactive_tick_marks_color = 0x7f0502e4
me.rerere.rikkahub.debug:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502d8
me.rerere.rikkahub.debug:color/material_slider_active_tick_marks_color = 0x7f0502e1
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06014d
me.rerere.rikkahub.debug:id/leak_canary_header_text = 0x7f0800fc
me.rerere.rikkahub.debug:color/material_personalized_primary_text_disable_only = 0x7f0502e0
me.rerere.rikkahub.debug:attr/fontProviderPackage = 0x7f0301f3
me.rerere.rikkahub.debug:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f0601a4
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f120427
me.rerere.rikkahub.debug:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502df
me.rerere.rikkahub.debug:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502db
me.rerere.rikkahub.debug:color/material_personalized_color_text_primary_inverse = 0x7f0502d9
me.rerere.rikkahub.debug:color/material_personalized_color_surface_variant = 0x7f0502d5
me.rerere.rikkahub.debug:color/mtrl_btn_text_color_disabled = 0x7f0502f1
me.rerere.rikkahub.debug:color/material_personalized_color_surface_container_low = 0x7f0502d1
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_secondary = 0x7f0501c6
me.rerere.rikkahub.debug:dimen/mtrl_high_ripple_default_alpha = 0x7f0602ca
me.rerere.rikkahub.debug:color/m3_ref_palette_primary30 = 0x7f050160
me.rerere.rikkahub.debug:color/material_personalized_color_surface_container_highest = 0x7f0502d0
me.rerere.rikkahub.debug:color/material_personalized_color_surface_bright = 0x7f0502cd
me.rerere.rikkahub.debug:color/material_personalized_color_secondary_text = 0x7f0502ca
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060264
me.rerere.rikkahub.debug:color/material_personalized_color_secondary = 0x7f0502c8
me.rerere.rikkahub.debug:color/material_personalized_color_primary = 0x7f0502c3
me.rerere.rikkahub.debug:style/CardView.Dark = 0x7f120120
me.rerere.rikkahub.debug:color/material_personalized_color_outline_variant = 0x7f0502c2
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Button = 0x7f120019
me.rerere.rikkahub.debug:string/abc_menu_alt_shortcut_label = 0x7f110008
me.rerere.rikkahub.debug:color/material_personalized_color_on_tertiary = 0x7f0502bf
me.rerere.rikkahub.debug:string/mermaid_export_failed = 0x7f110162
me.rerere.rikkahub.debug:attr/startIconCheckable = 0x7f0303c6
me.rerere.rikkahub.debug:color/material_dynamic_color_dark_on_error = 0x7f050251
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f07001f
me.rerere.rikkahub.debug:dimen/notification_media_narrow_margin = 0x7f060323
me.rerere.rikkahub.debug:color/material_personalized_color_on_secondary = 0x7f0502ba
me.rerere.rikkahub.debug:color/material_personalized_color_on_error_container = 0x7f0502b7
me.rerere.rikkahub.debug:attr/navigationRailStyle = 0x7f030337
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.SmallComponent = 0x7f12017e
me.rerere.rikkahub.debug:color/material_personalized_color_control_normal = 0x7f0502b2
me.rerere.rikkahub.debug:id/open_search_bar_text_view = 0x7f080174
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f12007a
me.rerere.rikkahub.debug:color/material_personalized_color_background = 0x7f0502af
me.rerere.rikkahub.debug:styleable/NavigationRailView = 0x7f130070
me.rerere.rikkahub.debug:color/material_personalized__highlighted_text = 0x7f0502ad
me.rerere.rikkahub.debug:color/ucrop_color_widget_rotate_mid_line = 0x7f05035a
me.rerere.rikkahub.debug:color/material_on_surface_emphasis_high_type = 0x7f0502aa
me.rerere.rikkahub.debug:color/material_on_background_emphasis_high_type = 0x7f0502a4
me.rerere.rikkahub.debug:color/material_on_background_disabled = 0x7f0502a3
me.rerere.rikkahub.debug:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_date_range_end_hint = 0x7f110194
me.rerere.rikkahub.debug:attr/data = 0x7f030158
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500fb
me.rerere.rikkahub.debug:color/material_harmonized_color_error_container = 0x7f0502a0
me.rerere.rikkahub.debug:id/unchecked = 0x7f080228
me.rerere.rikkahub.debug:color/material_harmonized_color_error = 0x7f05029f
me.rerere.rikkahub.debug:color/material_grey_800 = 0x7f05029c
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f12018d
me.rerere.rikkahub.debug:dimen/mtrl_btn_padding_top = 0x7f060278
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601b1
me.rerere.rikkahub.debug:id/fillCenter = 0x7f0800af
me.rerere.rikkahub.debug:attr/fastScrollEnabled = 0x7f0301c4
me.rerere.rikkahub.debug:color/material_grey_50 = 0x7f05029a
me.rerere.rikkahub.debug:dimen/m3_carousel_small_item_default_corner_size = 0x7f060100
me.rerere.rikkahub.debug:color/material_personalized_color_secondary_text_inverse = 0x7f0502cb
me.rerere.rikkahub.debug:macro/m3_comp_text_button_label_text_color = 0x7f0c0144
me.rerere.rikkahub.debug:attr/animate_relativeTo = 0x7f030032
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ProgressIndicator = 0x7f120447
me.rerere.rikkahub.debug:color/material_grey_100 = 0x7f050298
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070025
me.rerere.rikkahub.debug:color/material_dynamic_tertiary99 = 0x7f050297
me.rerere.rikkahub.debug:color/material_dynamic_tertiary90 = 0x7f050295
me.rerere.rikkahub.debug:style/Widget.Material3.NavigationRailView.Badge = 0x7f1203c3
me.rerere.rikkahub.debug:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010c
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface = 0x7f0501a6
me.rerere.rikkahub.debug:drawable/abc_textfield_search_material = 0x7f070075
me.rerere.rikkahub.debug:dimen/abc_text_size_display_3_material = 0x7f060045
me.rerere.rikkahub.debug:color/material_dynamic_tertiary80 = 0x7f050294
me.rerere.rikkahub.debug:id/contiguous = 0x7f080078
me.rerere.rikkahub.debug:color/material_dynamic_tertiary70 = 0x7f050293
me.rerere.rikkahub.debug:attr/listPreferredItemPaddingStart = 0x7f0302b9
me.rerere.rikkahub.debug:color/ucrop_color_heather = 0x7f05034e
me.rerere.rikkahub.debug:color/material_dynamic_tertiary50 = 0x7f050291
me.rerere.rikkahub.debug:color/mtrl_switch_thumb_tint = 0x7f050315
me.rerere.rikkahub.debug:color/material_dynamic_secondary95 = 0x7f050289
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00f9
me.rerere.rikkahub.debug:id/search_plate = 0x7f0801b9
me.rerere.rikkahub.debug:dimen/m3_fab_corner_size = 0x7f0601c6
me.rerere.rikkahub.debug:color/material_dynamic_secondary50 = 0x7f050284
me.rerere.rikkahub.debug:color/m3_sys_color_light_outline_variant = 0x7f050213
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f12037e
me.rerere.rikkahub.debug:color/material_dynamic_secondary100 = 0x7f050280
me.rerere.rikkahub.debug:string/menu_page_afternoon_greeting = 0x7f11015a
me.rerere.rikkahub.debug:dimen/m3_extended_fab_top_padding = 0x7f0601c4
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f12029d
me.rerere.rikkahub.debug:color/mtrl_navigation_bar_ripple_color = 0x7f05030a
me.rerere.rikkahub.debug:color/material_personalized_color_outline = 0x7f0502c1
me.rerere.rikkahub.debug:id/autoCompleteToStart = 0x7f080053
me.rerere.rikkahub.debug:id/open_search_view_header_container = 0x7f08017b
me.rerere.rikkahub.debug:color/material_dynamic_primary90 = 0x7f05027b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CardView = 0x7f12040f
me.rerere.rikkahub.debug:color/material_dynamic_primary80 = 0x7f05027a
me.rerere.rikkahub.debug:style/TextAppearance.Material3.DisplayLarge = 0x7f1201ed
me.rerere.rikkahub.debug:color/material_dynamic_primary60 = 0x7f050278
me.rerere.rikkahub.debug:color/material_dynamic_primary50 = 0x7f050277
me.rerere.rikkahub.debug:string/setting_page_model_and_services = 0x7f1101de
me.rerere.rikkahub.debug:color/material_dynamic_primary40 = 0x7f050276
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060121
me.rerere.rikkahub.debug:color/material_dynamic_primary30 = 0x7f050275
me.rerere.rikkahub.debug:color/material_dynamic_primary10 = 0x7f050272
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600b3
me.rerere.rikkahub.debug:attr/suffixText = 0x7f0303e6
me.rerere.rikkahub.debug:color/material_dynamic_primary0 = 0x7f050271
me.rerere.rikkahub.debug:dimen/design_snackbar_min_width = 0x7f060084
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant90 = 0x7f05026e
me.rerere.rikkahub.debug:dimen/mtrl_card_spacing = 0x7f0602b0
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant50 = 0x7f05026a
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant30 = 0x7f050268
me.rerere.rikkahub.debug:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060259
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant20 = 0x7f050267
me.rerere.rikkahub.debug:styleable/GradientColor = 0x7f13003a
me.rerere.rikkahub.debug:dimen/ucrop_margin_top_controls_text = 0x7f060340
me.rerere.rikkahub.debug:color/material_dynamic_neutral95 = 0x7f050262
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1202a3
me.rerere.rikkahub.debug:color/material_dynamic_neutral70 = 0x7f05025f
me.rerere.rikkahub.debug:drawable/ic_call_answer_low = 0x7f07009d
me.rerere.rikkahub.debug:attr/windowFixedWidthMinor = 0x7f0304ba
me.rerere.rikkahub.debug:color/material_dynamic_neutral60 = 0x7f05025e
me.rerere.rikkahub.debug:color/material_dynamic_neutral50 = 0x7f05025d
me.rerere.rikkahub.debug:color/material_dynamic_neutral20 = 0x7f05025a
me.rerere.rikkahub.debug:dimen/fastscroll_default_thickness = 0x7f060090
me.rerere.rikkahub.debug:color/leak_canary_gray_darkest_25p = 0x7f050077
me.rerere.rikkahub.debug:attr/layout_constraintBottom_creator = 0x7f03026e
me.rerere.rikkahub.debug:color/material_dynamic_neutral100 = 0x7f050259
me.rerere.rikkahub.debug:dimen/mtrl_calendar_year_corner = 0x7f0602a6
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_text_size = 0x7f06006a
me.rerere.rikkahub.debug:color/material_dynamic_color_light_on_error = 0x7f050255
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006a
me.rerere.rikkahub.debug:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
me.rerere.rikkahub.debug:color/material_dynamic_color_dark_on_error_container = 0x7f050252
me.rerere.rikkahub.debug:attr/customColorDrawableValue = 0x7f030150
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060167
me.rerere.rikkahub.debug:drawable/abc_text_cursor_material = 0x7f07006d
me.rerere.rikkahub.debug:color/material_dynamic_color_dark_error = 0x7f05024f
me.rerere.rikkahub.debug:drawable/design_password_eye = 0x7f070097
me.rerere.rikkahub.debug:color/material_deep_teal_500 = 0x7f05024d
me.rerere.rikkahub.debug:color/material_dynamic_color_dark_error_container = 0x7f050250
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level4 = 0x7f060204
me.rerere.rikkahub.debug:color/material_blue_grey_900 = 0x7f050249
me.rerere.rikkahub.debug:color/material_dynamic_primary20 = 0x7f050274
me.rerere.rikkahub.debug:color/m3_tonal_button_ripple_color_selector = 0x7f050247
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00ba
me.rerere.rikkahub.debug:color/m3_timepicker_time_input_stroke_color = 0x7f050246
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c0139
me.rerere.rikkahub.debug:color/material_cursor_color = 0x7f05024b
me.rerere.rikkahub.debug:attr/toggleCheckedStateOnClick = 0x7f030470
me.rerere.rikkahub.debug:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050244
me.rerere.rikkahub.debug:dimen/ucrop_margin_top_widget_text = 0x7f060341
me.rerere.rikkahub.debug:attr/titleMarginTop = 0x7f030469
me.rerere.rikkahub.debug:attr/numericModifiers = 0x7f03033e
me.rerere.rikkahub.debug:color/mtrl_chip_close_icon_tint = 0x7f0502f9
me.rerere.rikkahub.debug:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
me.rerere.rikkahub.debug:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06025c
me.rerere.rikkahub.debug:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011b
me.rerere.rikkahub.debug:color/m3_timepicker_display_ripple_color = 0x7f050242
me.rerere.rikkahub.debug:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f120142
me.rerere.rikkahub.debug:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ca
me.rerere.rikkahub.debug:color/m3_timepicker_display_background_color = 0x7f050241
me.rerere.rikkahub.debug:color/m3_timepicker_button_text_color = 0x7f05023f
me.rerere.rikkahub.debug:drawable/abc_cab_background_internal_bg = 0x7f070037
me.rerere.rikkahub.debug:color/material_dynamic_tertiary95 = 0x7f050296
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008b
me.rerere.rikkahub.debug:attr/closeItemLayout = 0x7f0300d3
me.rerere.rikkahub.debug:color/m3_timepicker_button_background_color = 0x7f05023d
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface = 0x7f0501ea
me.rerere.rikkahub.debug:color/m3_textfield_label_color = 0x7f05023b
me.rerere.rikkahub.debug:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601cd
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_icon_size = 0x7f06014c
me.rerere.rikkahub.debug:color/m3_textfield_indicator_text_color = 0x7f050239
me.rerere.rikkahub.debug:id/left = 0x7f08011d
me.rerere.rikkahub.debug:color/m3_text_button_ripple_color_selector = 0x7f050237
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_surface_variant = 0x7f05020f
me.rerere.rikkahub.debug:color/m3_text_button_background_color_selector = 0x7f050235
me.rerere.rikkahub.debug:layout/design_navigation_menu = 0x7f0b0028
me.rerere.rikkahub.debug:dimen/m3_card_elevation = 0x7f0600fa
me.rerere.rikkahub.debug:color/m3_sys_color_tertiary_fixed = 0x7f05022d
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary30 = 0x7f05017a
me.rerere.rikkahub.debug:color/m3_sys_color_secondary_fixed_dim = 0x7f05022c
me.rerere.rikkahub.debug:color/m3_sys_color_secondary_fixed = 0x7f05022b
me.rerere.rikkahub.debug:color/m3_sys_color_primary_fixed = 0x7f050229
me.rerere.rikkahub.debug:id/tag_transition_group = 0x7f0801f3
me.rerere.rikkahub.debug:dimen/m3_comp_switch_track_height = 0x7f0601a7
me.rerere.rikkahub.debug:attr/layout_collapseParallaxMultiplier = 0x7f030269
me.rerere.rikkahub.debug:color/m3_sys_color_on_tertiary_fixed = 0x7f050227
me.rerere.rikkahub.debug:color/m3_sys_color_on_secondary_fixed_variant = 0x7f050226
me.rerere.rikkahub.debug:color/m3_sys_color_on_primary_fixed = 0x7f050223
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Chip = 0x7f120298
me.rerere.rikkahub.debug:color/m3_sys_color_light_tertiary = 0x7f050221
me.rerere.rikkahub.debug:dimen/abc_config_prefDialogWidth = 0x7f060017
me.rerere.rikkahub.debug:color/ucrop_color_default_crop_frame = 0x7f050349
me.rerere.rikkahub.debug:style/Base.V7.Widget.AppCompat.EditText = 0x7f1200c0
me.rerere.rikkahub.debug:string/exposed_dropdown_menu_content_description = 0x7f1100a0
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_variant = 0x7f050220
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0034
me.rerere.rikkahub.debug:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060131
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_container_lowest = 0x7f05021e
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_container_highest = 0x7f05021c
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_container_high = 0x7f05021b
me.rerere.rikkahub.debug:style/Base.Widget.Material3.TabLayout = 0x7f12010e
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_container = 0x7f05021a
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface = 0x7f050218
me.rerere.rikkahub.debug:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f120381
me.rerere.rikkahub.debug:color/m3_sys_color_light_secondary_container = 0x7f050217
me.rerere.rikkahub.debug:string/leak_canary_notification_no_retained_object_title = 0x7f1100db
me.rerere.rikkahub.debug:color/m3_sys_color_light_primary_container = 0x7f050215
me.rerere.rikkahub.debug:string/setting_page_display_setting = 0x7f1101d9
me.rerere.rikkahub.debug:string/assistant_page_name = 0x7f11003b
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500db
me.rerere.rikkahub.debug:color/m3_sys_color_light_primary = 0x7f050214
me.rerere.rikkahub.debug:macro/m3_comp_fab_primary_container_shape = 0x7f0c0037
me.rerere.rikkahub.debug:dimen/mtrl_switch_track_height = 0x7f06030a
me.rerere.rikkahub.debug:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004a
me.rerere.rikkahub.debug:id/dragDown = 0x7f080096
me.rerere.rikkahub.debug:color/m3_sys_color_light_outline = 0x7f050212
me.rerere.rikkahub.debug:attr/ttcIndex = 0x7f030493
me.rerere.rikkahub.debug:attr/brightness = 0x7f030084
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_tertiary_container = 0x7f050211
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f12004e
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_primary_container = 0x7f05020b
me.rerere.rikkahub.debug:attr/mock_diagonalsColor = 0x7f030305
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_primary = 0x7f05020a
me.rerere.rikkahub.debug:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070061
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_full_corner_family = 0x7f090023
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_error = 0x7f050208
me.rerere.rikkahub.debug:color/m3_sys_color_light_background = 0x7f050201
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f050200
me.rerere.rikkahub.debug:layout/mtrl_calendar_month = 0x7f0b005b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501fd
me.rerere.rikkahub.debug:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501fb
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060212
me.rerere.rikkahub.debug:style/TextAppearance.Material3.SearchBar = 0x7f1201f7
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1201b8
me.rerere.rikkahub.debug:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f12009d
me.rerere.rikkahub.debug:dimen/ucrop_height_divider_shadow = 0x7f06033b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501f9
me.rerere.rikkahub.debug:attr/materialCalendarYearNavigationButton = 0x7f0302d9
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501f7
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501f5
me.rerere.rikkahub.debug:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f120109
me.rerere.rikkahub.debug:id/fitStart = 0x7f0800b7
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501f4
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501f3
me.rerere.rikkahub.debug:style/TextAppearance.Design.Suffix = 0x7f1201d7
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501f2
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501ee
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Subhead = 0x7f12002d
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060177
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0010
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501ec
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.Large = 0x7f120177
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1200e9
me.rerere.rikkahub.debug:anim/m3_bottom_sheet_slide_out = 0x7f010028
me.rerere.rikkahub.debug:attr/flow_verticalAlign = 0x7f0301e7
me.rerere.rikkahub.debug:drawable/abc_spinner_mtrl_am_alpha = 0x7f070065
me.rerere.rikkahub.debug:id/mtrl_picker_text_input_range_end = 0x7f08015e
me.rerere.rikkahub.debug:attr/flow_horizontalGap = 0x7f0301df
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501eb
me.rerere.rikkahub.debug:attr/flow_firstHorizontalStyle = 0x7f0301da
me.rerere.rikkahub.debug:attr/materialCircleRadius = 0x7f0302de
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_primary = 0x7f0501e6
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501e5
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_outline = 0x7f0501e4
me.rerere.rikkahub.debug:dimen/material_emphasis_medium = 0x7f060246
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Year = 0x7f1203b2
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501e2
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ShapeableImageView = 0x7f120448
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002e
me.rerere.rikkahub.debug:attr/layout_constraintVertical_chainStyle = 0x7f03028e
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501e1
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_path_name = 0x7f110171
me.rerere.rikkahub.debug:layout/leak_canary_about_screen = 0x7f0b002e
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501e0
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1202ce
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501df
me.rerere.rikkahub.debug:id/image_view_logo = 0x7f0800d7
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501de
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1203f9
me.rerere.rikkahub.debug:anim/ucrop_loader_circle_scale = 0x7f010033
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501db
me.rerere.rikkahub.debug:drawable/ic_call_decline_low = 0x7f0700a1
me.rerere.rikkahub.debug:color/material_dynamic_secondary30 = 0x7f050282
me.rerere.rikkahub.debug:color/m3_sys_color_light_secondary = 0x7f050216
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_background = 0x7f0501d9
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501d6
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1203a2
me.rerere.rikkahub.debug:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f06010e
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_item_min_width = 0x7f060066
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_error = 0x7f0501d4
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_background = 0x7f0501d3
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat = 0x7f12027b
me.rerere.rikkahub.debug:string/leak_canary_failure_copied = 0x7f1100bf
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0501d2
me.rerere.rikkahub.debug:string/assistant_page_header_name = 0x7f110031
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0501cf
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_min_width = 0x7f0602bf
me.rerere.rikkahub.debug:style/Widget.AppCompat.ListPopupWindow = 0x7f12031e
me.rerere.rikkahub.debug:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300dd
me.rerere.rikkahub.debug:dimen/abc_text_size_display_4_material = 0x7f060046
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0501cd
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0501cc
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0141
me.rerere.rikkahub.debug:layout/ucrop_layout_brightness_wheel = 0x7f0b0080
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0501cb
me.rerere.rikkahub.debug:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060182
me.rerere.rikkahub.debug:color/mtrl_navigation_bar_item_tint = 0x7f050309
me.rerere.rikkahub.debug:dimen/hint_alpha_material_light = 0x7f060097
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0501ca
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ab
me.rerere.rikkahub.debug:attr/pathMotionArc = 0x7f030359
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0501c9
me.rerere.rikkahub.debug:id/TOP_END = 0x7f08000c
me.rerere.rikkahub.debug:attr/collapsingToolbarLayoutLargeSize = 0x7f0300da
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_top_padding = 0x7f0602c2
me.rerere.rikkahub.debug:string/common_signin_button_text = 0x7f110095
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f0501bf
me.rerere.rikkahub.debug:color/abc_search_url_text_pressed = 0x7f05000f
me.rerere.rikkahub.debug:dimen/ucrop_text_size_controls_text = 0x7f060346
me.rerere.rikkahub.debug:dimen/material_time_picker_minimum_screen_height = 0x7f060254
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f0501bb
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f120169
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_primary = 0x7f0501ba
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f0501b9
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_error = 0x7f0501b8
me.rerere.rikkahub.debug:styleable/CoordinatorLayout = 0x7f13002b
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level1 = 0x7f060201
me.rerere.rikkahub.debug:layout/m3_alert_dialog_title = 0x7f0b003f
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_background = 0x7f0501b7
me.rerere.rikkahub.debug:attr/buttonIconDimen = 0x7f03008d
me.rerere.rikkahub.debug:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005d
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f0501b6
me.rerere.rikkahub.debug:attr/borderWidth = 0x7f030071
me.rerere.rikkahub.debug:attr/checkedChip = 0x7f0300a6
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f0501b5
me.rerere.rikkahub.debug:string/menu_page_morning_greeting = 0x7f11015f
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_error_container = 0x7f0501b3
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.Icon = 0x7f120404
me.rerere.rikkahub.debug:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06013e
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_error = 0x7f0501b2
me.rerere.rikkahub.debug:styleable/TabItem = 0x7f130090
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_background = 0x7f0501b1
me.rerere.rikkahub.debug:color/m3_sys_color_dark_tertiary = 0x7f0501af
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_variant = 0x7f0501ae
me.rerere.rikkahub.debug:dimen/m3_btn_inset = 0x7f0600e7
me.rerere.rikkahub.debug:style/TextAppearance.Design.Hint = 0x7f1201d3
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_container_low = 0x7f0501ab
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f12008f
me.rerere.rikkahub.debug:attr/badgeHeight = 0x7f030051
me.rerere.rikkahub.debug:drawable/ic_search_black_24 = 0x7f0700ac
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_container_high = 0x7f0501a9
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar = 0x7f120424
me.rerere.rikkahub.debug:layout/ucrop_layout_rotate_wheel = 0x7f0b0082
me.rerere.rikkahub.debug:dimen/m3_btn_padding_bottom = 0x7f0600e9
me.rerere.rikkahub.debug:string/mtrl_timepicker_confirm = 0x7f1101a7
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary50 = 0x7f050116
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_container = 0x7f0501a8
me.rerere.rikkahub.debug:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
me.rerere.rikkahub.debug:attr/endIconTint = 0x7f030197
me.rerere.rikkahub.debug:color/purple_500 = 0x7f05032e
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_bright = 0x7f0501a7
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1202d3
me.rerere.rikkahub.debug:layout/material_clock_period_toggle = 0x7f0b0045
me.rerere.rikkahub.debug:color/m3_sys_color_dark_secondary_container = 0x7f0501a5
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1203a5
me.rerere.rikkahub.debug:attr/textAppearanceHeadline5 = 0x7f030420
me.rerere.rikkahub.debug:color/m3_sys_color_dark_primary = 0x7f0501a2
me.rerere.rikkahub.debug:style/ucrop_TextViewWidget = 0x7f120472
me.rerere.rikkahub.debug:color/design_fab_stroke_end_outer_color = 0x7f05005c
me.rerere.rikkahub.debug:color/m3_sys_color_dark_outline_variant = 0x7f0501a1
me.rerere.rikkahub.debug:attr/dropdownListPreferredItemHeight = 0x7f030185
me.rerere.rikkahub.debug:color/m3_sys_color_dark_outline = 0x7f0501a0
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_tertiary_container = 0x7f05019f
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501ef
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_surface_variant = 0x7f05019d
me.rerere.rikkahub.debug:mipmap/ic_launcher_monochrome = 0x7f0e0003
me.rerere.rikkahub.debug:layout/material_time_chip = 0x7f0b004b
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_surface = 0x7f05019c
me.rerere.rikkahub.debug:drawable/ucrop_crop = 0x7f070110
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_secondary_container = 0x7f05019b
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_primary = 0x7f050198
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_error = 0x7f050196
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_background = 0x7f050195
me.rerere.rikkahub.debug:string/abc_searchview_description_voice = 0x7f110017
me.rerere.rikkahub.debug:id/center = 0x7f08005e
me.rerere.rikkahub.debug:color/m3_sys_color_dark_error = 0x7f050190
me.rerere.rikkahub.debug:string/mtrl_picker_text_input_year_abbr = 0x7f110198
me.rerere.rikkahub.debug:attr/navigationMode = 0x7f030336
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602b8
me.rerere.rikkahub.debug:color/m3_switch_track_tint = 0x7f05018e
me.rerere.rikkahub.debug:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060267
me.rerere.rikkahub.debug:color/m3_switch_thumb_tint = 0x7f05018d
me.rerere.rikkahub.debug:string/abc_menu_function_shortcut_label = 0x7f11000c
me.rerere.rikkahub.debug:attr/cursorColor = 0x7f03014c
me.rerere.rikkahub.debug:drawable/$m3_avd_show_password__2 = 0x7f07000b
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500d4
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0501c7
me.rerere.rikkahub.debug:color/m3_slider_thumb_color = 0x7f05018b
me.rerere.rikkahub.debug:drawable/abc_list_selector_disabled_holo_light = 0x7f070055
me.rerere.rikkahub.debug:color/m3_slider_active_track_color = 0x7f050186
me.rerere.rikkahub.debug:color/m3_sys_color_on_secondary_fixed = 0x7f050225
me.rerere.rikkahub.debug:color/m3_simple_item_ripple_color = 0x7f050185
me.rerere.rikkahub.debug:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302ac
me.rerere.rikkahub.debug:color/m3_selection_control_ripple_color_selector = 0x7f050184
me.rerere.rikkahub.debug:id/tabMode = 0x7f0801e7
me.rerere.rikkahub.debug:color/m3_ref_palette_white = 0x7f050183
me.rerere.rikkahub.debug:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06031b
me.rerere.rikkahub.debug:layout/mtrl_calendar_days_of_week = 0x7f0b0059
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary95 = 0x7f050181
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary90 = 0x7f050180
me.rerere.rikkahub.debug:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1201e8
me.rerere.rikkahub.debug:dimen/leak_canary_row_title_margin_top = 0x7f0600a7
me.rerere.rikkahub.debug:attr/textAppearanceHeadline6 = 0x7f030421
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501ff
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1203b5
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary70 = 0x7f05017e
me.rerere.rikkahub.debug:string/m3_ref_typeface_brand_regular = 0x7f1100f3
me.rerere.rikkahub.debug:dimen/mtrl_slider_widget_height = 0x7f0602ff
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary60 = 0x7f05017d
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button = 0x7f1200cd
me.rerere.rikkahub.debug:id/brightness_scroll_wheel = 0x7f08005b
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary40 = 0x7f05017b
me.rerere.rikkahub.debug:id/transition_pause_alpha = 0x7f080220
me.rerere.rikkahub.debug:animator/fragment_fade_enter = 0x7f020005
me.rerere.rikkahub.debug:dimen/mtrl_calendar_navigation_top_padding = 0x7f06029d
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary100 = 0x7f050178
me.rerere.rikkahub.debug:attr/motionProgress = 0x7f03032b
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_surface = 0x7f05020e
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button = 0x7f120290
me.rerere.rikkahub.debug:id/toolbar = 0x7f080214
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary99 = 0x7f050175
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary95 = 0x7f050174
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary90 = 0x7f050173
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary70 = 0x7f050171
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary30 = 0x7f05016d
me.rerere.rikkahub.debug:dimen/m3_card_dragged_z = 0x7f0600f5
me.rerere.rikkahub.debug:color/m3_textfield_filled_background_color = 0x7f050238
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary20 = 0x7f05016c
me.rerere.rikkahub.debug:style/TextAppearance.Design.HelperText = 0x7f1201d2
me.rerere.rikkahub.debug:drawable/quickie_ic_qrcode = 0x7f070108
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary10 = 0x7f05016a
me.rerere.rikkahub.debug:style/Platform.MaterialComponents.Light = 0x7f12013f
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary0 = 0x7f050169
me.rerere.rikkahub.debug:string/translator_page_result = 0x7f110200
me.rerere.rikkahub.debug:dimen/ucrop_size_dot_scale_text_view = 0x7f060344
me.rerere.rikkahub.debug:attr/autoSizeMaxTextSize = 0x7f03003e
me.rerere.rikkahub.debug:color/cardview_light_background = 0x7f05002d
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601b6
me.rerere.rikkahub.debug:color/mtrl_tabs_ripple_color = 0x7f05031c
me.rerere.rikkahub.debug:styleable/ScrimInsetsFrameLayout = 0x7f13007e
me.rerere.rikkahub.debug:color/m3_ref_palette_primary99 = 0x7f050168
me.rerere.rikkahub.debug:color/m3_ref_palette_primary95 = 0x7f050167
me.rerere.rikkahub.debug:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1202ff
me.rerere.rikkahub.debug:color/m3_ref_palette_primary90 = 0x7f050166
me.rerere.rikkahub.debug:attr/verticalOffset = 0x7f0304a9
me.rerere.rikkahub.debug:color/m3_ref_palette_primary80 = 0x7f050165
me.rerere.rikkahub.debug:style/Widget.AppCompat.SearchView = 0x7f12032a
me.rerere.rikkahub.debug:color/m3_ref_palette_primary70 = 0x7f050164
me.rerere.rikkahub.debug:color/m3_ref_palette_primary60 = 0x7f050163
me.rerere.rikkahub.debug:styleable/Spinner = 0x7f130089
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_small_container_height = 0x7f06012f
me.rerere.rikkahub.debug:attr/startIconTintMode = 0x7f0303cc
me.rerere.rikkahub.debug:color/primary_text_disabled_material_light = 0x7f05032c
me.rerere.rikkahub.debug:string/mtrl_picker_day_of_week_column_header = 0x7f110183
me.rerere.rikkahub.debug:attr/showTitle = 0x7f0303ad
me.rerere.rikkahub.debug:color/m3_ref_palette_primary50 = 0x7f050162
me.rerere.rikkahub.debug:dimen/mtrl_card_checked_icon_size = 0x7f0602ac
me.rerere.rikkahub.debug:id/material_label = 0x7f080134
me.rerere.rikkahub.debug:drawable/notification_tile_bg = 0x7f070104
me.rerere.rikkahub.debug:attr/colorTertiaryContainer = 0x7f03011a
me.rerere.rikkahub.debug:color/m3_ref_palette_primary100 = 0x7f05015e
me.rerere.rikkahub.debug:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090025
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f060207
me.rerere.rikkahub.debug:color/m3_ref_palette_primary0 = 0x7f05015c
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant90 = 0x7f050159
me.rerere.rikkahub.debug:attr/expandedTitleTextColor = 0x7f0301b2
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant80 = 0x7f050158
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f120034
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant60 = 0x7f050156
me.rerere.rikkahub.debug:id/content = 0x7f080076
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant40 = 0x7f050154
me.rerere.rikkahub.debug:style/Widget.AppCompat.SeekBar = 0x7f12032c
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060220
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant20 = 0x7f050152
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant100 = 0x7f050151
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral99 = 0x7f05014e
me.rerere.rikkahub.debug:drawable/design_snackbar_background = 0x7f070098
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary0 = 0x7f050110
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral95 = 0x7f05014b
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral94 = 0x7f05014a
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral92 = 0x7f050149
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00bf
me.rerere.rikkahub.debug:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060186
me.rerere.rikkahub.debug:id/counterclockwise = 0x7f08007e
me.rerere.rikkahub.debug:color/ucrop_color_black = 0x7f050346
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral90 = 0x7f050148
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral87 = 0x7f050147
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Tooltip = 0x7f12020d
me.rerere.rikkahub.debug:color/m3_dynamic_dark_hint_foreground = 0x7f0500b4
me.rerere.rikkahub.debug:dimen/m3_comp_elevated_card_icon_size = 0x7f06011b
me.rerere.rikkahub.debug:string/m3c_floating_toolbar_expand = 0x7f11012a
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0168
me.rerere.rikkahub.debug:color/m3_slider_halo_color_legacy = 0x7f050188
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007c
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral70 = 0x7f050145
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral60 = 0x7f050144
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f12045f
me.rerere.rikkahub.debug:drawable/abc_star_half_black_48dp = 0x7f070068
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Display2 = 0x7f12019f
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral6 = 0x7f050143
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral50 = 0x7f050142
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral4 = 0x7f050140
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.Light = 0x7f120241
me.rerere.rikkahub.debug:color/teal_200 = 0x7f050340
me.rerere.rikkahub.debug:styleable/MaterialAlertDialog = 0x7f13004d
me.rerere.rikkahub.debug:color/material_slider_inactive_track_color = 0x7f0502e5
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f12003d
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f06011f
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f120389
me.rerere.rikkahub.debug:attr/actionProviderClass = 0x7f030022
me.rerere.rikkahub.debug:color/m3_sys_color_on_primary_fixed_variant = 0x7f050224
me.rerere.rikkahub.debug:attr/colorSurfaceContainerHighest = 0x7f030112
me.rerere.rikkahub.debug:color/tooltip_background_light = 0x7f050343
me.rerere.rikkahub.debug:animator/design_fab_show_motion_spec = 0x7f020002
me.rerere.rikkahub.debug:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602da
me.rerere.rikkahub.debug:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f120347
me.rerere.rikkahub.debug:dimen/mtrl_card_checked_icon_margin = 0x7f0602ab
me.rerere.rikkahub.debug:dimen/m3_btn_elevation = 0x7f0600e0
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral20 = 0x7f05013c
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_path_morphing = 0x7f1101a0
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral17 = 0x7f05013b
me.rerere.rikkahub.debug:color/m3_ref_palette_error90 = 0x7f050134
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_elevation = 0x7f0600b0
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
me.rerere.rikkahub.debug:color/m3_ref_palette_error80 = 0x7f050133
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1202b3
me.rerere.rikkahub.debug:string/assistant_page_thinking_budget_desc = 0x7f11004a
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06015c
me.rerere.rikkahub.debug:style/Widget.Material3.CompoundButton.Switch = 0x7f120385
me.rerere.rikkahub.debug:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c0039
me.rerere.rikkahub.debug:color/m3_ref_palette_error60 = 0x7f050131
me.rerere.rikkahub.debug:attr/isLightTheme = 0x7f03022e
me.rerere.rikkahub.debug:drawable/design_fab_background = 0x7f070094
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f12018c
me.rerere.rikkahub.debug:attr/contentScrim = 0x7f030133
me.rerere.rikkahub.debug:color/m3_ref_palette_error30 = 0x7f05012e
me.rerere.rikkahub.debug:attr/clockIcon = 0x7f0300ca
me.rerere.rikkahub.debug:attr/materialCalendarDay = 0x7f0302cb
me.rerere.rikkahub.debug:color/m3_ref_palette_error20 = 0x7f05012d
me.rerere.rikkahub.debug:dimen/abc_text_size_title_material = 0x7f06004f
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070015
me.rerere.rikkahub.debug:color/m3_ref_palette_error100 = 0x7f05012c
me.rerere.rikkahub.debug:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301cd
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501e3
me.rerere.rikkahub.debug:id/accessibility_custom_action_22 = 0x7f08001f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary99 = 0x7f050129
me.rerere.rikkahub.debug:string/m3c_date_picker_scroll_to_earlier_years = 0x7f110112
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary95 = 0x7f050128
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary90 = 0x7f050127
me.rerere.rikkahub.debug:styleable/AnimatedStateListDrawableCompat = 0x7f130008
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary60 = 0x7f050124
me.rerere.rikkahub.debug:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_size = 0x7f0602e9
me.rerere.rikkahub.debug:drawable/notification_template_icon_low_bg = 0x7f070103
me.rerere.rikkahub.debug:dimen/m3_searchbar_height = 0x7f0601eb
me.rerere.rikkahub.debug:styleable/TextAppearance = 0x7f130092
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary50 = 0x7f050123
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary0 = 0x7f05011d
me.rerere.rikkahub.debug:string/not_selected = 0x7f1101a9
me.rerere.rikkahub.debug:attr/badgeWithTextShapeAppearanceOverlay = 0x7f03005f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary99 = 0x7f05011c
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary95 = 0x7f05011b
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1203e0
me.rerere.rikkahub.debug:attr/listPreferredItemHeightSmall = 0x7f0302b5
me.rerere.rikkahub.debug:attr/clockNumberTextColor = 0x7f0300cb
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary70 = 0x7f050118
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0128
me.rerere.rikkahub.debug:drawable/mtrl_dialog_background = 0x7f0700de
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary99 = 0x7f05010f
me.rerere.rikkahub.debug:id/submenuarrow = 0x7f0801e5
me.rerere.rikkahub.debug:color/material_personalized_color_secondary_container = 0x7f0502c9
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary95 = 0x7f05010e
me.rerere.rikkahub.debug:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602cb
me.rerere.rikkahub.debug:styleable/LinearLayoutCompat = 0x7f130048
me.rerere.rikkahub.debug:string/m3c_date_input_headline = 0x7f110106
me.rerere.rikkahub.debug:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0048
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_secondary = 0x7f05019a
me.rerere.rikkahub.debug:color/mtrl_textinput_default_box_stroke_color = 0x7f05031e
me.rerere.rikkahub.debug:attr/subtitleTextAppearance = 0x7f0303e3
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary20 = 0x7f050106
me.rerere.rikkahub.debug:attr/buttonIconTintMode = 0x7f03008f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f050102
me.rerere.rikkahub.debug:color/m3_sys_color_dark_error_container = 0x7f050191
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f050101
me.rerere.rikkahub.debug:string/setting_display_page_title = 0x7f1101c5
me.rerere.rikkahub.debug:color/material_harmonized_color_on_error_container = 0x7f0502a2
me.rerere.rikkahub.debug:string/assistant_page_context_message_count = 0x7f110027
me.rerere.rikkahub.debug:color/m3_dynamic_dark_primary_text_disable_only = 0x7f0500b5
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f050100
me.rerere.rikkahub.debug:dimen/mtrl_slider_label_radius = 0x7f0602f7
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500ec
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500ff
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060164
me.rerere.rikkahub.debug:macro/m3_comp_fab_surface_container_color = 0x7f0c003d
me.rerere.rikkahub.debug:id/legacy = 0x7f08011f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500fe
me.rerere.rikkahub.debug:id/material_textinput_timepicker = 0x7f080137
me.rerere.rikkahub.debug:attr/materialCalendarHeaderCancelButton = 0x7f0302ce
me.rerere.rikkahub.debug:style/Widget.Material3.SideSheet.Modal = 0x7f1203d2
me.rerere.rikkahub.debug:style/Base.Widget.Material3.CollapsingToolbar = 0x7f120102
me.rerere.rikkahub.debug:attr/materialIconButtonStyle = 0x7f0302e6
me.rerere.rikkahub.debug:attr/paddingTopSystemWindowInsets = 0x7f030350
me.rerere.rikkahub.debug:string/google_api_key = 0x7f1100a4
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500fa
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f12018a
me.rerere.rikkahub.debug:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500f8
me.rerere.rikkahub.debug:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070071
me.rerere.rikkahub.debug:string/assistant_page_tab_memory = 0x7f110043
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_light = 0x7f050035
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500ea
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500e9
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b6
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary30 = 0x7f050107
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500e6
me.rerere.rikkahub.debug:styleable/PreviewView = 0x7f130077
me.rerere.rikkahub.debug:dimen/m3_btn_icon_only_min_width = 0x7f0600e6
me.rerere.rikkahub.debug:style/TextAppearance.Design.Counter.Overflow = 0x7f1201d0
me.rerere.rikkahub.debug:attr/tabMode = 0x7f0303fe
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500e5
me.rerere.rikkahub.debug:drawable/mtrl_ic_error = 0x7f0700e6
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602ed
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500dd
me.rerere.rikkahub.debug:dimen/m3_comp_filter_chip_container_height = 0x7f06013b
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0501c0
me.rerere.rikkahub.debug:attr/itemSpacing = 0x7f030247
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500dc
me.rerere.rikkahub.debug:attr/prefixTextColor = 0x7f030370
me.rerere.rikkahub.debug:string/more_options = 0x7f110169
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500d7
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f12007f
me.rerere.rikkahub.debug:color/mtrl_textinput_filled_box_default_background_color = 0x7f050320
me.rerere.rikkahub.debug:color/ucrop_color_inactive_aspect_ratio = 0x7f05034f
me.rerere.rikkahub.debug:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f030320
me.rerere.rikkahub.debug:animator/fragment_fade_exit = 0x7f020006
me.rerere.rikkahub.debug:color/material_personalized_color_on_surface_inverse = 0x7f0502bd
me.rerere.rikkahub.debug:color/m3_radiobutton_ripple_tint = 0x7f0500d1
me.rerere.rikkahub.debug:id/mtrl_calendar_days_of_week = 0x7f08014d
me.rerere.rikkahub.debug:anim/m3_motion_fade_enter = 0x7f010029
me.rerere.rikkahub.debug:dimen/m3_btn_elevated_btn_elevation = 0x7f0600df
me.rerere.rikkahub.debug:color/design_box_stroke_color = 0x7f05003c
me.rerere.rikkahub.debug:color/m3_navigation_rail_ripple_color_selector = 0x7f0500cd
me.rerere.rikkahub.debug:dimen/abc_edit_text_inset_top_material = 0x7f06002e
me.rerere.rikkahub.debug:color/m3_navigation_item_icon_tint = 0x7f0500c8
me.rerere.rikkahub.debug:color/m3_hint_foreground = 0x7f0500c2
me.rerere.rikkahub.debug:style/Widget.Material3.PopupMenu.Overflow = 0x7f1203c8
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060153
me.rerere.rikkahub.debug:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601c7
me.rerere.rikkahub.debug:attr/layout_constraintWidth_percent = 0x7f030293
me.rerere.rikkahub.debug:attr/switchPadding = 0x7f0303ec
me.rerere.rikkahub.debug:color/m3_filled_icon_button_container_color_selector = 0x7f0500c0
me.rerere.rikkahub.debug:attr/exitAnim = 0x7f0301a7
me.rerere.rikkahub.debug:attr/region_widthLessThan = 0x7f030380
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_dark_disabled = 0x7f050032
me.rerere.rikkahub.debug:color/m3_efab_ripple_color_selector = 0x7f0500bb
me.rerere.rikkahub.debug:id/actions = 0x7f080041
me.rerere.rikkahub.debug:color/m3_dynamic_dark_highlighted_text = 0x7f0500b3
me.rerere.rikkahub.debug:attr/paddingBottomNoButtons = 0x7f030348
me.rerere.rikkahub.debug:attr/bottomSheetDragHandleStyle = 0x7f030077
me.rerere.rikkahub.debug:color/m3_default_color_secondary_text = 0x7f0500b0
me.rerere.rikkahub.debug:macro/m3_comp_input_chip_container_shape = 0x7f0c005b
me.rerere.rikkahub.debug:color/m3_dark_primary_text_disable_only = 0x7f0500ae
me.rerere.rikkahub.debug:style/TextAppearance.Material3.BodyMedium = 0x7f1201eb
me.rerere.rikkahub.debug:color/m3_dark_default_color_secondary_text = 0x7f0500ab
me.rerere.rikkahub.debug:color/m3_dark_default_color_primary_text = 0x7f0500aa
me.rerere.rikkahub.debug:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601b9
me.rerere.rikkahub.debug:id/mtrl_picker_header_toggle = 0x7f08015c
me.rerere.rikkahub.debug:color/quickie_white = 0x7f050333
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_tertiary = 0x7f05019e
me.rerere.rikkahub.debug:color/m3_chip_ripple_color = 0x7f0500a7
me.rerere.rikkahub.debug:color/m3_sys_color_dark_secondary = 0x7f0501a4
me.rerere.rikkahub.debug:color/m3_chip_assist_text_color = 0x7f0500a5
me.rerere.rikkahub.debug:color/m3_button_ripple_color_selector = 0x7f05009d
me.rerere.rikkahub.debug:attr/trackTint = 0x7f030489
me.rerere.rikkahub.debug:color/m3_button_outline_color_selector = 0x7f05009b
me.rerere.rikkahub.debug:layout/notification_action_tombstone = 0x7f0b0071
me.rerere.rikkahub.debug:attr/tintNavigationIcon = 0x7f030460
me.rerere.rikkahub.debug:color/m3_bottom_sheet_drag_handle_color = 0x7f050098
me.rerere.rikkahub.debug:style/Base.V7.Theme.AppCompat.Light = 0x7f1200bc
me.rerere.rikkahub.debug:color/m3_assist_chip_icon_tint_color = 0x7f050096
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1203a8
me.rerere.rikkahub.debug:string/common_google_play_services_updating_text = 0x7f110092
me.rerere.rikkahub.debug:attr/layout_anchorGravity = 0x7f030266
me.rerere.rikkahub.debug:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060195
me.rerere.rikkahub.debug:color/m3_appbar_overlay_color = 0x7f050095
me.rerere.rikkahub.debug:anim/abc_tooltip_exit = 0x7f01000b
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.Dialog = 0x7f120054
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_icon_size = 0x7f06012a
me.rerere.rikkahub.debug:attr/materialButtonToggleGroupStyle = 0x7f0302ca
me.rerere.rikkahub.debug:color/leak_canary_white = 0x7f050091
me.rerere.rikkahub.debug:color/leak_canary_reference = 0x7f050090
me.rerere.rikkahub.debug:color/ucrop_color_active_controls_color = 0x7f050345
me.rerere.rikkahub.debug:attr/windowActionModeOverlay = 0x7f0304b6
me.rerere.rikkahub.debug:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0500cc
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_light_focused = 0x7f050038
me.rerere.rikkahub.debug:style/Widget.Material3.Slider = 0x7f1203d4
me.rerere.rikkahub.debug:style/Widget.AppCompat.SearchView.ActionBar = 0x7f12032b
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f120312
me.rerere.rikkahub.debug:color/leak_canary_help = 0x7f05008e
me.rerere.rikkahub.debug:color/leak_canary_heap_stack_trace = 0x7f05008c
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1202cb
me.rerere.rikkahub.debug:color/leak_canary_extra = 0x7f050072
me.rerere.rikkahub.debug:color/leak_canary_heap_short_array = 0x7f05008b
me.rerere.rikkahub.debug:string/material_clock_display_divider = 0x7f110143
me.rerere.rikkahub.debug:attr/startDestination = 0x7f0303c5
me.rerere.rikkahub.debug:color/leak_canary_heap_int_array = 0x7f050086
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f120096
me.rerere.rikkahub.debug:attr/actionBarTheme = 0x7f03000a
me.rerere.rikkahub.debug:string/setting_page_theme_type_standard = 0x7f1101e9
me.rerere.rikkahub.debug:attr/boxStrokeWidthFocused = 0x7f030083
me.rerere.rikkahub.debug:attr/buttonStyleSmall = 0x7f030093
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Bridge = 0x7f12024d
me.rerere.rikkahub.debug:color/leak_canary_heap_double_array = 0x7f050080
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f120262
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_height = 0x7f060063
me.rerere.rikkahub.debug:string/tooltip_label = 0x7f1101fc
me.rerere.rikkahub.debug:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
me.rerere.rikkahub.debug:color/material_dynamic_secondary90 = 0x7f050288
me.rerere.rikkahub.debug:color/leak_canary_heap_char_array = 0x7f05007e
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500e8
me.rerere.rikkahub.debug:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601d8
me.rerere.rikkahub.debug:attr/minWidth = 0x7f030304
me.rerere.rikkahub.debug:layout/abc_expanded_menu_layout = 0x7f0b000d
me.rerere.rikkahub.debug:color/leak_canary_heap_byte_array = 0x7f05007d
me.rerere.rikkahub.debug:attr/progressBarStyle = 0x7f030374
me.rerere.rikkahub.debug:color/bright_foreground_inverse_material_light = 0x7f050025
me.rerere.rikkahub.debug:color/leak_canary_gray_darkest_40p = 0x7f050078
me.rerere.rikkahub.debug:color/leak_canary_gray_6f = 0x7f050075
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060136
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary100 = 0x7f050105
me.rerere.rikkahub.debug:color/leak_canary_count_new = 0x7f05006f
me.rerere.rikkahub.debug:color/leak_canary_count_default = 0x7f05006e
me.rerere.rikkahub.debug:color/ripple_material_light = 0x7f050335
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06015d
me.rerere.rikkahub.debug:attr/cornerSizeTopRight = 0x7f030142
me.rerere.rikkahub.debug:id/accessibility_custom_action_14 = 0x7f080016
me.rerere.rikkahub.debug:color/foreground_material_light = 0x7f050068
me.rerere.rikkahub.debug:integer/m3c_window_layout_in_display_cutout_mode = 0x7f090027
me.rerere.rikkahub.debug:color/error_color_material_dark = 0x7f050065
me.rerere.rikkahub.debug:color/leak_canary_heap_boolean_array = 0x7f05007c
me.rerere.rikkahub.debug:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f120330
me.rerere.rikkahub.debug:string/translator_page_target_language = 0x7f110202
me.rerere.rikkahub.debug:attr/layout_editor_absoluteY = 0x7f030296
me.rerere.rikkahub.debug:attr/moveWhenScrollAtTop = 0x7f030330
me.rerere.rikkahub.debug:attr/tabRippleColor = 0x7f030404
me.rerere.rikkahub.debug:drawable/ic_mtrl_chip_close_circle = 0x7f0700ab
me.rerere.rikkahub.debug:drawable/abc_ic_search_api_material = 0x7f070048
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f12026f
me.rerere.rikkahub.debug:color/dim_foreground_material_light = 0x7f050064
me.rerere.rikkahub.debug:attr/popUpToSaveState = 0x7f030369
me.rerere.rikkahub.debug:anim/design_snackbar_in = 0x7f01001a
me.rerere.rikkahub.debug:color/design_snackbar_background_color = 0x7f050060
me.rerere.rikkahub.debug:string/assistant_page_system_prompt = 0x7f110041
me.rerere.rikkahub.debug:drawable/notification_bg_normal = 0x7f0700fe
me.rerere.rikkahub.debug:color/m3_ref_palette_primary40 = 0x7f050161
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Title = 0x7f12002f
me.rerere.rikkahub.debug:color/mtrl_indicator_text_color = 0x7f050306
me.rerere.rikkahub.debug:attr/materialAlertDialogTitleTextStyle = 0x7f0302c7
me.rerere.rikkahub.debug:color/design_icon_tint = 0x7f05005f
me.rerere.rikkahub.debug:color/design_fab_shadow_start_color = 0x7f05005a
me.rerere.rikkahub.debug:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070034
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Caption = 0x7f12019d
me.rerere.rikkahub.debug:drawable/leak_canary_tab_selector_ripple = 0x7f0700bb
me.rerere.rikkahub.debug:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602f3
me.rerere.rikkahub.debug:color/m3_ref_palette_error95 = 0x7f050135
me.rerere.rikkahub.debug:attr/dayTodayStyle = 0x7f03015d
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06012d
me.rerere.rikkahub.debug:drawable/abc_seekbar_thumb_material = 0x7f070062
me.rerere.rikkahub.debug:color/material_personalized_color_error = 0x7f0502b3
me.rerere.rikkahub.debug:color/design_fab_shadow_mid_color = 0x7f050059
me.rerere.rikkahub.debug:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0500c5
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1203ab
me.rerere.rikkahub.debug:drawable/abc_popup_background_mtrl_mult = 0x7f070059
me.rerere.rikkahub.debug:color/design_fab_shadow_end_color = 0x7f050058
me.rerere.rikkahub.debug:attr/layout_constraintStart_toEndOf = 0x7f030287
me.rerere.rikkahub.debug:color/design_default_color_surface = 0x7f050056
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1203a9
me.rerere.rikkahub.debug:dimen/m3_small_fab_size = 0x7f0601fd
me.rerere.rikkahub.debug:color/m3_default_color_primary_text = 0x7f0500af
me.rerere.rikkahub.debug:attr/collapseContentDescription = 0x7f0300d4
me.rerere.rikkahub.debug:attr/hintTextAppearance = 0x7f030210
me.rerere.rikkahub.debug:color/design_default_color_error = 0x7f05004b
me.rerere.rikkahub.debug:layout/abc_select_dialog_material = 0x7f0b001a
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060178
me.rerere.rikkahub.debug:attr/boxCornerRadiusBottomStart = 0x7f03007d
me.rerere.rikkahub.debug:color/design_default_color_background = 0x7f05004a
me.rerere.rikkahub.debug:dimen/tooltip_precise_anchor_threshold = 0x7f06032f
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialDivider = 0x7f12043c
me.rerere.rikkahub.debug:attr/colorPrimaryFixedDim = 0x7f030104
me.rerere.rikkahub.debug:anim/abc_slide_out_bottom = 0x7f010008
me.rerere.rikkahub.debug:attr/nestedScrollable = 0x7f03033b
me.rerere.rikkahub.debug:attr/arcMode = 0x7f030036
me.rerere.rikkahub.debug:attr/motionDurationLong4 = 0x7f030313
me.rerere.rikkahub.debug:color/design_dark_default_color_secondary_variant = 0x7f050048
me.rerere.rikkahub.debug:color/design_dark_default_color_secondary = 0x7f050047
me.rerere.rikkahub.debug:color/material_personalized_color_control_activated = 0x7f0502b0
me.rerere.rikkahub.debug:attr/shortcutMatchRequired = 0x7f0303a3
me.rerere.rikkahub.debug:color/design_dark_default_color_primary_variant = 0x7f050046
me.rerere.rikkahub.debug:attr/marginRightSystemWindowInsets = 0x7f0302c0
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_width = 0x7f06028b
me.rerere.rikkahub.debug:color/design_dark_default_color_on_secondary = 0x7f050042
me.rerere.rikkahub.debug:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002d
me.rerere.rikkahub.debug:drawable/abc_ratingbar_small_material = 0x7f07005c
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f120191
me.rerere.rikkahub.debug:color/design_dark_default_color_on_primary = 0x7f050041
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120434
me.rerere.rikkahub.debug:attr/actionModeCopyDrawable = 0x7f030015
me.rerere.rikkahub.debug:dimen/tooltip_precise_anchor_extra_offset = 0x7f06032e
me.rerere.rikkahub.debug:color/design_dark_default_color_error = 0x7f05003e
me.rerere.rikkahub.debug:attr/iconStartPadding = 0x7f03021c
me.rerere.rikkahub.debug:color/common_google_signin_btn_tint = 0x7f05003a
me.rerere.rikkahub.debug:drawable/ucrop_vector_loader_animated = 0x7f07012c
me.rerere.rikkahub.debug:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060173
me.rerere.rikkahub.debug:color/cardview_shadow_start_color = 0x7f05002f
me.rerere.rikkahub.debug:attr/preserveIconSpacing = 0x7f030371
me.rerere.rikkahub.debug:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f12033e
me.rerere.rikkahub.debug:color/cardview_shadow_end_color = 0x7f05002e
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007d
me.rerere.rikkahub.debug:color/call_notification_answer_color = 0x7f05002a
me.rerere.rikkahub.debug:color/button_material_dark = 0x7f050028
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light = 0x7f12005f
me.rerere.rikkahub.debug:color/bright_foreground_material_light = 0x7f050027
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant10 = 0x7f050265
me.rerere.rikkahub.debug:color/bright_foreground_material_dark = 0x7f050026
me.rerere.rikkahub.debug:string/assistant_page_add_body = 0x7f11001f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500e2
me.rerere.rikkahub.debug:attr/itemShapeAppearance = 0x7f030240
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f120293
me.rerere.rikkahub.debug:color/background_material_dark = 0x7f05001f
me.rerere.rikkahub.debug:styleable/BottomSheetBehavior_Layout = 0x7f130018
me.rerere.rikkahub.debug:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f120218
me.rerere.rikkahub.debug:attr/cardElevation = 0x7f030098
me.rerere.rikkahub.debug:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1203ca
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f120090
me.rerere.rikkahub.debug:style/Animation.AppCompat.DropDownUp = 0x7f120003
me.rerere.rikkahub.debug:color/background_floating_material_light = 0x7f05001e
me.rerere.rikkahub.debug:color/ucrop_color_toolbar_widget = 0x7f050354
me.rerere.rikkahub.debug:color/abc_primary_text_disable_only_material_light = 0x7f05000a
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f120316
me.rerere.rikkahub.debug:color/abc_primary_text_disable_only_material_dark = 0x7f050009
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton = 0x7f120407
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1203aa
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Badge = 0x7f1201fd
me.rerere.rikkahub.debug:dimen/abc_dialog_fixed_height_major = 0x7f06001c
me.rerere.rikkahub.debug:color/abc_hint_foreground_material_dark = 0x7f050007
me.rerere.rikkahub.debug:string/abc_menu_space_shortcut_label = 0x7f11000f
me.rerere.rikkahub.debug:id/noScroll = 0x7f08016c
me.rerere.rikkahub.debug:id/mtrl_picker_header_selection_text = 0x7f08015a
me.rerere.rikkahub.debug:color/m3_sys_color_dark_background = 0x7f05018f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary90 = 0x7f05010d
me.rerere.rikkahub.debug:dimen/leak_canary_row_margins = 0x7f0600a5
me.rerere.rikkahub.debug:color/abc_decor_view_status_guard_light = 0x7f050006
me.rerere.rikkahub.debug:attr/materialCalendarStyle = 0x7f0302d7
me.rerere.rikkahub.debug:color/abc_decor_view_status_guard = 0x7f050005
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f120197
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06018b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Badge = 0x7f1203fa
me.rerere.rikkahub.debug:color/m3_dynamic_dark_default_color_secondary_text = 0x7f0500b2
me.rerere.rikkahub.debug:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f060119
me.rerere.rikkahub.debug:color/material_dynamic_secondary40 = 0x7f050283
me.rerere.rikkahub.debug:color/abc_color_highlight_material = 0x7f050004
me.rerere.rikkahub.debug:color/abc_btn_colored_borderless_text_material = 0x7f050002
me.rerere.rikkahub.debug:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f0601a6
me.rerere.rikkahub.debug:bool/leak_canary_add_dynamic_shortcut = 0x7f040005
me.rerere.rikkahub.debug:attr/thickness = 0x7f030445
me.rerere.rikkahub.debug:dimen/fastscroll_margin = 0x7f060091
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f0501b4
me.rerere.rikkahub.debug:bool/enable_system_alarm_service_default = 0x7f040002
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700dd
me.rerere.rikkahub.debug:attr/clockHandColor = 0x7f0300c9
me.rerere.rikkahub.debug:bool/abc_action_bar_embed_tabs = 0x7f040000
me.rerere.rikkahub.debug:dimen/m3_navigation_item_shape_inset_end = 0x7f0601d3
me.rerere.rikkahub.debug:attr/yearTodayStyle = 0x7f0304c0
me.rerere.rikkahub.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
me.rerere.rikkahub.debug:dimen/abc_action_bar_default_height_material = 0x7f060002
me.rerere.rikkahub.debug:style/Widget.AppCompat.Spinner.Underlined = 0x7f120331
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary10 = 0x7f05011e
me.rerere.rikkahub.debug:attr/windowNoTitle = 0x7f0304bd
me.rerere.rikkahub.debug:layout/custom_dialog = 0x7f0b001c
me.rerere.rikkahub.debug:color/ripple_material_dark = 0x7f050334
me.rerere.rikkahub.debug:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f12023e
me.rerere.rikkahub.debug:attr/windowMinWidthMajor = 0x7f0304bb
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.PopupWindow = 0x7f1200ec
me.rerere.rikkahub.debug:attr/actionModeCutDrawable = 0x7f030016
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1202db
me.rerere.rikkahub.debug:attr/windowFixedWidthMajor = 0x7f0304b9
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0132
me.rerere.rikkahub.debug:attr/colorOnTertiaryContainer = 0x7f0300fb
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f07008a
me.rerere.rikkahub.debug:attr/waveShape = 0x7f0304b2
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501d7
me.rerere.rikkahub.debug:string/setting_display_page_chat_list_model_icon_title = 0x7f1101c0
me.rerere.rikkahub.debug:attr/viewInflaterClass = 0x7f0304ab
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120119
me.rerere.rikkahub.debug:attr/textAppearanceCaption = 0x7f030418
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f12042b
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f1203fd
me.rerere.rikkahub.debug:attr/ucrop_show_frame = 0x7f0304a1
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary30 = 0x7f050114
me.rerere.rikkahub.debug:attr/tint = 0x7f03045e
me.rerere.rikkahub.debug:color/material_on_primary_disabled = 0x7f0502a6
me.rerere.rikkahub.debug:attr/mock_labelColor = 0x7f030308
me.rerere.rikkahub.debug:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
me.rerere.rikkahub.debug:attr/ucrop_grid_stroke_size = 0x7f0304a0
me.rerere.rikkahub.debug:attr/colorSurface = 0x7f03010e
me.rerere.rikkahub.debug:attr/layout_constraintRight_toRightOf = 0x7f030286
me.rerere.rikkahub.debug:string/setting_page_providers = 0x7f1101e0
me.rerere.rikkahub.debug:attr/colorPrimaryVariant = 0x7f030107
me.rerere.rikkahub.debug:style/Widget.Material3.NavigationRailView = 0x7f1203c1
me.rerere.rikkahub.debug:attr/prefixTextAppearance = 0x7f03036f
me.rerere.rikkahub.debug:attr/ucrop_grid_column_count = 0x7f03049e
me.rerere.rikkahub.debug:string/material_hour_selection = 0x7f110146
me.rerere.rikkahub.debug:color/m3_card_ripple_color = 0x7f0500a1
me.rerere.rikkahub.debug:color/design_default_color_on_secondary = 0x7f05004f
me.rerere.rikkahub.debug:dimen/m3_carousel_small_item_size_max = 0x7f060101
me.rerere.rikkahub.debug:attr/itemActiveIndicatorStyle = 0x7f030232
me.rerere.rikkahub.debug:attr/ucrop_dimmed_color = 0x7f03049a
me.rerere.rikkahub.debug:attr/logoScaleType = 0x7f0302bd
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f120056
me.rerere.rikkahub.debug:attr/ucrop_aspect_ratio_y = 0x7f030498
me.rerere.rikkahub.debug:dimen/m3_btn_stroke_size = 0x7f0600ed
me.rerere.rikkahub.debug:color/m3_ref_palette_error10 = 0x7f05012b
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1203b1
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1202ea
me.rerere.rikkahub.debug:string/m3c_time_input_dialog_title = 0x7f11012f
me.rerere.rikkahub.debug:attr/ucrop_artv_ratio_y = 0x7f030496
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f120362
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06014b
me.rerere.rikkahub.debug:color/m3_tabs_icon_color = 0x7f05022f
me.rerere.rikkahub.debug:attr/searchPrefixText = 0x7f030391
me.rerere.rikkahub.debug:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f12010b
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
me.rerere.rikkahub.debug:attr/ucrop_artv_ratio_x = 0x7f030495
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f120428
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1200d0
me.rerere.rikkahub.debug:attr/triggerSlack = 0x7f030492
me.rerere.rikkahub.debug:id/accessibility_custom_action_5 = 0x7f08002b
me.rerere.rikkahub.debug:attr/triggerReceiver = 0x7f030491
me.rerere.rikkahub.debug:id/dragStart = 0x7f08009a
me.rerere.rikkahub.debug:color/leak_canary_count_new_border = 0x7f050070
me.rerere.rikkahub.debug:attr/transitionShapeAppearance = 0x7f03048f
me.rerere.rikkahub.debug:attr/colorOnTertiaryFixed = 0x7f0300fc
me.rerere.rikkahub.debug:attr/transitionPathRotate = 0x7f03048e
me.rerere.rikkahub.debug:bool/enable_system_job_service_default = 0x7f040004
me.rerere.rikkahub.debug:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070073
me.rerere.rikkahub.debug:string/abc_action_menu_overflow_description = 0x7f110002
me.rerere.rikkahub.debug:attr/textAppearanceHeadline4 = 0x7f03041f
me.rerere.rikkahub.debug:attr/transitionDisable = 0x7f03048b
me.rerere.rikkahub.debug:attr/customNavigationLayout = 0x7f030155
me.rerere.rikkahub.debug:attr/tabIconTint = 0x7f0303f2
me.rerere.rikkahub.debug:dimen/notification_subtext_size = 0x7f060328
me.rerere.rikkahub.debug:string/mtrl_picker_save = 0x7f110191
me.rerere.rikkahub.debug:string/leak_canary_about_menu = 0x7f1100b0
me.rerere.rikkahub.debug:dimen/compat_button_inset_horizontal_material = 0x7f060056
me.rerere.rikkahub.debug:attr/flow_padding = 0x7f0301e6
me.rerere.rikkahub.debug:anim/m3_side_sheet_enter_from_left = 0x7f01002b
me.rerere.rikkahub.debug:attr/trackThickness = 0x7f030488
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001c
me.rerere.rikkahub.debug:attr/overlay = 0x7f030347
me.rerere.rikkahub.debug:attr/buttonBarNegativeButtonStyle = 0x7f030086
me.rerere.rikkahub.debug:color/leak_canary_gray_3f = 0x7f050074
me.rerere.rikkahub.debug:attr/trackColorInactive = 0x7f030480
me.rerere.rikkahub.debug:styleable/CheckedTextView = 0x7f13001d
me.rerere.rikkahub.debug:string/leak_canary_tv_analysis_failure = 0x7f1100ee
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500f5
me.rerere.rikkahub.debug:attr/trackColorActive = 0x7f03047f
me.rerere.rikkahub.debug:dimen/mtrl_navigation_item_icon_size = 0x7f0602d8
me.rerere.rikkahub.debug:dimen/m3_large_fab_max_image_size = 0x7f0601c9
me.rerere.rikkahub.debug:attr/altSrc = 0x7f03002f
me.rerere.rikkahub.debug:color/design_fab_stroke_end_inner_color = 0x7f05005b
me.rerere.rikkahub.debug:attr/touchAnchorSide = 0x7f03047b
me.rerere.rikkahub.debug:id/matrix = 0x7f08013e
me.rerere.rikkahub.debug:attr/touchAnchorId = 0x7f03047a
me.rerere.rikkahub.debug:drawable/btn_radio_off_mtrl = 0x7f07007d
me.rerere.rikkahub.debug:string/model_list_chat = 0x7f110164
me.rerere.rikkahub.debug:attr/tooltipFrameBackground = 0x7f030476
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0600ad
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
me.rerere.rikkahub.debug:color/material_dynamic_primary99 = 0x7f05027d
me.rerere.rikkahub.debug:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0104
me.rerere.rikkahub.debug:attr/tooltipForegroundColor = 0x7f030475
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_pressed = 0x7f0700ef
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral96 = 0x7f05014c
me.rerere.rikkahub.debug:attr/layout_constraintGuide_begin = 0x7f030277
me.rerere.rikkahub.debug:color/m3_navigation_bar_ripple_color_selector = 0x7f0500c6
me.rerere.rikkahub.debug:attr/actionDropDownStyle = 0x7f03000d
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_container_color = 0x7f0c0099
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_short2 = 0x7f09001d
me.rerere.rikkahub.debug:attr/toolbarStyle = 0x7f030473
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1201c7
me.rerere.rikkahub.debug:dimen/mtrl_switch_text_padding = 0x7f060306
me.rerere.rikkahub.debug:string/common_signin_button_text_long = 0x7f110096
me.rerere.rikkahub.debug:color/accent_material_dark = 0x7f050019
me.rerere.rikkahub.debug:attr/toolbarId = 0x7f030471
me.rerere.rikkahub.debug:styleable/SearchBar = 0x7f130080
me.rerere.rikkahub.debug:attr/strokeWidth = 0x7f0303da
me.rerere.rikkahub.debug:attr/titlePositionInterpolator = 0x7f03046b
me.rerere.rikkahub.debug:drawable/abc_btn_borderless_material = 0x7f07002a
me.rerere.rikkahub.debug:attr/titleMargins = 0x7f03046a
me.rerere.rikkahub.debug:drawable/abc_seekbar_track_material = 0x7f070064
me.rerere.rikkahub.debug:attr/fontProviderFetchTimeout = 0x7f0301f2
me.rerere.rikkahub.debug:integer/google_play_services_version = 0x7f090008
me.rerere.rikkahub.debug:id/wrapper_controls = 0x7f08023d
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500f4
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1202dc
me.rerere.rikkahub.debug:attr/searchIcon = 0x7f030390
me.rerere.rikkahub.debug:anim/abc_slide_in_top = 0x7f010007
me.rerere.rikkahub.debug:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f12003e
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c0169
me.rerere.rikkahub.debug:color/material_on_surface_emphasis_medium = 0x7f0502ab
me.rerere.rikkahub.debug:color/bright_foreground_inverse_material_dark = 0x7f050024
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f120152
me.rerere.rikkahub.debug:style/Base.AlertDialog.AppCompat = 0x7f12000b
me.rerere.rikkahub.debug:attr/titleMarginStart = 0x7f030468
me.rerere.rikkahub.debug:style/TextAppearance.Material3.TitleMedium = 0x7f1201fb
me.rerere.rikkahub.debug:string/m3c_date_range_picker_title = 0x7f110124
me.rerere.rikkahub.debug:attr/actionModePopupWindowStyle = 0x7f030019
me.rerere.rikkahub.debug:string/leak_canary_explorer_search_classes = 0x7f1100bd
me.rerere.rikkahub.debug:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
me.rerere.rikkahub.debug:attr/cardBackgroundColor = 0x7f030096
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602ee
me.rerere.rikkahub.debug:attr/titleMarginEnd = 0x7f030467
me.rerere.rikkahub.debug:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
me.rerere.rikkahub.debug:color/m3_button_ripple_color = 0x7f05009c
me.rerere.rikkahub.debug:attr/titleMargin = 0x7f030465
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_medium4 = 0x7f09001b
me.rerere.rikkahub.debug:attr/titleEnabled = 0x7f030464
me.rerere.rikkahub.debug:id/barrier = 0x7f080054
me.rerere.rikkahub.debug:drawable/tooltip_frame_light = 0x7f07010d
me.rerere.rikkahub.debug:color/m3_checkbox_button_tint = 0x7f0500a4
me.rerere.rikkahub.debug:attr/tintMode = 0x7f03045f
me.rerere.rikkahub.debug:string/tooltip_description = 0x7f1101fb
me.rerere.rikkahub.debug:color/m3_dynamic_highlighted_text = 0x7f0500b8
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c2
me.rerere.rikkahub.debug:attr/tickRadiusInactive = 0x7f03045c
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f120130
me.rerere.rikkahub.debug:color/material_personalized_hint_foreground = 0x7f0502dd
me.rerere.rikkahub.debug:color/abc_tint_spinner = 0x7f050017
me.rerere.rikkahub.debug:dimen/mtrl_switch_thumb_elevation = 0x7f060307
me.rerere.rikkahub.debug:attr/tickColorInactive = 0x7f030457
me.rerere.rikkahub.debug:attr/thumbWidth = 0x7f030454
me.rerere.rikkahub.debug:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1200b2
me.rerere.rikkahub.debug:string/leak_canary_permission_notification_title = 0x7f1100e6
me.rerere.rikkahub.debug:attr/textAppearanceLabelSmall = 0x7f030427
me.rerere.rikkahub.debug:attr/thumbTrackGapSize = 0x7f030453
me.rerere.rikkahub.debug:dimen/m3_extended_fab_icon_padding = 0x7f0601c1
me.rerere.rikkahub.debug:attr/state_dragged = 0x7f0303d0
me.rerere.rikkahub.debug:attr/thumbTextPadding = 0x7f030450
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c016f
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060292
me.rerere.rikkahub.debug:dimen/m3_comp_badge_large_size = 0x7f06010f
me.rerere.rikkahub.debug:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003c
me.rerere.rikkahub.debug:attr/closeIconSize = 0x7f0300cf
me.rerere.rikkahub.debug:attr/thumbStrokeColor = 0x7f03044e
me.rerere.rikkahub.debug:attr/thumbRadius = 0x7f03044d
me.rerere.rikkahub.debug:attr/yearSelectedStyle = 0x7f0304be
me.rerere.rikkahub.debug:drawable/ic_call_answer_video = 0x7f07009e
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0135
me.rerere.rikkahub.debug:dimen/m3_badge_offset = 0x7f0600c4
me.rerere.rikkahub.debug:dimen/design_navigation_elevation = 0x7f060075
me.rerere.rikkahub.debug:style/Widget.AppCompat.Button.Colored = 0x7f1202fc
me.rerere.rikkahub.debug:attr/minHideDelay = 0x7f030301
me.rerere.rikkahub.debug:attr/textStartPadding = 0x7f030443
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary10 = 0x7f050104
me.rerere.rikkahub.debug:string/model_list_no_providers = 0x7f110167
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button = 0x7f0700d4
me.rerere.rikkahub.debug:dimen/ucrop_height_crop_aspect_ratio_text = 0x7f06033a
me.rerere.rikkahub.debug:attr/boxCornerRadiusBottomEnd = 0x7f03007c
me.rerere.rikkahub.debug:attr/motionPath = 0x7f030329
me.rerere.rikkahub.debug:attr/closeIcon = 0x7f0300cc
me.rerere.rikkahub.debug:attr/textAppearanceSubtitle1 = 0x7f030432
me.rerere.rikkahub.debug:attr/drawableTopCompat = 0x7f03017f
me.rerere.rikkahub.debug:attr/waveOffset = 0x7f0304b0
me.rerere.rikkahub.debug:attr/hideOnScroll = 0x7f03020d
me.rerere.rikkahub.debug:attr/dragScale = 0x7f030174
me.rerere.rikkahub.debug:attr/textInputOutlinedStyle = 0x7f030440
me.rerere.rikkahub.debug:attr/motionEasingStandardAccelerateInterpolator = 0x7f030325
me.rerere.rikkahub.debug:attr/thumbIcon = 0x7f030449
me.rerere.rikkahub.debug:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03043f
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button.Colored = 0x7f1200d1
me.rerere.rikkahub.debug:attr/textInputLayoutFocusedRectEnabled = 0x7f03043d
me.rerere.rikkahub.debug:style/Widget.Material3.SideSheet = 0x7f1203d0
me.rerere.rikkahub.debug:attr/textInputFilledStyle = 0x7f03043c
me.rerere.rikkahub.debug:drawable/abc_text_select_handle_left_mtrl = 0x7f07006e
me.rerere.rikkahub.debug:attr/textInputFilledExposedDropdownMenuStyle = 0x7f03043b
me.rerere.rikkahub.debug:dimen/m3_chip_corner_size = 0x7f060104
me.rerere.rikkahub.debug:attr/useDrawerArrowDrawable = 0x7f0304a6
me.rerere.rikkahub.debug:attr/wavePeriod = 0x7f0304b1
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0501ce
me.rerere.rikkahub.debug:attr/textEndPadding = 0x7f030439
me.rerere.rikkahub.debug:attr/textInputOutlinedDenseStyle = 0x7f03043e
me.rerere.rikkahub.debug:bool/leak_canary_allow_in_non_debuggable_build = 0x7f040007
me.rerere.rikkahub.debug:attr/textColorAlertDialogListItem = 0x7f030437
me.rerere.rikkahub.debug:attr/textAppearanceTitleSmall = 0x7f030436
me.rerere.rikkahub.debug:attr/actionLayout = 0x7f03000e
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700ee
me.rerere.rikkahub.debug:animator/m3_card_state_list_anim = 0x7f02000d
me.rerere.rikkahub.debug:attr/motionStagger = 0x7f03032c
me.rerere.rikkahub.debug:attr/circleCrop = 0x7f0300c4
me.rerere.rikkahub.debug:attr/textAppearanceTitleMedium = 0x7f030435
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f120422
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f120079
me.rerere.rikkahub.debug:string/m3c_date_input_invalid_for_pattern = 0x7f110108
me.rerere.rikkahub.debug:attr/colorOnPrimaryFixedVariant = 0x7f0300f1
me.rerere.rikkahub.debug:attr/textAppearanceTitleLarge = 0x7f030434
me.rerere.rikkahub.debug:attr/textAppearanceSubtitle2 = 0x7f030433
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_bright = 0x7f050219
me.rerere.rikkahub.debug:style/leak_canary_Theme.Transparent = 0x7f12046e
me.rerere.rikkahub.debug:color/leak_canary_leak = 0x7f05008f
me.rerere.rikkahub.debug:attr/textAppearanceSmallPopupMenu = 0x7f030431
me.rerere.rikkahub.debug:style/ucrop_TextViewCropAspectRatio = 0x7f120471
me.rerere.rikkahub.debug:drawable/quickie_ic_torch = 0x7f070109
me.rerere.rikkahub.debug:dimen/abc_text_size_medium_material = 0x7f060049
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_error = 0x7f0501da
me.rerere.rikkahub.debug:color/leak_canary_gray_darkest = 0x7f050076
me.rerere.rikkahub.debug:attr/collapsedTitleTextColor = 0x7f0300d9
me.rerere.rikkahub.debug:attr/textAppearanceSearchResultSubtitle = 0x7f03042f
me.rerere.rikkahub.debug:attr/textAppearanceOverline = 0x7f03042d
me.rerere.rikkahub.debug:integer/m3_btn_anim_delay_ms = 0x7f09000b
me.rerere.rikkahub.debug:attr/materialCalendarDayOfWeekLabel = 0x7f0302cc
me.rerere.rikkahub.debug:attr/tickVisible = 0x7f03045d
me.rerere.rikkahub.debug:id/wrapper_rotate_by_angle = 0x7f08023f
me.rerere.rikkahub.debug:id/mtrl_picker_header = 0x7f080159
me.rerere.rikkahub.debug:attr/textAppearanceLineHeightEnabled = 0x7f030429
me.rerere.rikkahub.debug:string/mtrl_picker_toggle_to_year_selection = 0x7f11019d
me.rerere.rikkahub.debug:color/button_material_light = 0x7f050029
me.rerere.rikkahub.debug:attr/textAppearanceLargePopupMenu = 0x7f030428
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_path_unchecked = 0x7f110172
me.rerere.rikkahub.debug:dimen/m3_card_elevated_dragged_z = 0x7f0600f7
me.rerere.rikkahub.debug:color/m3_dark_hint_foreground = 0x7f0500ad
me.rerere.rikkahub.debug:attr/colorErrorContainer = 0x7f0300e8
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1201bd
me.rerere.rikkahub.debug:attr/textAppearanceLabelLarge = 0x7f030425
me.rerere.rikkahub.debug:attr/textAppearanceHeadlineSmall = 0x7f030424
me.rerere.rikkahub.debug:drawable/ic_keyboard_black_24dp = 0x7f0700a4
me.rerere.rikkahub.debug:attr/textAppearanceHeadlineMedium = 0x7f030423
me.rerere.rikkahub.debug:style/ShapeAppearance.Material3.Corner.Small = 0x7f12017a
me.rerere.rikkahub.debug:drawable/ic_mtrl_chip_checked_black = 0x7f0700a9
me.rerere.rikkahub.debug:attr/textAppearanceHeadline3 = 0x7f03041e
me.rerere.rikkahub.debug:drawable/ic_m3_chip_close = 0x7f0700a7
me.rerere.rikkahub.debug:attr/textAppearanceHeadline2 = 0x7f03041d
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500d9
me.rerere.rikkahub.debug:style/Theme.MaterialComponents = 0x7f12024b
me.rerere.rikkahub.debug:id/image_view_crop = 0x7f0800d6
me.rerere.rikkahub.debug:attr/colorTertiary = 0x7f030119
me.rerere.rikkahub.debug:attr/textAppearanceButton = 0x7f030417
me.rerere.rikkahub.debug:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
me.rerere.rikkahub.debug:attr/motionDurationMedium3 = 0x7f030316
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f06020c
me.rerere.rikkahub.debug:drawable/ucrop_ic_sharpness = 0x7f070122
me.rerere.rikkahub.debug:color/m3_timepicker_clock_text_color = 0x7f050240
me.rerere.rikkahub.debug:attr/tabContentStart = 0x7f0303f0
me.rerere.rikkahub.debug:attr/textAppearanceBodyLarge = 0x7f030414
me.rerere.rikkahub.debug:dimen/m3_comp_scrim_container_opacity = 0x7f06017d
me.rerere.rikkahub.debug:dimen/design_fab_border_width = 0x7f06006e
me.rerere.rikkahub.debug:style/Base.V21.Theme.AppCompat = 0x7f1200a1
me.rerere.rikkahub.debug:attr/colorSurfaceInverse = 0x7f030116
me.rerere.rikkahub.debug:id/design_navigation_view = 0x7f08008e
me.rerere.rikkahub.debug:attr/textAppearanceBody1 = 0x7f030412
me.rerere.rikkahub.debug:color/design_dark_default_color_surface = 0x7f050049
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060150
me.rerere.rikkahub.debug:style/Widget.Material3.Snackbar.FullWidth = 0x7f1203d9
me.rerere.rikkahub.debug:attr/materialCalendarMonthNavigationButton = 0x7f0302d6
me.rerere.rikkahub.debug:color/leak_canary_heap_float_array = 0x7f050081
me.rerere.rikkahub.debug:attr/closeIconEnabled = 0x7f0300cd
me.rerere.rikkahub.debug:attr/textAllCaps = 0x7f030411
me.rerere.rikkahub.debug:attr/duration = 0x7f030186
me.rerere.rikkahub.debug:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002e
me.rerere.rikkahub.debug:attr/drawableSize = 0x7f03017b
me.rerere.rikkahub.debug:attr/telltales_velocityMode = 0x7f030410
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1203e1
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_error_container = 0x7f050197
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f120077
me.rerere.rikkahub.debug:attr/telltales_tailScale = 0x7f03040f
me.rerere.rikkahub.debug:attr/telltales_tailColor = 0x7f03040e
me.rerere.rikkahub.debug:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f06029f
me.rerere.rikkahub.debug:attr/targetPackage = 0x7f03040d
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1200d7
me.rerere.rikkahub.debug:layout/design_layout_snackbar_include = 0x7f0b0020
me.rerere.rikkahub.debug:attr/lastBaselineToBottomHeight = 0x7f03025e
me.rerere.rikkahub.debug:attr/targetId = 0x7f03040c
me.rerere.rikkahub.debug:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301b6
me.rerere.rikkahub.debug:attr/tabUnboundedRipple = 0x7f03040b
me.rerere.rikkahub.debug:attr/layout_collapseMode = 0x7f030268
me.rerere.rikkahub.debug:string/leak_canary_heap_dump_enabled_text = 0x7f1100c9
me.rerere.rikkahub.debug:color/m3_chip_background_color = 0x7f0500a6
me.rerere.rikkahub.debug:id/graph_wrap = 0x7f0800c4
me.rerere.rikkahub.debug:attr/tabStyle = 0x7f030408
me.rerere.rikkahub.debug:attr/roundPercent = 0x7f030387
me.rerere.rikkahub.debug:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
me.rerere.rikkahub.debug:color/background_floating_material_dark = 0x7f05001d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f120416
me.rerere.rikkahub.debug:color/mtrl_textinput_hovered_box_stroke_color = 0x7f050322
me.rerere.rikkahub.debug:attr/tabPaddingTop = 0x7f030403
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0011
me.rerere.rikkahub.debug:attr/backgroundInsetBottom = 0x7f030047
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500e0
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00b8
me.rerere.rikkahub.debug:attr/spinnerStyle = 0x7f0303c0
me.rerere.rikkahub.debug:color/background_material_light = 0x7f050020
me.rerere.rikkahub.debug:attr/carousel_alignment = 0x7f03009e
me.rerere.rikkahub.debug:dimen/design_navigation_padding_bottom = 0x7f06007c
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060296
me.rerere.rikkahub.debug:attr/tabPaddingEnd = 0x7f030401
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary60 = 0x7f050117
me.rerere.rikkahub.debug:color/m3_ref_palette_error50 = 0x7f050130
me.rerere.rikkahub.debug:attr/tabMinWidth = 0x7f0303fd
me.rerere.rikkahub.debug:attr/colorOnError = 0x7f0300ec
me.rerere.rikkahub.debug:string/setting_display_page_show_updates_title = 0x7f1101c4
me.rerere.rikkahub.debug:attr/errorIconTintMode = 0x7f0301a3
me.rerere.rikkahub.debug:attr/tabMaxWidth = 0x7f0303fc
me.rerere.rikkahub.debug:attr/showAsAction = 0x7f0303a6
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Inverse = 0x7f1201a3
me.rerere.rikkahub.debug:attr/tabInlineLabel = 0x7f0303fb
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501e7
me.rerere.rikkahub.debug:attr/shapeAppearanceMediumComponent = 0x7f03039f
me.rerere.rikkahub.debug:dimen/ucrop_height_wrapper_states = 0x7f06033e
me.rerere.rikkahub.debug:attr/tabIndicatorHeight = 0x7f0303fa
me.rerere.rikkahub.debug:attr/tabIndicatorFullWidth = 0x7f0303f8
me.rerere.rikkahub.debug:attr/tabIndicatorAnimationMode = 0x7f0303f6
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1202a1
me.rerere.rikkahub.debug:attr/tabIndicator = 0x7f0303f4
me.rerere.rikkahub.debug:dimen/abc_dialog_min_width_major = 0x7f060022
me.rerere.rikkahub.debug:string/assistant_page_title = 0x7f11004d
me.rerere.rikkahub.debug:color/m3_dynamic_dark_default_color_primary_text = 0x7f0500b1
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070013
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialDivider = 0x7f1203b6
me.rerere.rikkahub.debug:color/design_default_color_secondary = 0x7f050054
me.rerere.rikkahub.debug:styleable/AppBarLayout_Layout = 0x7f13000d
me.rerere.rikkahub.debug:attr/tabGravity = 0x7f0303f1
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f120256
me.rerere.rikkahub.debug:attr/titleTextAppearance = 0x7f03046c
me.rerere.rikkahub.debug:color/m3_fab_efab_background_color_selector = 0x7f0500bd
me.rerere.rikkahub.debug:style/Widget.Material3.NavigationView = 0x7f1203c4
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120097
me.rerere.rikkahub.debug:dimen/abc_control_corner_material = 0x7f060018
me.rerere.rikkahub.debug:style/Theme.Design = 0x7f120227
me.rerere.rikkahub.debug:attr/suggestionRowLayout = 0x7f0303e9
me.rerere.rikkahub.debug:macro/m3_comp_menu_container_color = 0x7f0c005d
me.rerere.rikkahub.debug:id/design_menu_item_text = 0x7f08008d
me.rerere.rikkahub.debug:attr/hintTextColor = 0x7f030211
me.rerere.rikkahub.debug:attr/subtitleTextStyle = 0x7f0303e5
me.rerere.rikkahub.debug:attr/multiChoiceItemLayout = 0x7f030331
me.rerere.rikkahub.debug:drawable/googleg_standard_color_18 = 0x7f07009a
me.rerere.rikkahub.debug:attr/motion_postLayoutCollision = 0x7f03032e
me.rerere.rikkahub.debug:attr/backgroundColor = 0x7f030046
me.rerere.rikkahub.debug:attr/itemStrokeColor = 0x7f030248
me.rerere.rikkahub.debug:attr/textAppearanceBody2 = 0x7f030413
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060226
me.rerere.rikkahub.debug:attr/customColorValue = 0x7f030151
me.rerere.rikkahub.debug:attr/subtitleCentered = 0x7f0303e2
me.rerere.rikkahub.debug:attr/dialogTheme = 0x7f030169
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000d
me.rerere.rikkahub.debug:dimen/mtrl_calendar_month_vertical_padding = 0x7f06029a
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0171
me.rerere.rikkahub.debug:color/leak_canary_gray = 0x7f050073
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Large = 0x7f120021
me.rerere.rikkahub.debug:attr/placeholderTextColor = 0x7f030363
me.rerere.rikkahub.debug:attr/subtitle = 0x7f0303e1
me.rerere.rikkahub.debug:attr/submitBackground = 0x7f0303e0
me.rerere.rikkahub.debug:drawable/leak_canary_info = 0x7f0700b4
me.rerere.rikkahub.debug:string/assistant_page_memory_desc = 0x7f11003a
me.rerere.rikkahub.debug:attr/chipIconTint = 0x7f0300b7
me.rerere.rikkahub.debug:id/leak_canary_import_heap_dump = 0x7f0800ff
me.rerere.rikkahub.debug:attr/expandedTitleTextAppearance = 0x7f0301b1
me.rerere.rikkahub.debug:attr/initialActivityCount = 0x7f03022c
me.rerere.rikkahub.debug:attr/subheaderTextAppearance = 0x7f0303df
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f120069
me.rerere.rikkahub.debug:attr/subheaderInsetStart = 0x7f0303de
me.rerere.rikkahub.debug:attr/subheaderInsetEnd = 0x7f0303dd
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501dd
me.rerere.rikkahub.debug:attr/windowActionBar = 0x7f0304b4
me.rerere.rikkahub.debug:attr/titleTextEllipsize = 0x7f03046e
me.rerere.rikkahub.debug:attr/shapeAppearanceCornerExtraLarge = 0x7f030399
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Inverse = 0x7f120020
me.rerere.rikkahub.debug:attr/subheaderColor = 0x7f0303dc
me.rerere.rikkahub.debug:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f060302
me.rerere.rikkahub.debug:attr/subMenuArrow = 0x7f0303db
me.rerere.rikkahub.debug:string/abc_menu_enter_shortcut_label = 0x7f11000b
me.rerere.rikkahub.debug:dimen/m3_appbar_size_compact = 0x7f0600b9
me.rerere.rikkahub.debug:id/material_timepicker_view = 0x7f08013c
me.rerere.rikkahub.debug:attr/ucrop_show_grid = 0x7f0304a2
me.rerere.rikkahub.debug:attr/colorOnSurfaceInverse = 0x7f0300f8
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060288
me.rerere.rikkahub.debug:styleable/NavigationBarView = 0x7f13006f
me.rerere.rikkahub.debug:dimen/m3_comp_snackbar_container_elevation = 0x7f060199
me.rerere.rikkahub.debug:attr/statusBarScrim = 0x7f0303d8
me.rerere.rikkahub.debug:attr/state_with_icon = 0x7f0303d5
me.rerere.rikkahub.debug:attr/state_liftable = 0x7f0303d3
me.rerere.rikkahub.debug:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090039
me.rerere.rikkahub.debug:attr/textLocale = 0x7f030442
me.rerere.rikkahub.debug:color/ucrop_color_inactive_controls_color = 0x7f050350
me.rerere.rikkahub.debug:id/state_saturation = 0x7f0801de
me.rerere.rikkahub.debug:attr/contentInsetRight = 0x7f030129
me.rerere.rikkahub.debug:color/material_grey_300 = 0x7f050299
me.rerere.rikkahub.debug:string/leak_canary_group_list_time_label = 0x7f1100c3
me.rerere.rikkahub.debug:animator/m3_extended_fab_show_motion_spec = 0x7f020013
me.rerere.rikkahub.debug:animator/design_appbar_state_list_animator = 0x7f020000
me.rerere.rikkahub.debug:attr/warmth = 0x7f0304ae
me.rerere.rikkahub.debug:attr/state_collapsible = 0x7f0303cf
me.rerere.rikkahub.debug:attr/constraintSet = 0x7f03011f
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary60 = 0x7f05010a
me.rerere.rikkahub.debug:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1201f6
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_group_name = 0x7f11019e
me.rerere.rikkahub.debug:attr/textAppearanceDisplayLarge = 0x7f030419
me.rerere.rikkahub.debug:attr/startIconTint = 0x7f0303cb
me.rerere.rikkahub.debug:string/model_list_select_model = 0x7f110168
me.rerere.rikkahub.debug:attr/startIconScaleType = 0x7f0303ca
me.rerere.rikkahub.debug:attr/homeAsUpIndicator = 0x7f030212
me.rerere.rikkahub.debug:attr/startIconMinSize = 0x7f0303c9
me.rerere.rikkahub.debug:string/editing = 0x7f11009d
me.rerere.rikkahub.debug:attr/svg = 0x7f0303ea
me.rerere.rikkahub.debug:styleable/FragmentContainerView = 0x7f130039
me.rerere.rikkahub.debug:attr/layout_constraintHeight_max = 0x7f03027b
me.rerere.rikkahub.debug:color/abc_tint_switch_track = 0x7f050018
me.rerere.rikkahub.debug:attr/staggered = 0x7f0303c4
me.rerere.rikkahub.debug:attr/buttonIcon = 0x7f03008c
me.rerere.rikkahub.debug:attr/splitTrack = 0x7f0303c1
me.rerere.rikkahub.debug:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f06010b
me.rerere.rikkahub.debug:id/open_search_view_background = 0x7f080175
me.rerere.rikkahub.debug:attr/spanCount = 0x7f0303bd
me.rerere.rikkahub.debug:attr/snackbarStyle = 0x7f0303bb
me.rerere.rikkahub.debug:color/foreground_material_dark = 0x7f050067
me.rerere.rikkahub.debug:string/translator_page_title = 0x7f110203
me.rerere.rikkahub.debug:dimen/abc_search_view_preferred_height = 0x7f060036
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060175
me.rerere.rikkahub.debug:attr/sliderStyle = 0x7f0303b9
me.rerere.rikkahub.debug:dimen/abc_text_size_body_2_material = 0x7f060040
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f120134
me.rerere.rikkahub.debug:id/asConfigured = 0x7f08004e
me.rerere.rikkahub.debug:attr/sizePercent = 0x7f0303b8
me.rerere.rikkahub.debug:styleable/MaterialButtonToggleGroup = 0x7f130051
me.rerere.rikkahub.debug:layout/abc_screen_content_include = 0x7f0b0014
me.rerere.rikkahub.debug:attr/singleSelection = 0x7f0303b7
me.rerere.rikkahub.debug:attr/cardForegroundColor = 0x7f030099
me.rerere.rikkahub.debug:attr/passwordToggleTintMode = 0x7f030358
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f12027e
me.rerere.rikkahub.debug:attr/singleChoiceItemLayout = 0x7f0303b5
me.rerere.rikkahub.debug:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0602a2
me.rerere.rikkahub.debug:attr/simpleItemSelectedColor = 0x7f0303b2
me.rerere.rikkahub.debug:color/switch_thumb_material_dark = 0x7f05033c
me.rerere.rikkahub.debug:attr/simpleItemLayout = 0x7f0303b1
me.rerere.rikkahub.debug:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f06030e
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_medium1 = 0x7f090018
me.rerere.rikkahub.debug:attr/customDimension = 0x7f030152
me.rerere.rikkahub.debug:attr/shrinkMotionSpec = 0x7f0303ae
me.rerere.rikkahub.debug:color/m3_ref_palette_error40 = 0x7f05012f
me.rerere.rikkahub.debug:attr/fastScrollVerticalTrackDrawable = 0x7f0301c8
me.rerere.rikkahub.debug:attr/voiceIcon = 0x7f0304ad
me.rerere.rikkahub.debug:anim/leak_canary_exit_forward = 0x7f010022
me.rerere.rikkahub.debug:dimen/mtrl_snackbar_background_corner_radius = 0x7f060301
me.rerere.rikkahub.debug:attr/showMotionSpec = 0x7f0303aa
me.rerere.rikkahub.debug:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060176
me.rerere.rikkahub.debug:style/Widget.Material3.Slider.Legacy.Label = 0x7f1203d7
me.rerere.rikkahub.debug:attr/showDividers = 0x7f0303a8
me.rerere.rikkahub.debug:color/m3_dark_highlighted_text = 0x7f0500ac
me.rerere.rikkahub.debug:attr/dataPattern = 0x7f030159
me.rerere.rikkahub.debug:dimen/abc_text_size_large_material = 0x7f060048
me.rerere.rikkahub.debug:attr/textAppearanceDisplaySmall = 0x7f03041b
me.rerere.rikkahub.debug:color/m3_tabs_ripple_color_secondary = 0x7f050232
me.rerere.rikkahub.debug:attr/jlmv_alignHorizontal = 0x7f030250
me.rerere.rikkahub.debug:color/accent_material_light = 0x7f05001a
me.rerere.rikkahub.debug:attr/shouldRemoveExpandedCorners = 0x7f0303a4
me.rerere.rikkahub.debug:id/hide_in_inspector_tag = 0x7f0800ca
me.rerere.rikkahub.debug:attr/chipSpacingHorizontal = 0x7f0300bc
me.rerere.rikkahub.debug:attr/shapeCornerFamily = 0x7f0303a2
me.rerere.rikkahub.debug:color/leak_canary_yellow = 0x7f050092
me.rerere.rikkahub.debug:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060298
me.rerere.rikkahub.debug:attr/shapeAppearanceCornerSmall = 0x7f03039d
me.rerere.rikkahub.debug:id/pin = 0x7f080191
me.rerere.rikkahub.debug:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600ee
me.rerere.rikkahub.debug:attr/flow_firstVerticalBias = 0x7f0301db
me.rerere.rikkahub.debug:attr/shapeAppearanceCornerLarge = 0x7f03039b
me.rerere.rikkahub.debug:dimen/notification_small_icon_size_as_large = 0x7f060327
me.rerere.rikkahub.debug:attr/customBoolean = 0x7f03014f
me.rerere.rikkahub.debug:attr/simpleItemSelectedRippleColor = 0x7f0303b3
me.rerere.rikkahub.debug:dimen/mtrl_card_dragged_z = 0x7f0602ae
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202cd
me.rerere.rikkahub.debug:attr/selectorSize = 0x7f030397
me.rerere.rikkahub.debug:color/m3_sys_color_tertiary_fixed_dim = 0x7f05022e
me.rerere.rikkahub.debug:attr/selectableItemBackgroundBorderless = 0x7f030395
me.rerere.rikkahub.debug:drawable/m3_bottom_sheet_drag_handle = 0x7f0700c0
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070026
me.rerere.rikkahub.debug:styleable/MenuView = 0x7f130061
me.rerere.rikkahub.debug:attr/selectableItemBackground = 0x7f030394
me.rerere.rikkahub.debug:layout/mtrl_picker_header_selection_text = 0x7f0b0069
me.rerere.rikkahub.debug:dimen/m3_toolbar_text_size_title = 0x7f060230
me.rerere.rikkahub.debug:layout/ucrop_layout_contrast_wheel = 0x7f0b0081
me.rerere.rikkahub.debug:color/design_bottom_navigation_shadow_color = 0x7f05003b
me.rerere.rikkahub.debug:style/TextAppearance.Design.Error = 0x7f1201d1
me.rerere.rikkahub.debug:anim/m3_bottom_sheet_slide_in = 0x7f010027
me.rerere.rikkahub.debug:attr/seekBarStyle = 0x7f030393
me.rerere.rikkahub.debug:attr/flow_lastHorizontalBias = 0x7f0301e1
me.rerere.rikkahub.debug:attr/scrimVisibleHeightTrigger = 0x7f03038e
me.rerere.rikkahub.debug:color/design_default_color_on_primary = 0x7f05004e
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1202e6
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f120239
me.rerere.rikkahub.debug:attr/scrimBackground = 0x7f03038d
me.rerere.rikkahub.debug:color/design_error = 0x7f050057
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level5 = 0x7f060205
me.rerere.rikkahub.debug:string/tts = 0x7f110205
me.rerere.rikkahub.debug:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700d5
me.rerere.rikkahub.debug:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060181
me.rerere.rikkahub.debug:attr/round = 0x7f030386
me.rerere.rikkahub.debug:color/abc_tint_seek_thumb = 0x7f050016
me.rerere.rikkahub.debug:attr/divider = 0x7f03016b
me.rerere.rikkahub.debug:attr/displayOptions = 0x7f03016a
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
me.rerere.rikkahub.debug:attr/layout_constraintHorizontal_chainStyle = 0x7f03027f
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_elevation = 0x7f0601da
me.rerere.rikkahub.debug:attr/removeEmbeddedFabElevation = 0x7f030382
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1201bc
me.rerere.rikkahub.debug:attr/region_widthMoreThan = 0x7f030381
me.rerere.rikkahub.debug:color/abc_search_url_text_selected = 0x7f050010
me.rerere.rikkahub.debug:attr/recyclerViewStyle = 0x7f03037d
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06011e
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f120063
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_light_pressed = 0x7f050039
me.rerere.rikkahub.debug:attr/trackDecorationTint = 0x7f030483
me.rerere.rikkahub.debug:attr/ratingBarStyleSmall = 0x7f03037c
me.rerere.rikkahub.debug:attr/ratingBarStyleIndicator = 0x7f03037b
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1202a8
me.rerere.rikkahub.debug:attr/ratingBarStyle = 0x7f03037a
me.rerere.rikkahub.debug:dimen/m3_carousel_small_item_size_min = 0x7f060102
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120445
me.rerere.rikkahub.debug:attr/queryPatterns = 0x7f030377
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1202f3
me.rerere.rikkahub.debug:id/honorRequest = 0x7f0800ce
me.rerere.rikkahub.debug:color/leak_canary_yellow_button_pressed = 0x7f050094
me.rerere.rikkahub.debug:dimen/material_emphasis_disabled_background = 0x7f060244
me.rerere.rikkahub.debug:attr/verticalOffsetWithText = 0x7f0304aa
me.rerere.rikkahub.debug:string/mtrl_picker_range_header_unselected = 0x7f110190
me.rerere.rikkahub.debug:attr/listLayout = 0x7f0302b0
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500ef
me.rerere.rikkahub.debug:color/m3_sys_color_light_inverse_on_surface = 0x7f050204
me.rerere.rikkahub.debug:attr/queryHint = 0x7f030376
me.rerere.rikkahub.debug:attr/actionBarDivider = 0x7f030001
me.rerere.rikkahub.debug:dimen/m3_badge_vertical_offset = 0x7f0600c6
me.rerere.rikkahub.debug:dimen/m3_comp_filled_button_container_elevation = 0x7f060132
me.rerere.rikkahub.debug:attr/ucrop_grid_row_count = 0x7f03049f
me.rerere.rikkahub.debug:attr/badgeTextColor = 0x7f030058
me.rerere.rikkahub.debug:attr/progressBarPadding = 0x7f030373
me.rerere.rikkahub.debug:attr/pressedTranslationZ = 0x7f030372
me.rerere.rikkahub.debug:color/material_grey_900 = 0x7f05029e
me.rerere.rikkahub.debug:bool/mtrl_btn_textappearance_all_caps = 0x7f04000b
me.rerere.rikkahub.debug:attr/prefixText = 0x7f03036e
me.rerere.rikkahub.debug:attr/popupMenuBackground = 0x7f03036a
me.rerere.rikkahub.debug:id/controls_wrapper = 0x7f08007b
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_end_padding = 0x7f0602ba
me.rerere.rikkahub.debug:attr/tickMarkTint = 0x7f030459
me.rerere.rikkahub.debug:attr/collapsingToolbarLayoutStyle = 0x7f0300de
me.rerere.rikkahub.debug:attr/pivotAnchor = 0x7f030360
me.rerere.rikkahub.debug:style/Widget.AppCompat.RatingBar.Small = 0x7f120329
me.rerere.rikkahub.debug:attr/perpendicularPath_percent = 0x7f03035f
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant70 = 0x7f050157
me.rerere.rikkahub.debug:attr/percentY = 0x7f03035e
me.rerere.rikkahub.debug:attr/percentWidth = 0x7f03035c
me.rerere.rikkahub.debug:attr/path_percent = 0x7f03035a
me.rerere.rikkahub.debug:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06024c
me.rerere.rikkahub.debug:string/material_minute_suffix = 0x7f110149
me.rerere.rikkahub.debug:id/withText = 0x7f080237
me.rerere.rikkahub.debug:attr/passwordToggleTint = 0x7f030357
me.rerere.rikkahub.debug:color/m3_text_button_foreground_color_selector = 0x7f050236
me.rerere.rikkahub.debug:attr/flow_horizontalAlign = 0x7f0301dd
me.rerere.rikkahub.debug:color/leak_canary_heap_image = 0x7f050083
me.rerere.rikkahub.debug:color/design_default_color_on_background = 0x7f05004c
me.rerere.rikkahub.debug:attr/passwordToggleDrawable = 0x7f030355
me.rerere.rikkahub.debug:dimen/abc_button_inset_vertical_material = 0x7f060013
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
me.rerere.rikkahub.debug:attr/panelMenuListWidth = 0x7f030353
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0164
me.rerere.rikkahub.debug:attr/buttonBarNeutralButtonStyle = 0x7f030087
me.rerere.rikkahub.debug:dimen/ucrop_size_wrapper_rotate_button = 0x7f060345
me.rerere.rikkahub.debug:style/Widget.AppCompat.ListView.DropDown = 0x7f120320
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Body2 = 0x7f12019b
me.rerere.rikkahub.debug:attr/popEnterAnim = 0x7f030365
me.rerere.rikkahub.debug:attr/customFloatValue = 0x7f030153
me.rerere.rikkahub.debug:layout/mtrl_calendar_day = 0x7f0b0057
me.rerere.rikkahub.debug:attr/panelMenuListTheme = 0x7f030352
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_path_checked = 0x7f11019f
me.rerere.rikkahub.debug:drawable/abc_list_selector_background_transition_holo_light = 0x7f070053
me.rerere.rikkahub.debug:attr/panelBackground = 0x7f030351
me.rerere.rikkahub.debug:attr/hintEnabled = 0x7f03020f
me.rerere.rikkahub.debug:color/m3_ref_palette_black = 0x7f0500d2
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Headline1 = 0x7f120203
me.rerere.rikkahub.debug:attr/paddingStartSystemWindowInsets = 0x7f03034e
me.rerere.rikkahub.debug:attr/trackColor = 0x7f03047e
me.rerere.rikkahub.debug:attr/paddingRightSystemWindowInsets = 0x7f03034c
me.rerere.rikkahub.debug:animator/mtrl_fab_show_motion_spec = 0x7f02001f
me.rerere.rikkahub.debug:attr/overlapAnchor = 0x7f030346
me.rerere.rikkahub.debug:attr/badgeWidePadding = 0x7f03005a
me.rerere.rikkahub.debug:dimen/m3_btn_icon_only_icon_padding = 0x7f0600e5
me.rerere.rikkahub.debug:attr/onShow = 0x7f030344
me.rerere.rikkahub.debug:id/fitToContents = 0x7f0800b8
me.rerere.rikkahub.debug:anim/abc_slide_in_bottom = 0x7f010006
me.rerere.rikkahub.debug:attr/actionModeSelectAllDrawable = 0x7f03001a
me.rerere.rikkahub.debug:attr/textInputFilledDenseStyle = 0x7f03043a
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.PopupMenu = 0x7f1200ea
me.rerere.rikkahub.debug:attr/endIconTintMode = 0x7f030198
me.rerere.rikkahub.debug:attr/onPositiveCross = 0x7f030343
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070012
me.rerere.rikkahub.debug:attr/statusBarForeground = 0x7f0303d7
me.rerere.rikkahub.debug:attr/onHide = 0x7f030341
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f12038e
me.rerere.rikkahub.debug:attr/layoutDuringTransition = 0x7f030263
me.rerere.rikkahub.debug:attr/graph = 0x7f030200
me.rerere.rikkahub.debug:string/mtrl_picker_range_header_only_end_selected = 0x7f11018c
me.rerere.rikkahub.debug:color/leak_canary_heap_load_class = 0x7f050087
me.rerere.rikkahub.debug:dimen/abc_switch_padding = 0x7f06003e
me.rerere.rikkahub.debug:attr/onCross = 0x7f030340
me.rerere.rikkahub.debug:id/dark = 0x7f080083
me.rerere.rikkahub.debug:attr/customStringValue = 0x7f030157
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Bridge = 0x7f120066
me.rerere.rikkahub.debug:macro/m3_comp_filled_button_label_text_type = 0x7f0c0045
me.rerere.rikkahub.debug:id/image_view_state_brightness = 0x7f0800d9
me.rerere.rikkahub.debug:attr/popupWindowStyle = 0x7f03036d
me.rerere.rikkahub.debug:color/mtrl_btn_text_color_selector = 0x7f0502f2
me.rerere.rikkahub.debug:attr/offsetAlignmentMode = 0x7f03033f
me.rerere.rikkahub.debug:style/Base.V14.Theme.Material3.Dark = 0x7f120089
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_default_width = 0x7f0601d9
me.rerere.rikkahub.debug:attr/nullable = 0x7f03033c
me.rerere.rikkahub.debug:style/Animation.Material3.SideSheetDialog.Right = 0x7f120009
me.rerere.rikkahub.debug:attr/nestedScrollFlags = 0x7f030339
me.rerere.rikkahub.debug:attr/ucrop_artv_ratio_title = 0x7f030494
me.rerere.rikkahub.debug:dimen/mtrl_slider_track_side_padding = 0x7f0602fe
me.rerere.rikkahub.debug:attr/navigationViewStyle = 0x7f030338
me.rerere.rikkahub.debug:attr/startIconDrawable = 0x7f0303c8
me.rerere.rikkahub.debug:bool/leak_canary_plumber_auto_install = 0x7f040008
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_dark = 0x7f070082
me.rerere.rikkahub.debug:color/material_personalized_color_surface_container_high = 0x7f0502cf
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120139
me.rerere.rikkahub.debug:attr/spinnerDropDownItemStyle = 0x7f0303bf
me.rerere.rikkahub.debug:attr/navigationIconTint = 0x7f030335
me.rerere.rikkahub.debug:attr/sideSheetModalStyle = 0x7f0303b0
me.rerere.rikkahub.debug:color/material_personalized_color_surface_inverse = 0x7f0502d4
me.rerere.rikkahub.debug:attr/navigationContentDescription = 0x7f030333
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f120402
me.rerere.rikkahub.debug:color/leak_canary_gray_light = 0x7f050079
me.rerere.rikkahub.debug:attr/backgroundTintMode = 0x7f03004f
me.rerere.rikkahub.debug:attr/cursorErrorColor = 0x7f03014d
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f12022e
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c0079
me.rerere.rikkahub.debug:color/error_color_material_light = 0x7f050066
me.rerere.rikkahub.debug:attr/motionTarget = 0x7f03032d
me.rerere.rikkahub.debug:dimen/m3_extended_fab_bottom_padding = 0x7f0601bf
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f12044b
me.rerere.rikkahub.debug:attr/buttonTintMode = 0x7f030095
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500da
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f120031
me.rerere.rikkahub.debug:string/template_percent = 0x7f1101f6
me.rerere.rikkahub.debug:id/accessibility_custom_action_30 = 0x7f080028
me.rerere.rikkahub.debug:attr/trackHeight = 0x7f030485
me.rerere.rikkahub.debug:attr/thumbIconTintMode = 0x7f03044c
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light = 0x7f120268
me.rerere.rikkahub.debug:color/m3_card_stroke_color = 0x7f0500a2
me.rerere.rikkahub.debug:attr/fabCradleRoundedCornerRadius = 0x7f0301c0
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f120024
me.rerere.rikkahub.debug:layout/material_timepicker_dialog = 0x7f0b004e
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary0 = 0x7f050103
me.rerere.rikkahub.debug:attr/autoCompleteTextViewStyle = 0x7f03003c
me.rerere.rikkahub.debug:color/material_dynamic_secondary99 = 0x7f05028a
me.rerere.rikkahub.debug:macro/m3_comp_dialog_headline_type = 0x7f0c0025
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_dark = 0x7f050030
me.rerere.rikkahub.debug:attr/errorTextColor = 0x7f0301a6
me.rerere.rikkahub.debug:color/design_default_color_primary = 0x7f050051
me.rerere.rikkahub.debug:attr/dropDownBackgroundTint = 0x7f030183
me.rerere.rikkahub.debug:attr/boxStrokeWidth = 0x7f030082
me.rerere.rikkahub.debug:color/abc_secondary_text_material_light = 0x7f050012
me.rerere.rikkahub.debug:attr/editTextStyle = 0x7f03018a
me.rerere.rikkahub.debug:drawable/ucrop_ic_saturation_unselected = 0x7f07011f
me.rerere.rikkahub.debug:attr/motionEasingLinearInterpolator = 0x7f030323
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060227
me.rerere.rikkahub.debug:color/m3_button_background_color_selector = 0x7f050099
me.rerere.rikkahub.debug:styleable/ExtendedFloatingActionButton = 0x7f130030
me.rerere.rikkahub.debug:id/uniform = 0x7f080229
me.rerere.rikkahub.debug:attr/tabSecondaryStyle = 0x7f030405
me.rerere.rikkahub.debug:attr/motionEasingEmphasizedInterpolator = 0x7f030321
me.rerere.rikkahub.debug:id/message = 0x7f080141
me.rerere.rikkahub.debug:drawable/$m3_avd_hide_password__0 = 0x7f070006
me.rerere.rikkahub.debug:dimen/compat_notification_large_icon_max_width = 0x7f06005c
me.rerere.rikkahub.debug:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f03031f
me.rerere.rikkahub.debug:id/open_search_view_content_container = 0x7f080177
me.rerere.rikkahub.debug:attr/motionEasingEmphasized = 0x7f03031e
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f120053
me.rerere.rikkahub.debug:attr/motionEasingAccelerated = 0x7f03031c
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0121
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060154
me.rerere.rikkahub.debug:attr/colorPrimaryDark = 0x7f030102
me.rerere.rikkahub.debug:attr/contentInsetEndWithActions = 0x7f030127
me.rerere.rikkahub.debug:attr/motionDurationShort3 = 0x7f03031a
me.rerere.rikkahub.debug:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
me.rerere.rikkahub.debug:attr/ucrop_grid_color = 0x7f03049d
me.rerere.rikkahub.debug:attr/srcCompat = 0x7f0303c2
me.rerere.rikkahub.debug:id/textEnd = 0x7f0801f9
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_light_normal_background = 0x7f070093
me.rerere.rikkahub.debug:attr/motionDurationShort2 = 0x7f030319
me.rerere.rikkahub.debug:color/design_default_color_on_surface = 0x7f050050
me.rerere.rikkahub.debug:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1200a7
me.rerere.rikkahub.debug:attr/hideOnContentScroll = 0x7f03020c
me.rerere.rikkahub.debug:attr/waveVariesBy = 0x7f0304b3
me.rerere.rikkahub.debug:attr/motionDurationMedium4 = 0x7f030317
me.rerere.rikkahub.debug:style/ucrop_ImageViewWidgetIcon = 0x7f120470
me.rerere.rikkahub.debug:attr/animationMode = 0x7f030033
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500ee
me.rerere.rikkahub.debug:attr/thumbColor = 0x7f030446
me.rerere.rikkahub.debug:color/material_timepicker_clock_text_color = 0x7f0502e9
me.rerere.rikkahub.debug:anim/mtrl_bottom_sheet_slide_in = 0x7f01002f
me.rerere.rikkahub.debug:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1203e8
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1203ad
me.rerere.rikkahub.debug:attr/tooltipStyle = 0x7f030477
me.rerere.rikkahub.debug:attr/errorIconDrawable = 0x7f0301a1
me.rerere.rikkahub.debug:attr/windowFixedHeightMajor = 0x7f0304b7
me.rerere.rikkahub.debug:id/scroll = 0x7f0801ad
me.rerere.rikkahub.debug:attr/motionDurationMedium2 = 0x7f030315
me.rerere.rikkahub.debug:string/m3c_date_range_input_title = 0x7f11011e
me.rerere.rikkahub.debug:attr/motionDurationLong2 = 0x7f030311
me.rerere.rikkahub.debug:attr/popExitAnim = 0x7f030366
me.rerere.rikkahub.debug:style/Widget.AppCompat.PopupWindow = 0x7f120324
me.rerere.rikkahub.debug:attr/number = 0x7f03033d
me.rerere.rikkahub.debug:attr/motionDurationExtraLong4 = 0x7f03030f
me.rerere.rikkahub.debug:attr/materialIconButtonOutlinedStyle = 0x7f0302e5
me.rerere.rikkahub.debug:id/design_menu_item_action_area_stub = 0x7f08008c
me.rerere.rikkahub.debug:attr/fabAnchorMode = 0x7f0301bd
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary50 = 0x7f050109
me.rerere.rikkahub.debug:style/ThemeOverlay.AppCompat.Dialog = 0x7f120281
me.rerere.rikkahub.debug:attr/motionDurationExtraLong3 = 0x7f03030e
me.rerere.rikkahub.debug:id/leak_canary_toast_text = 0x7f08011c
me.rerere.rikkahub.debug:attr/dialogCornerRadius = 0x7f030167
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500df
me.rerere.rikkahub.debug:attr/motionDurationExtraLong2 = 0x7f03030d
me.rerere.rikkahub.debug:id/radio = 0x7f08019a
me.rerere.rikkahub.debug:attr/cardCornerRadius = 0x7f030097
me.rerere.rikkahub.debug:color/material_grey_600 = 0x7f05029b
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500ed
me.rerere.rikkahub.debug:attr/forceApplySystemWindowInsetTop = 0x7f0301f9
me.rerere.rikkahub.debug:attr/motionDebug = 0x7f03030b
me.rerere.rikkahub.debug:attr/mock_showLabel = 0x7f03030a
me.rerere.rikkahub.debug:string/m3c_tooltip_long_press_label = 0x7f11013f
me.rerere.rikkahub.debug:attr/mock_labelBackgroundColor = 0x7f030307
me.rerere.rikkahub.debug:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f12010f
me.rerere.rikkahub.debug:attr/mock_label = 0x7f030306
me.rerere.rikkahub.debug:dimen/mtrl_textinput_box_corner_radius_small = 0x7f06030d
me.rerere.rikkahub.debug:attr/minTouchTargetSize = 0x7f030303
me.rerere.rikkahub.debug:attr/thumbIconSize = 0x7f03044a
me.rerere.rikkahub.debug:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601ba
me.rerere.rikkahub.debug:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f06030c
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501f6
me.rerere.rikkahub.debug:attr/mimeType = 0x7f0302ff
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1201e5
me.rerere.rikkahub.debug:color/material_personalized_color_on_secondary_container = 0x7f0502bb
me.rerere.rikkahub.debug:attr/menuGravity = 0x7f0302fe
me.rerere.rikkahub.debug:string/assistant_page_custom_headers = 0x7f11002c
me.rerere.rikkahub.debug:color/bright_foreground_disabled_material_dark = 0x7f050022
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001d
me.rerere.rikkahub.debug:attr/menu = 0x7f0302fc
me.rerere.rikkahub.debug:color/m3_card_foreground_color = 0x7f0500a0
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f12014e
me.rerere.rikkahub.debug:style/Base.V28.Theme.AppCompat.Light = 0x7f1200b9
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_text_padding = 0x7f060294
me.rerere.rikkahub.debug:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070017
me.rerere.rikkahub.debug:attr/maxWidth = 0x7f0302fa
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Chip.Filter = 0x7f120414
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f120154
me.rerere.rikkahub.debug:attr/maxNumber = 0x7f0302f8
me.rerere.rikkahub.debug:attr/maxImageSize = 0x7f0302f6
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602b6
me.rerere.rikkahub.debug:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601b8
me.rerere.rikkahub.debug:dimen/highlight_alpha_material_colored = 0x7f060093
me.rerere.rikkahub.debug:attr/maxHeight = 0x7f0302f5
me.rerere.rikkahub.debug:id/labeled = 0x7f0800e9
me.rerere.rikkahub.debug:dimen/material_clock_period_toggle_vertical_gap = 0x7f06023d
me.rerere.rikkahub.debug:attr/textAppearanceListItemSecondary = 0x7f03042b
me.rerere.rikkahub.debug:attr/errorTextAppearance = 0x7f0301a5
me.rerere.rikkahub.debug:attr/maxCharacterCount = 0x7f0302f4
me.rerere.rikkahub.debug:attr/cornerFamilyBottomLeft = 0x7f030139
me.rerere.rikkahub.debug:attr/maxActionInlineWidth = 0x7f0302f2
me.rerere.rikkahub.debug:attr/cardViewStyle = 0x7f03009d
me.rerere.rikkahub.debug:id/leak_canary_explorer_title = 0x7f0800fb
me.rerere.rikkahub.debug:drawable/m3_avd_show_password = 0x7f0700bf
me.rerere.rikkahub.debug:attr/materialTimePickerStyle = 0x7f0302ee
me.rerere.rikkahub.debug:layout/abc_tooltip = 0x7f0b001b
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
me.rerere.rikkahub.debug:color/dim_foreground_material_dark = 0x7f050063
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb = 0x7f0700eb
me.rerere.rikkahub.debug:attr/materialSearchViewToolbarStyle = 0x7f0302eb
me.rerere.rikkahub.debug:macro/m3_comp_elevated_card_container_color = 0x7f0c002a
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant10 = 0x7f050150
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f070085
me.rerere.rikkahub.debug:attr/materialSearchViewStyle = 0x7f0302e9
me.rerere.rikkahub.debug:attr/badgeWidth = 0x7f03005b
me.rerere.rikkahub.debug:dimen/leak_canary_connector_center_y = 0x7f06009d
me.rerere.rikkahub.debug:attr/materialSearchViewPrefixStyle = 0x7f0302e8
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_active_text_size = 0x7f060061
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f120133
me.rerere.rikkahub.debug:dimen/m3_comp_search_bar_container_elevation = 0x7f06017f
me.rerere.rikkahub.debug:attr/materialIconButtonFilledStyle = 0x7f0302e3
me.rerere.rikkahub.debug:color/secondary_text_default_material_dark = 0x7f050336
me.rerere.rikkahub.debug:attr/track = 0x7f03047d
me.rerere.rikkahub.debug:attr/closeIconEndPadding = 0x7f0300ce
me.rerere.rikkahub.debug:attr/materialCardViewFilledStyle = 0x7f0302db
me.rerere.rikkahub.debug:attr/errorAccessibilityLiveRegion = 0x7f03019e
me.rerere.rikkahub.debug:attr/materialCalendarTheme = 0x7f0302d8
me.rerere.rikkahub.debug:attr/tabSelectedTextAppearance = 0x7f030406
me.rerere.rikkahub.debug:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003a
me.rerere.rikkahub.debug:attr/materialCalendarMonth = 0x7f0302d5
me.rerere.rikkahub.debug:id/checked = 0x7f080066
me.rerere.rikkahub.debug:attr/materialCalendarHeaderToggleButton = 0x7f0302d4
me.rerere.rikkahub.debug:id/material_clock_hand = 0x7f08012d
me.rerere.rikkahub.debug:attr/materialCalendarHeaderLayout = 0x7f0302d1
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f120193
me.rerere.rikkahub.debug:color/material_personalized_color_on_error = 0x7f0502b6
me.rerere.rikkahub.debug:style/Widget.AppCompat.RatingBar = 0x7f120327
me.rerere.rikkahub.debug:dimen/design_fab_size_normal = 0x7f060072
me.rerere.rikkahub.debug:attr/badgeRadius = 0x7f030052
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001c
me.rerere.rikkahub.debug:attr/materialCalendarHeaderDivider = 0x7f0302d0
me.rerere.rikkahub.debug:integer/config_tooltipAnimTime = 0x7f090005
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_container_low = 0x7f05021d
me.rerere.rikkahub.debug:string/dropdown_menu = 0x7f11009b
me.rerere.rikkahub.debug:drawable/$m3_avd_hide_password__1 = 0x7f070007
me.rerere.rikkahub.debug:attr/materialCalendarHeaderConfirmButton = 0x7f0302cf
me.rerere.rikkahub.debug:id/action_bar_root = 0x7f080033
me.rerere.rikkahub.debug:attr/popUpToInclusive = 0x7f030368
me.rerere.rikkahub.debug:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0601ad
me.rerere.rikkahub.debug:string/abc_menu_shift_shortcut_label = 0x7f11000e
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral12 = 0x7f05013a
me.rerere.rikkahub.debug:attr/switchMinWidth = 0x7f0303eb
me.rerere.rikkahub.debug:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070039
me.rerere.rikkahub.debug:attr/materialButtonStyle = 0x7f0302c9
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015b
me.rerere.rikkahub.debug:attr/materialButtonOutlinedStyle = 0x7f0302c8
me.rerere.rikkahub.debug:attr/contentPaddingEnd = 0x7f03012e
me.rerere.rikkahub.debug:attr/backgroundInsetStart = 0x7f030049
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.Chip = 0x7f120113
me.rerere.rikkahub.debug:attr/materialAlertDialogTitlePanelStyle = 0x7f0302c6
me.rerere.rikkahub.debug:color/abc_primary_text_material_dark = 0x7f05000b
me.rerere.rikkahub.debug:style/Widget.Material3.Button.IconButton = 0x7f120359
me.rerere.rikkahub.debug:attr/colorSecondaryFixedDim = 0x7f03010c
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00aa
me.rerere.rikkahub.debug:color/design_dark_default_color_primary_dark = 0x7f050045
me.rerere.rikkahub.debug:attr/itemPadding = 0x7f03023c
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009b
me.rerere.rikkahub.debug:drawable/abc_list_divider_mtrl_alpha = 0x7f07004d
me.rerere.rikkahub.debug:style/Widget.AppCompat.Toolbar = 0x7f120334
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1200e1
me.rerere.rikkahub.debug:string/assistant_page_no_system_prompt = 0x7f11003c
me.rerere.rikkahub.debug:attr/badgeShapeAppearanceOverlay = 0x7f030054
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_icon_margin = 0x7f0600b1
me.rerere.rikkahub.debug:attr/logoDescription = 0x7f0302bc
me.rerere.rikkahub.debug:string/leak_canary_notification_retained_visible = 0x7f1100e0
me.rerere.rikkahub.debug:attr/contentDescription = 0x7f030125
me.rerere.rikkahub.debug:color/m3_chip_stroke_color = 0x7f0500a8
me.rerere.rikkahub.debug:attr/logoAdjustViewBounds = 0x7f0302bb
me.rerere.rikkahub.debug:style/Widget.Material3.CheckedTextView = 0x7f12036b
me.rerere.rikkahub.debug:string/common_google_play_services_unsupported_text = 0x7f11008e
me.rerere.rikkahub.debug:color/material_dynamic_color_light_error_container = 0x7f050254
me.rerere.rikkahub.debug:attr/autoSizeStepGranularity = 0x7f030041
me.rerere.rikkahub.debug:styleable/Transform = 0x7f130098
me.rerere.rikkahub.debug:attr/backHandlingEnabled = 0x7f030044
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f120400
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0501c3
me.rerere.rikkahub.debug:color/m3_button_foreground_color_selector = 0x7f05009a
me.rerere.rikkahub.debug:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
me.rerere.rikkahub.debug:macro/m3_comp_badge_color = 0x7f0c0002
me.rerere.rikkahub.debug:attr/layout_constraintWidth_default = 0x7f030290
me.rerere.rikkahub.debug:attr/listPreferredItemPaddingRight = 0x7f0302b8
me.rerere.rikkahub.debug:styleable/MaterialShape = 0x7f130059
me.rerere.rikkahub.debug:attr/listPreferredItemPaddingLeft = 0x7f0302b7
me.rerere.rikkahub.debug:attr/listPreferredItemHeightLarge = 0x7f0302b4
me.rerere.rikkahub.debug:color/purple_700 = 0x7f05032f
me.rerere.rikkahub.debug:color/design_default_color_primary_dark = 0x7f050052
me.rerere.rikkahub.debug:color/white = 0x7f05035f
me.rerere.rikkahub.debug:attr/colorPrimaryContainer = 0x7f030101
me.rerere.rikkahub.debug:attr/listItemLayout = 0x7f0302af
me.rerere.rikkahub.debug:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700ed
me.rerere.rikkahub.debug:attr/itemTextAppearanceInactive = 0x7f03024d
me.rerere.rikkahub.debug:dimen/ucrop_default_crop_rect_min_size = 0x7f060339
me.rerere.rikkahub.debug:color/leak_canary_heap_other = 0x7f05008a
me.rerere.rikkahub.debug:id/material_minute_text_input = 0x7f080135
me.rerere.rikkahub.debug:color/material_harmonized_color_on_error = 0x7f0502a1
me.rerere.rikkahub.debug:string/material_timepicker_clock_mode_description = 0x7f110153
me.rerere.rikkahub.debug:attr/titleMarginBottom = 0x7f030466
me.rerere.rikkahub.debug:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f110122
me.rerere.rikkahub.debug:attr/listChoiceBackgroundIndicator = 0x7f0302ab
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f120138
me.rerere.rikkahub.debug:color/m3_slider_thumb_color_legacy = 0x7f05018c
me.rerere.rikkahub.debug:string/m3_ref_typeface_brand_medium = 0x7f1100f2
me.rerere.rikkahub.debug:attr/checkMarkCompat = 0x7f0300a1
me.rerere.rikkahub.debug:attr/linearProgressIndicatorStyle = 0x7f0302aa
me.rerere.rikkahub.debug:attr/lineSpacing = 0x7f0302a9
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary20 = 0x7f050179
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500e1
me.rerere.rikkahub.debug:attr/trackInsideCornerSize = 0x7f030486
me.rerere.rikkahub.debug:string/m3c_snackbar_pane_title = 0x7f11012d
me.rerere.rikkahub.debug:id/month_navigation_fragment_toggle = 0x7f080146
me.rerere.rikkahub.debug:attr/buttonCompat = 0x7f03008a
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant99 = 0x7f05015b
me.rerere.rikkahub.debug:attr/defaultScrollFlagsEnabled = 0x7f030161
me.rerere.rikkahub.debug:attr/materialAlertDialogBodyTextStyle = 0x7f0302c2
me.rerere.rikkahub.debug:layout/material_clock_display = 0x7f0b0043
me.rerere.rikkahub.debug:color/ucrop_color_widget_rotate_angle = 0x7f050359
me.rerere.rikkahub.debug:layout/select_dialog_multichoice_material = 0x7f0b0079
me.rerere.rikkahub.debug:id/accessibility_custom_action_1 = 0x7f080011
me.rerere.rikkahub.debug:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301ca
me.rerere.rikkahub.debug:attr/liftOnScrollColor = 0x7f0302a5
me.rerere.rikkahub.debug:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.RatingBar = 0x7f1200ef
me.rerere.rikkahub.debug:layout/ucrop_fragment_photobox = 0x7f0b007f
me.rerere.rikkahub.debug:attr/liftOnScroll = 0x7f0302a4
me.rerere.rikkahub.debug:attr/trackTintMode = 0x7f03048a
me.rerere.rikkahub.debug:attr/leak_canary_plus_color = 0x7f0302a3
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1200cc
me.rerere.rikkahub.debug:string/assistant_page_delete_header = 0x7f110030
me.rerere.rikkahub.debug:attr/itemShapeFillColor = 0x7f030242
me.rerere.rikkahub.debug:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f06025e
me.rerere.rikkahub.debug:attr/expandedTitleMarginBottom = 0x7f0301ad
me.rerere.rikkahub.debug:attr/layout_scrollEffect = 0x7f0302a0
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1202ef
me.rerere.rikkahub.debug:color/design_dark_default_color_background = 0x7f05003d
me.rerere.rikkahub.debug:string/menu_page_night_greeting = 0x7f110160
me.rerere.rikkahub.debug:attr/layout_goneMarginTop = 0x7f03029c
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_light_disabled = 0x7f050037
me.rerere.rikkahub.debug:dimen/m3_alert_dialog_corner_size = 0x7f0600af
me.rerere.rikkahub.debug:layout/material_chip_input_combo = 0x7f0b0042
me.rerere.rikkahub.debug:attr/layout_goneMarginRight = 0x7f03029a
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents = 0x7f120065
me.rerere.rikkahub.debug:color/leak_canary_yellow_button = 0x7f050093
me.rerere.rikkahub.debug:bool/workmanager_test_configuration = 0x7f04000c
me.rerere.rikkahub.debug:string/m3_exceed_max_badge_text_suffix = 0x7f1100f1
me.rerere.rikkahub.debug:attr/shapeAppearanceLargeComponent = 0x7f03039e
me.rerere.rikkahub.debug:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06025a
me.rerere.rikkahub.debug:color/material_dynamic_secondary20 = 0x7f050281
me.rerere.rikkahub.debug:string/mtrl_picker_a11y_next_month = 0x7f110179
me.rerere.rikkahub.debug:id/action_menu_divider = 0x7f08003b
me.rerere.rikkahub.debug:attr/cornerSizeTopLeft = 0x7f030141
me.rerere.rikkahub.debug:string/mtrl_picker_invalid_format_use = 0x7f110187
me.rerere.rikkahub.debug:attr/itemIconPadding = 0x7f030237
me.rerere.rikkahub.debug:color/material_personalized__highlighted_text_inverse = 0x7f0502ae
me.rerere.rikkahub.debug:attr/itemShapeAppearanceOverlay = 0x7f030241
me.rerere.rikkahub.debug:attr/dayInvalidStyle = 0x7f03015a
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f120261
me.rerere.rikkahub.debug:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f7
me.rerere.rikkahub.debug:attr/statusBarBackground = 0x7f0303d6
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_heap_dumps = 0x7f080106
me.rerere.rikkahub.debug:attr/layout_goneMarginBottom = 0x7f030297
me.rerere.rikkahub.debug:attr/tabSelectedTextColor = 0x7f030407
me.rerere.rikkahub.debug:anim/leak_canary_enter_backward = 0x7f01001e
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level2 = 0x7f060202
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060161
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500d8
me.rerere.rikkahub.debug:id/snackbar_text = 0x7f0801c8
me.rerere.rikkahub.debug:attr/actionBarPopupTheme = 0x7f030003
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral24 = 0x7f05013e
me.rerere.rikkahub.debug:attr/colorPrimaryFixed = 0x7f030103
me.rerere.rikkahub.debug:attr/layout_constraintVertical_weight = 0x7f03028f
me.rerere.rikkahub.debug:attr/flow_verticalGap = 0x7f0301e9
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f12024c
me.rerere.rikkahub.debug:dimen/m3_card_disabled_z = 0x7f0600f4
me.rerere.rikkahub.debug:attr/layout_constraintTag = 0x7f030289
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1200cf
me.rerere.rikkahub.debug:anim/design_bottom_sheet_slide_in = 0x7f010018
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070023
me.rerere.rikkahub.debug:color/mtrl_textinput_disabled_color = 0x7f05031f
me.rerere.rikkahub.debug:string/setting_page_default_model_desc = 0x7f1101d8
me.rerere.rikkahub.debug:color/material_personalized_color_surface = 0x7f0502cc
me.rerere.rikkahub.debug:color/leak_canary_heap_long_array = 0x7f050088
me.rerere.rikkahub.debug:attr/layout_constraintRight_toLeftOf = 0x7f030285
me.rerere.rikkahub.debug:attr/layout_constraintLeft_toLeftOf = 0x7f030282
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f12038b
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0163
me.rerere.rikkahub.debug:dimen/mtrl_slider_thumb_elevation = 0x7f0602f9
me.rerere.rikkahub.debug:color/material_blue_grey_800 = 0x7f050248
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0071
me.rerere.rikkahub.debug:attr/borderlessButtonStyle = 0x7f030072
me.rerere.rikkahub.debug:attr/constraintSetEnd = 0x7f030120
me.rerere.rikkahub.debug:attr/layout_constraintHeight_min = 0x7f03027c
me.rerere.rikkahub.debug:dimen/design_tab_text_size = 0x7f06008b
me.rerere.rikkahub.debug:id/navigation_bar_item_large_label_view = 0x7f080168
me.rerere.rikkahub.debug:attr/useMaterialThemeColors = 0x7f0304a7
me.rerere.rikkahub.debug:attr/mock_showDiagonals = 0x7f030309
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016a
me.rerere.rikkahub.debug:attr/materialTimePickerTheme = 0x7f0302ef
me.rerere.rikkahub.debug:dimen/design_navigation_item_vertical_padding = 0x7f06007a
me.rerere.rikkahub.debug:attr/checkMarkTint = 0x7f0300a2
me.rerere.rikkahub.debug:attr/layout_constraintGuide_end = 0x7f030278
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_outline = 0x7f0501c2
me.rerere.rikkahub.debug:attr/listDividerAlertDialog = 0x7f0302ae
me.rerere.rikkahub.debug:attr/showDelay = 0x7f0303a7
me.rerere.rikkahub.debug:attr/layout_constraintCircleRadius = 0x7f030273
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionBar = 0x7f1202ed
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06018e
me.rerere.rikkahub.debug:attr/titleCentered = 0x7f030462
me.rerere.rikkahub.debug:attr/itemBackground = 0x7f030233
me.rerere.rikkahub.debug:dimen/design_navigation_icon_padding = 0x7f060076
me.rerere.rikkahub.debug:color/mtrl_text_btn_text_color_selector = 0x7f05031d
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
me.rerere.rikkahub.debug:color/material_dynamic_secondary70 = 0x7f050286
me.rerere.rikkahub.debug:attr/layout_constraintCircleAngle = 0x7f030272
me.rerere.rikkahub.debug:attr/layout_constraintBottom_toBottomOf = 0x7f03026f
me.rerere.rikkahub.debug:attr/checkedState = 0x7f0300ae
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_year_selection = 0x7f110119
me.rerere.rikkahub.debug:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300db
me.rerere.rikkahub.debug:attr/layout_constraintBaseline_toBaselineOf = 0x7f03026d
me.rerere.rikkahub.debug:drawable/avd_hide_password = 0x7f070077
me.rerere.rikkahub.debug:color/androidx_core_ripple_material_light = 0x7f05001b
me.rerere.rikkahub.debug:styleable/CoordinatorLayout_Layout = 0x7f13002c
me.rerere.rikkahub.debug:string/call_notification_ongoing_text = 0x7f110060
me.rerere.rikkahub.debug:attr/trackStopIndicatorSize = 0x7f030487
me.rerere.rikkahub.debug:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06018f
me.rerere.rikkahub.debug:attr/layout_anchor = 0x7f030265
me.rerere.rikkahub.debug:string/leak_canary_failure_clipdata_label = 0x7f1100be
me.rerere.rikkahub.debug:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
me.rerere.rikkahub.debug:attr/layoutManager = 0x7f030264
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601b7
me.rerere.rikkahub.debug:attr/textAppearanceListItemSmall = 0x7f03042c
me.rerere.rikkahub.debug:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f0601a3
me.rerere.rikkahub.debug:attr/contentInsetLeft = 0x7f030128
me.rerere.rikkahub.debug:string/mtrl_picker_a11y_prev_month = 0x7f11017a
me.rerere.rikkahub.debug:attr/ucrop_circle_dimmed_layer = 0x7f030499
me.rerere.rikkahub.debug:attr/layoutDescription = 0x7f030262
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary60 = 0x7f050170
me.rerere.rikkahub.debug:drawable/abc_seekbar_tick_mark_material = 0x7f070063
me.rerere.rikkahub.debug:dimen/ucrop_width_horizontal_wheel_progress_line = 0x7f060348
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_container_elevation = 0x7f060125
me.rerere.rikkahub.debug:attr/popupMenuStyle = 0x7f03036b
me.rerere.rikkahub.debug:attr/lastItemDecorated = 0x7f03025f
me.rerere.rikkahub.debug:attr/labelVisibilityMode = 0x7f03025c
me.rerere.rikkahub.debug:attr/fontProviderFetchStrategy = 0x7f0301f1
me.rerere.rikkahub.debug:attr/tabPaddingBottom = 0x7f030400
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_input_text_type = 0x7f0c00ea
me.rerere.rikkahub.debug:attr/layout_constraintLeft_creator = 0x7f030281
me.rerere.rikkahub.debug:attr/labelBehavior = 0x7f03025a
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0089
me.rerere.rikkahub.debug:drawable/abc_list_selector_disabled_holo_dark = 0x7f070054
me.rerere.rikkahub.debug:attr/layout_dodgeInsetEdges = 0x7f030294
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500f9
me.rerere.rikkahub.debug:color/material_personalized_color_primary_container = 0x7f0502c4
me.rerere.rikkahub.debug:id/compatible = 0x7f080070
me.rerere.rikkahub.debug:color/abc_tint_btn_checkable = 0x7f050013
me.rerere.rikkahub.debug:dimen/mtrl_switch_thumb_icon_size = 0x7f060308
me.rerere.rikkahub.debug:color/switch_thumb_material_light = 0x7f05033d
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f060168
me.rerere.rikkahub.debug:attr/dividerPadding = 0x7f030170
me.rerere.rikkahub.debug:attr/chipIcon = 0x7f0300b4
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f120088
me.rerere.rikkahub.debug:integer/design_snackbar_text_max_lines = 0x7f090006
me.rerere.rikkahub.debug:dimen/mtrl_slider_thumb_radius = 0x7f0602fa
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1203f3
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.SearchView = 0x7f12031b
me.rerere.rikkahub.debug:id/visible = 0x7f080234
me.rerere.rikkahub.debug:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
me.rerere.rikkahub.debug:attr/jlmv_textSize = 0x7f030255
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_light_normal = 0x7f070089
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070024
me.rerere.rikkahub.debug:attr/flow_maxElementsWrap = 0x7f0301e5
me.rerere.rikkahub.debug:anim/abc_tooltip_enter = 0x7f01000a
me.rerere.rikkahub.debug:attr/layout_constrainedWidth = 0x7f03026b
me.rerere.rikkahub.debug:attr/dayStyle = 0x7f03015c
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f06020f
me.rerere.rikkahub.debug:id/state_sharpness = 0x7f0801e0
me.rerere.rikkahub.debug:attr/contentInsetStart = 0x7f03012a
me.rerere.rikkahub.debug:attr/paddingLeftSystemWindowInsets = 0x7f03034b
me.rerere.rikkahub.debug:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0152
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary50 = 0x7f05017c
me.rerere.rikkahub.debug:string/common_google_play_services_notification_channel_name = 0x7f11008b
me.rerere.rikkahub.debug:attr/fastScrollHorizontalThumbDrawable = 0x7f0301c5
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060128
me.rerere.rikkahub.debug:attr/jlmv_latex = 0x7f030253
me.rerere.rikkahub.debug:id/decor_content_parent = 0x7f080087
me.rerere.rikkahub.debug:color/material_slider_active_track_color = 0x7f0502e2
me.rerere.rikkahub.debug:attr/simpleItems = 0x7f0303b4
me.rerere.rikkahub.debug:attr/shapeAppearanceCornerExtraSmall = 0x7f03039a
me.rerere.rikkahub.debug:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
me.rerere.rikkahub.debug:attr/itemVerticalPadding = 0x7f03024f
me.rerere.rikkahub.debug:attr/itemTextAppearanceActiveBoldEnabled = 0x7f03024c
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral100 = 0x7f050139
me.rerere.rikkahub.debug:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
me.rerere.rikkahub.debug:attr/itemTextAppearance = 0x7f03024a
me.rerere.rikkahub.debug:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1201e2
me.rerere.rikkahub.debug:id/listMode = 0x7f080124
me.rerere.rikkahub.debug:attr/nestedScrollViewStyle = 0x7f03033a
me.rerere.rikkahub.debug:attr/behavior_hideable = 0x7f03006b
me.rerere.rikkahub.debug:string/assistant_page_manage_memory_title = 0x7f110037
me.rerere.rikkahub.debug:attr/itemShapeInsetTop = 0x7f030246
me.rerere.rikkahub.debug:attr/itemShapeInsetStart = 0x7f030245
me.rerere.rikkahub.debug:dimen/m3_comp_input_chip_container_height = 0x7f060141
me.rerere.rikkahub.debug:attr/chipIconSize = 0x7f0300b6
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060147
me.rerere.rikkahub.debug:attr/itemIconTint = 0x7f030239
me.rerere.rikkahub.debug:attr/currentState = 0x7f03014b
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button.Small = 0x7f1200d2
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c00ff
me.rerere.rikkahub.debug:attr/itemIconSize = 0x7f030238
me.rerere.rikkahub.debug:color/leak_canary_heap_object_array = 0x7f050089
me.rerere.rikkahub.debug:id/transition_transform = 0x7f080223
me.rerere.rikkahub.debug:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0601ab
me.rerere.rikkahub.debug:color/mtrl_card_view_ripple = 0x7f0502f7
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0072
me.rerere.rikkahub.debug:attr/indicatorTrackGapSize = 0x7f03022b
me.rerere.rikkahub.debug:attr/tabIndicatorAnimationDuration = 0x7f0303f5
me.rerere.rikkahub.debug:attr/materialCalendarHeaderTitle = 0x7f0302d3
me.rerere.rikkahub.debug:attr/marginTopSystemWindowInsets = 0x7f0302c1
me.rerere.rikkahub.debug:integer/mtrl_chip_anim_duration = 0x7f090037
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_icon_size = 0x7f060064
me.rerere.rikkahub.debug:attr/clickAction = 0x7f0300c7
me.rerere.rikkahub.debug:attr/backgroundInsetEnd = 0x7f030048
me.rerere.rikkahub.debug:attr/tabBackground = 0x7f0303ef
me.rerere.rikkahub.debug:attr/indicatorInset = 0x7f030229
me.rerere.rikkahub.debug:dimen/abc_action_button_min_height_material = 0x7f06000d
me.rerere.rikkahub.debug:attr/indicatorDirectionLinear = 0x7f030228
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral0 = 0x7f0500d3
me.rerere.rikkahub.debug:xml/leak_canary_file_paths = 0x7f140004
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Toolbar = 0x7f120467
me.rerere.rikkahub.debug:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0056
me.rerere.rikkahub.debug:attr/indeterminateProgressStyle = 0x7f030225
me.rerere.rikkahub.debug:string/leak_canary_notification_analysing = 0x7f1100d5
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_divider_thickness = 0x7f060290
me.rerere.rikkahub.debug:attr/imageAspectRatioAdjust = 0x7f030221
me.rerere.rikkahub.debug:attr/iconifiedByDefault = 0x7f03021f
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary80 = 0x7f05017f
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c007f
me.rerere.rikkahub.debug:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
me.rerere.rikkahub.debug:attr/buttonBarButtonStyle = 0x7f030085
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500fc
me.rerere.rikkahub.debug:attr/materialSwitchStyle = 0x7f0302ec
me.rerere.rikkahub.debug:id/dragLeft = 0x7f080098
me.rerere.rikkahub.debug:attr/tickColorActive = 0x7f030456
me.rerere.rikkahub.debug:string/leak_canary_notification_retained_title = 0x7f1100df
me.rerere.rikkahub.debug:dimen/m3_badge_with_text_offset = 0x7f0600c8
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ButtonBar = 0x7f1200d3
me.rerere.rikkahub.debug:animator/m3_btn_state_list_anim = 0x7f02000b
me.rerere.rikkahub.debug:attr/drawerArrowStyle = 0x7f030180
me.rerere.rikkahub.debug:attr/textAppearanceBodySmall = 0x7f030416
me.rerere.rikkahub.debug:color/m3_sys_color_dark_inverse_primary = 0x7f050193
me.rerere.rikkahub.debug:attr/passwordToggleContentDescription = 0x7f030354
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ChipGroup = 0x7f120415
me.rerere.rikkahub.debug:string/mtrl_picker_invalid_range = 0x7f110188
me.rerere.rikkahub.debug:id/ALT = 0x7f080000
me.rerere.rikkahub.debug:attr/iconTint = 0x7f03021d
me.rerere.rikkahub.debug:drawable/abc_control_background_material = 0x7f07003a
me.rerere.rikkahub.debug:attr/layout_constraintHeight_default = 0x7f03027a
me.rerere.rikkahub.debug:attr/hideAnimationBehavior = 0x7f030209
me.rerere.rikkahub.debug:id/parent_matrix = 0x7f08018a
me.rerere.rikkahub.debug:attr/framePosition = 0x7f0301fc
me.rerere.rikkahub.debug:color/m3_sys_color_light_tertiary_container = 0x7f050222
me.rerere.rikkahub.debug:drawable/abc_cab_background_top_material = 0x7f070038
me.rerere.rikkahub.debug:attr/iconGravity = 0x7f030219
me.rerere.rikkahub.debug:attr/iconEndPadding = 0x7f030218
me.rerere.rikkahub.debug:anim/abc_fade_in = 0x7f010000
me.rerere.rikkahub.debug:attr/endIconDrawable = 0x7f030193
me.rerere.rikkahub.debug:attr/checkedButton = 0x7f0300a5
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1202be
me.rerere.rikkahub.debug:attr/icon = 0x7f030217
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f12012e
me.rerere.rikkahub.debug:attr/switchTextAppearance = 0x7f0303ee
me.rerere.rikkahub.debug:attr/actionBarSize = 0x7f030004
me.rerere.rikkahub.debug:attr/headerLayout = 0x7f030203
me.rerere.rikkahub.debug:styleable/KeyFramesVelocity = 0x7f130043
me.rerere.rikkahub.debug:attr/horizontalOffsetWithText = 0x7f030215
me.rerere.rikkahub.debug:attr/horizontalOffset = 0x7f030214
me.rerere.rikkahub.debug:anim/leak_canary_enter_alpha = 0x7f01001d
me.rerere.rikkahub.debug:string/m3c_time_picker_hour = 0x7f110132
me.rerere.rikkahub.debug:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602d0
me.rerere.rikkahub.debug:dimen/abc_text_size_subhead_material = 0x7f06004d
me.rerere.rikkahub.debug:attr/actionModeCloseButtonStyle = 0x7f030012
me.rerere.rikkahub.debug:layout/leak_canary_heap_dumps_screen = 0x7f0b0032
me.rerere.rikkahub.debug:attr/hintAnimationEnabled = 0x7f03020e
me.rerere.rikkahub.debug:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
me.rerere.rikkahub.debug:attr/hideMotionSpec = 0x7f03020a
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Chip = 0x7f120202
me.rerere.rikkahub.debug:attr/layout_constrainedHeight = 0x7f03026a
me.rerere.rikkahub.debug:attr/helperTextTextAppearance = 0x7f030207
me.rerere.rikkahub.debug:attr/goIcon = 0x7f0301ff
me.rerere.rikkahub.debug:attr/gestureInsetBottomIgnored = 0x7f0301fe
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Medium = 0x7f120025
me.rerere.rikkahub.debug:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06025b
me.rerere.rikkahub.debug:attr/gapBetweenBars = 0x7f0301fd
me.rerere.rikkahub.debug:attr/forceDefaultNavigationOnClickListener = 0x7f0301fa
me.rerere.rikkahub.debug:attr/fontWeight = 0x7f0301f8
me.rerere.rikkahub.debug:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060120
me.rerere.rikkahub.debug:attr/flow_verticalStyle = 0x7f0301ea
me.rerere.rikkahub.debug:anim/ucrop_loader_circle_path = 0x7f010032
me.rerere.rikkahub.debug:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f120236
me.rerere.rikkahub.debug:attr/tickMarkTintMode = 0x7f03045a
me.rerere.rikkahub.debug:id/with_icon = 0x7f080238
me.rerere.rikkahub.debug:attr/itemShapeInsetBottom = 0x7f030243
me.rerere.rikkahub.debug:attr/fontVariationSettings = 0x7f0301f7
me.rerere.rikkahub.debug:dimen/notification_large_icon_height = 0x7f060320
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents = 0x7f120180
me.rerere.rikkahub.debug:attr/fontStyle = 0x7f0301f6
me.rerere.rikkahub.debug:style/Widget.AppCompat.Light.ActionBar = 0x7f120307
me.rerere.rikkahub.debug:color/m3_elevated_chip_background_color = 0x7f0500bc
me.rerere.rikkahub.debug:attr/fontProviderSystemFontFamily = 0x7f0301f5
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_size_small = 0x7f0602ec
me.rerere.rikkahub.debug:dimen/m3_btn_padding_top = 0x7f0600ec
me.rerere.rikkahub.debug:attr/dialogPreferredPadding = 0x7f030168
me.rerere.rikkahub.debug:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601e9
me.rerere.rikkahub.debug:attr/defaultDuration = 0x7f03015e
me.rerere.rikkahub.debug:attr/materialThemeOverlay = 0x7f0302ed
me.rerere.rikkahub.debug:attr/compatShadowEnabled = 0x7f03011e
me.rerere.rikkahub.debug:color/mtrl_btn_ripple_color = 0x7f0502ed
me.rerere.rikkahub.debug:attr/deltaPolarRadius = 0x7f030164
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f120258
me.rerere.rikkahub.debug:attr/fontProviderFallbackQuery = 0x7f0301f0
me.rerere.rikkahub.debug:string/click_to_get_api_key = 0x7f11007f
me.rerere.rikkahub.debug:id/autoComplete = 0x7f080051
me.rerere.rikkahub.debug:attr/actionMenuTextAppearance = 0x7f03000f
me.rerere.rikkahub.debug:attr/layout_constraintLeft_toRightOf = 0x7f030283
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0167
me.rerere.rikkahub.debug:attr/colorSecondaryFixed = 0x7f03010b
me.rerere.rikkahub.debug:attr/textAppearanceListItem = 0x7f03042a
me.rerere.rikkahub.debug:attr/fontProviderAuthority = 0x7f0301ee
me.rerere.rikkahub.debug:color/material_personalized_color_surface_dim = 0x7f0502d3
me.rerere.rikkahub.debug:attr/fontFamily = 0x7f0301ed
me.rerere.rikkahub.debug:attr/flow_verticalBias = 0x7f0301e8
me.rerere.rikkahub.debug:styleable/Variant = 0x7f13009a
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_secondary = 0x7f05020c
me.rerere.rikkahub.debug:attr/layout_constraintVertical_bias = 0x7f03028d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f120440
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f12026c
me.rerere.rikkahub.debug:dimen/mtrl_fab_elevation = 0x7f0602c6
me.rerere.rikkahub.debug:dimen/m3_extended_fab_min_height = 0x7f0601c2
me.rerere.rikkahub.debug:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f120062
me.rerere.rikkahub.debug:string/leak_canary_go_to_heap_analysis = 0x7f1100c2
me.rerere.rikkahub.debug:id/month_navigation_bar = 0x7f080145
me.rerere.rikkahub.debug:attr/jlmv_textColor = 0x7f030254
me.rerere.rikkahub.debug:attr/minHeight = 0x7f030300
me.rerere.rikkahub.debug:attr/layout_constraintHorizontal_bias = 0x7f03027e
me.rerere.rikkahub.debug:attr/colorSurfaceContainerLow = 0x7f030113
me.rerere.rikkahub.debug:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060194
me.rerere.rikkahub.debug:string/setting_page_color_mode = 0x7f1101d0
me.rerere.rikkahub.debug:attr/jlmv_background = 0x7f030252
me.rerere.rikkahub.debug:id/leak_canary_row_small_text = 0x7f080113
me.rerere.rikkahub.debug:attr/flow_lastVerticalBias = 0x7f0301e3
me.rerere.rikkahub.debug:attr/badgeTextAppearance = 0x7f030057
me.rerere.rikkahub.debug:attr/flow_lastHorizontalStyle = 0x7f0301e2
me.rerere.rikkahub.debug:attr/itemHorizontalTranslationEnabled = 0x7f030236
me.rerere.rikkahub.debug:attr/flow_horizontalStyle = 0x7f0301e0
me.rerere.rikkahub.debug:attr/floatingActionButtonTertiaryStyle = 0x7f0301d8
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1202d9
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_compact_width = 0x7f0602dc
me.rerere.rikkahub.debug:attr/homeLayout = 0x7f030213
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f120115
me.rerere.rikkahub.debug:attr/materialTimePickerTitleStyle = 0x7f0302f0
me.rerere.rikkahub.debug:attr/motionDurationShort1 = 0x7f030318
me.rerere.rikkahub.debug:attr/floatingActionButtonSmallStyle = 0x7f0301d3
me.rerere.rikkahub.debug:styleable/AppBarLayoutStates = 0x7f13000c
me.rerere.rikkahub.debug:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f12035e
me.rerere.rikkahub.debug:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301d2
me.rerere.rikkahub.debug:attr/emojiCompatEnabled = 0x7f03018f
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Title = 0x7f1201b3
me.rerere.rikkahub.debug:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
me.rerere.rikkahub.debug:attr/labelStyle = 0x7f03025b
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f120167
me.rerere.rikkahub.debug:string/bottomsheet_drag_handle_content_description = 0x7f110059
me.rerere.rikkahub.debug:attr/closeIconStartPadding = 0x7f0300d0
me.rerere.rikkahub.debug:style/Base.V14.Theme.MaterialComponents.Light = 0x7f120095
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_dark_pressed = 0x7f050034
me.rerere.rikkahub.debug:attr/fastScrollVerticalThumbDrawable = 0x7f0301c7
me.rerere.rikkahub.debug:styleable/AppCompatImageView = 0x7f13000f
me.rerere.rikkahub.debug:bool/enable_system_foreground_service_default = 0x7f040003
me.rerere.rikkahub.debug:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060256
me.rerere.rikkahub.debug:attr/itemRippleColor = 0x7f03023f
me.rerere.rikkahub.debug:string/stop = 0x7f1101f2
me.rerere.rikkahub.debug:color/switch_thumb_normal_material_light = 0x7f05033f
me.rerere.rikkahub.debug:id/leak_canary_single_leak_trace_row = 0x7f080117
me.rerere.rikkahub.debug:color/m3_sys_color_light_inverse_surface = 0x7f050206
me.rerere.rikkahub.debug:attr/fastScrollHorizontalTrackDrawable = 0x7f0301c6
me.rerere.rikkahub.debug:attr/layout_constraintBottom_toTopOf = 0x7f030270
me.rerere.rikkahub.debug:dimen/mtrl_btn_pressed_z = 0x7f060279
me.rerere.rikkahub.debug:attr/fabSize = 0x7f0301c3
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Headline = 0x7f12001f
me.rerere.rikkahub.debug:string/leak_canary_about_enable_heap_dump_textOn = 0x7f1100af
me.rerere.rikkahub.debug:dimen/notification_top_pad_large_text = 0x7f06032a
me.rerere.rikkahub.debug:attr/region_heightLessThan = 0x7f03037e
me.rerere.rikkahub.debug:color/m3_calendar_item_disabled_text = 0x7f05009e
me.rerere.rikkahub.debug:dimen/abc_text_size_caption_material = 0x7f060042
me.rerere.rikkahub.debug:color/m3_sys_color_primary_fixed_dim = 0x7f05022a
me.rerere.rikkahub.debug:styleable/AnimatedStateListDrawableTransition = 0x7f13000a
me.rerere.rikkahub.debug:attr/fabCustomSize = 0x7f0301c2
me.rerere.rikkahub.debug:attr/counterEnabled = 0x7f030143
me.rerere.rikkahub.debug:attr/fabCradleVerticalOffset = 0x7f0301c1
me.rerere.rikkahub.debug:attr/layout_constraintEnd_toStartOf = 0x7f030276
me.rerere.rikkahub.debug:attr/motionEasingStandardInterpolator = 0x7f030327
me.rerere.rikkahub.debug:color/material_personalized_color_surface_container_lowest = 0x7f0502d2
me.rerere.rikkahub.debug:attr/fabCradleMargin = 0x7f0301bf
me.rerere.rikkahub.debug:string/m3c_date_picker_scroll_to_later_years = 0x7f110113
me.rerere.rikkahub.debug:attr/layout_constraintWidth_min = 0x7f030292
me.rerere.rikkahub.debug:style/Widget.Material3.ActionMode = 0x7f120344
me.rerere.rikkahub.debug:color/m3_navigation_item_background_color = 0x7f0500c7
me.rerere.rikkahub.debug:id/leak_canary_notification_no_retained_object_on_tap = 0x7f08010d
me.rerere.rikkahub.debug:attr/fabAnimationMode = 0x7f0301be
me.rerere.rikkahub.debug:attr/bottomInsetScrimEnabled = 0x7f030074
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_dark_normal = 0x7f07008d
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Small = 0x7f12002b
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602db
me.rerere.rikkahub.debug:attr/itemMaxLines = 0x7f03023a
me.rerere.rikkahub.debug:attr/colorOnSurface = 0x7f0300f7
me.rerere.rikkahub.debug:dimen/m3_side_sheet_margin_detached = 0x7f0601f5
me.rerere.rikkahub.debug:attr/actionModeTheme = 0x7f03001e
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f120037
me.rerere.rikkahub.debug:attr/extraMultilineHeightEnabled = 0x7f0301ba
me.rerere.rikkahub.debug:color/material_dynamic_color_light_on_error_container = 0x7f050256
me.rerere.rikkahub.debug:attr/layout_constraintWidth_max = 0x7f030291
me.rerere.rikkahub.debug:attr/extendedFloatingActionButtonStyle = 0x7f0301b7
me.rerere.rikkahub.debug:attr/layout_behavior = 0x7f030267
me.rerere.rikkahub.debug:attr/extendStrategy = 0x7f0301b4
me.rerere.rikkahub.debug:styleable/KeyFrame = 0x7f130041
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionButton = 0x7f1202f2
me.rerere.rikkahub.debug:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f0601a9
me.rerere.rikkahub.debug:string/m3c_date_input_invalid_year_range = 0x7f11010a
me.rerere.rikkahub.debug:string/abc_searchview_description_submit = 0x7f110016
me.rerere.rikkahub.debug:attr/applyMotionScene = 0x7f030035
me.rerere.rikkahub.debug:attr/iconPadding = 0x7f03021a
me.rerere.rikkahub.debug:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
me.rerere.rikkahub.debug:id/unlabeled = 0x7f08022a
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary80 = 0x7f050172
me.rerere.rikkahub.debug:color/material_personalized_color_primary_inverse = 0x7f0502c5
me.rerere.rikkahub.debug:color/highlighted_text_material_light = 0x7f05006a
me.rerere.rikkahub.debug:attr/showAnimationBehavior = 0x7f0303a5
me.rerere.rikkahub.debug:attr/expandedTitleGravity = 0x7f0301ab
me.rerere.rikkahub.debug:color/highlighted_text_material_dark = 0x7f050069
me.rerere.rikkahub.debug:attr/chipCornerRadius = 0x7f0300b1
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f120058
me.rerere.rikkahub.debug:attr/expandedHintEnabled = 0x7f0301aa
me.rerere.rikkahub.debug:string/assistant_page_top_p_value = 0x7f11004f
me.rerere.rikkahub.debug:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c0059
me.rerere.rikkahub.debug:id/ignore = 0x7f0800d3
me.rerere.rikkahub.debug:id/accessibility_custom_action_4 = 0x7f08002a
me.rerere.rikkahub.debug:color/dim_foreground_disabled_material_dark = 0x7f050061
me.rerere.rikkahub.debug:attr/expanded = 0x7f0301a9
me.rerere.rikkahub.debug:attr/materialIconButtonFilledTonalStyle = 0x7f0302e4
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Chip.Entry = 0x7f120413
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_primary = 0x7f0501c4
me.rerere.rikkahub.debug:attr/bottomAppBarStyle = 0x7f030073
me.rerere.rikkahub.debug:attr/errorShown = 0x7f0301a4
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f1100f8
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0501d0
me.rerere.rikkahub.debug:anim/abc_grow_fade_in_from_bottom = 0x7f010002
me.rerere.rikkahub.debug:attr/endIconContentDescription = 0x7f030192
me.rerere.rikkahub.debug:attr/itemMinHeight = 0x7f03023b
me.rerere.rikkahub.debug:string/leak_canary_shortcut_label = 0x7f1100e9
me.rerere.rikkahub.debug:attr/checkedIconMargin = 0x7f0300aa
me.rerere.rikkahub.debug:attr/layout_insetEdge = 0x7f03029d
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1200f1
me.rerere.rikkahub.debug:string/m3c_search_bar_search = 0x7f11012b
me.rerere.rikkahub.debug:drawable/ic_m3_chip_check = 0x7f0700a5
me.rerere.rikkahub.debug:attr/title = 0x7f030461
me.rerere.rikkahub.debug:attr/errorEnabled = 0x7f0301a0
me.rerere.rikkahub.debug:attr/colorOnContainerUnchecked = 0x7f0300eb
me.rerere.rikkahub.debug:styleable/RecycleListView = 0x7f13007b
me.rerere.rikkahub.debug:attr/chipMinHeight = 0x7f0300b9
me.rerere.rikkahub.debug:attr/action = 0x7f030000
me.rerere.rikkahub.debug:attr/enterAnim = 0x7f03019c
me.rerere.rikkahub.debug:string/material_hour_24h_suffix = 0x7f110145
me.rerere.rikkahub.debug:attr/shapeAppearanceCornerMedium = 0x7f03039c
me.rerere.rikkahub.debug:style/Theme.AppCompat.Dialog.MinWidth = 0x7f12021c
me.rerere.rikkahub.debug:attr/suffixTextColor = 0x7f0303e8
me.rerere.rikkahub.debug:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f11016d
me.rerere.rikkahub.debug:id/tag_compat_insets_dispatch = 0x7f0801ec
me.rerere.rikkahub.debug:attr/content = 0x7f030124
me.rerere.rikkahub.debug:id/arc = 0x7f08004d
me.rerere.rikkahub.debug:attr/shapeAppearance = 0x7f030398
me.rerere.rikkahub.debug:attr/ensureMinTouchTargetSize = 0x7f03019b
me.rerere.rikkahub.debug:dimen/abc_panel_menu_list_width = 0x7f060034
me.rerere.rikkahub.debug:bool/leak_canary_watcher_watch_dismissed_dialogs = 0x7f04000a
me.rerere.rikkahub.debug:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f06029e
me.rerere.rikkahub.debug:attr/layout_constraintHorizontal_weight = 0x7f030280
me.rerere.rikkahub.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f120171
me.rerere.rikkahub.debug:attr/tabPaddingStart = 0x7f030402
me.rerere.rikkahub.debug:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601d7
me.rerere.rikkahub.debug:attr/largeFontVerticalOffsetAdjustment = 0x7f03025d
me.rerere.rikkahub.debug:attr/endIconScaleType = 0x7f030196
me.rerere.rikkahub.debug:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0106
me.rerere.rikkahub.debug:color/m3_assist_chip_stroke_color = 0x7f050097
me.rerere.rikkahub.debug:attr/endIconMode = 0x7f030195
me.rerere.rikkahub.debug:style/Widget.Material3.BottomSheet = 0x7f120352
me.rerere.rikkahub.debug:attr/floatingActionButtonPrimaryStyle = 0x7f0301cf
me.rerere.rikkahub.debug:dimen/m3_sys_elevation_level3 = 0x7f060203
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500e3
me.rerere.rikkahub.debug:color/m3_primary_text_disable_only = 0x7f0500cf
me.rerere.rikkahub.debug:id/leftToRight = 0x7f08011e
me.rerere.rikkahub.debug:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
me.rerere.rikkahub.debug:attr/enableEdgeToEdge = 0x7f030190
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501e9
me.rerere.rikkahub.debug:attr/elevationOverlayEnabled = 0x7f03018e
me.rerere.rikkahub.debug:dimen/tooltip_margin = 0x7f06032d
me.rerere.rikkahub.debug:string/translator_page_input_placeholder = 0x7f1101fe
me.rerere.rikkahub.debug:attr/elevation = 0x7f03018b
me.rerere.rikkahub.debug:dimen/m3_comp_search_bar_avatar_size = 0x7f06017e
me.rerere.rikkahub.debug:attr/textAppearanceHeadline1 = 0x7f03041c
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008a
me.rerere.rikkahub.debug:attr/bottomSheetStyle = 0x7f030078
me.rerere.rikkahub.debug:id/view_offset_helper = 0x7f08022d
me.rerere.rikkahub.debug:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06029b
me.rerere.rikkahub.debug:id/spacer = 0x7f0801cb
me.rerere.rikkahub.debug:attr/behavior_significantVelocityThreshold = 0x7f03006f
me.rerere.rikkahub.debug:attr/motionDurationLong1 = 0x7f030310
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060265
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0062
me.rerere.rikkahub.debug:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f0601a0
me.rerere.rikkahub.debug:attr/editTextBackground = 0x7f030188
me.rerere.rikkahub.debug:drawable/abc_list_longpressed_holo = 0x7f07004f
me.rerere.rikkahub.debug:attr/implementationMode = 0x7f030223
me.rerere.rikkahub.debug:attr/colorSurfaceVariant = 0x7f030117
me.rerere.rikkahub.debug:attr/fontProviderQuery = 0x7f0301f4
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500f1
me.rerere.rikkahub.debug:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
me.rerere.rikkahub.debug:attr/dynamicColorThemeOverlay = 0x7f030187
me.rerere.rikkahub.debug:style/Widget.Material3.SearchBar.Outlined = 0x7f1203cc
me.rerere.rikkahub.debug:color/ucrop_color_default_crop_grid = 0x7f05034a
me.rerere.rikkahub.debug:string/m3c_date_range_picker_start_headline = 0x7f110123
me.rerere.rikkahub.debug:dimen/appcompat_dialog_background_inset = 0x7f060051
me.rerere.rikkahub.debug:color/m3_fab_ripple_color_selector = 0x7f0500bf
me.rerere.rikkahub.debug:id/mtrl_picker_fullscreen = 0x7f080158
me.rerere.rikkahub.debug:attr/chipSurfaceColor = 0x7f0300c3
me.rerere.rikkahub.debug:string/chat_page_export_markdown = 0x7f11006f
me.rerere.rikkahub.debug:layout/mtrl_calendar_month_navigation = 0x7f0b005d
me.rerere.rikkahub.debug:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060258
me.rerere.rikkahub.debug:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
me.rerere.rikkahub.debug:styleable/SwitchCompat = 0x7f13008e
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1203bb
me.rerere.rikkahub.debug:id/mtrl_calendar_frame = 0x7f08014e
me.rerere.rikkahub.debug:attr/fabAlignmentMode = 0x7f0301bb
me.rerere.rikkahub.debug:attr/colorTertiaryFixed = 0x7f03011b
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_emphasized_path_data = 0x7f1100f9
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0093
me.rerere.rikkahub.debug:attr/indicatorSize = 0x7f03022a
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f12042a
me.rerere.rikkahub.debug:attr/drawableTintMode = 0x7f03017e
me.rerere.rikkahub.debug:style/Platform.Widget.AppCompat.Spinner = 0x7f120148
me.rerere.rikkahub.debug:color/material_personalized_color_primary_text_inverse = 0x7f0502c7
me.rerere.rikkahub.debug:attr/trackCornerRadius = 0x7f030481
me.rerere.rikkahub.debug:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003b
me.rerere.rikkahub.debug:style/Theme.Material3.Dark.Dialog.Alert = 0x7f120230
me.rerere.rikkahub.debug:attr/buttonStyle = 0x7f030092
me.rerere.rikkahub.debug:attr/drawableStartCompat = 0x7f03017c
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1203e7
me.rerere.rikkahub.debug:attr/drawableRightCompat = 0x7f03017a
me.rerere.rikkahub.debug:id/leak_canary_loading = 0x7f080102
me.rerere.rikkahub.debug:attr/helperTextEnabled = 0x7f030206
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f12004f
me.rerere.rikkahub.debug:attr/colorSecondaryVariant = 0x7f03010d
me.rerere.rikkahub.debug:layout/design_text_input_start_icon = 0x7f0b002b
me.rerere.rikkahub.debug:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060137
me.rerere.rikkahub.debug:attr/dragThreshold = 0x7f030175
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007b
me.rerere.rikkahub.debug:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b0055
me.rerere.rikkahub.debug:id/leak_canary_spinner = 0x7f080118
me.rerere.rikkahub.debug:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501d8
me.rerere.rikkahub.debug:string/assistant_page_add = 0x7f11001e
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral98 = 0x7f05014d
me.rerere.rikkahub.debug:id/material_timepicker_container = 0x7f080139
me.rerere.rikkahub.debug:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
me.rerere.rikkahub.debug:attr/expandedTitleMarginEnd = 0x7f0301ae
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1201b7
me.rerere.rikkahub.debug:id/on = 0x7f080173
me.rerere.rikkahub.debug:color/material_dynamic_neutral99 = 0x7f050263
me.rerere.rikkahub.debug:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1203c2
me.rerere.rikkahub.debug:string/common_google_play_services_wear_update_text = 0x7f110093
me.rerere.rikkahub.debug:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301b5
me.rerere.rikkahub.debug:integer/m3_sys_motion_duration_extra_long1 = 0x7f090010
me.rerere.rikkahub.debug:attr/colorSecondaryContainer = 0x7f03010a
me.rerere.rikkahub.debug:id/material_clock_level = 0x7f08012e
me.rerere.rikkahub.debug:attr/listChoiceIndicatorSingleAnimated = 0x7f0302ad
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_height = 0x7f060287
me.rerere.rikkahub.debug:attr/dividerHorizontal = 0x7f03016d
me.rerere.rikkahub.debug:attr/color = 0x7f0300df
me.rerere.rikkahub.debug:attr/colorContainer = 0x7f0300e3
me.rerere.rikkahub.debug:attr/colorSurfaceContainerHigh = 0x7f030111
me.rerere.rikkahub.debug:attr/dividerColor = 0x7f03016c
me.rerere.rikkahub.debug:integer/mtrl_calendar_selection_text_lines = 0x7f090033
me.rerere.rikkahub.debug:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070020
me.rerere.rikkahub.debug:id/leak_canary_main_container = 0x7f080103
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06021a
me.rerere.rikkahub.debug:color/design_dark_default_color_on_error = 0x7f050040
me.rerere.rikkahub.debug:styleable/LoadingImageView = 0x7f13004c
me.rerere.rikkahub.debug:attr/paddingBottomSystemWindowInsets = 0x7f030349
me.rerere.rikkahub.debug:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060142
me.rerere.rikkahub.debug:dimen/design_bottom_navigation_item_max_width = 0x7f060065
me.rerere.rikkahub.debug:attr/arrowShaftLength = 0x7f030039
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0501c1
me.rerere.rikkahub.debug:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f070019
me.rerere.rikkahub.debug:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602bd
me.rerere.rikkahub.debug:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060281
me.rerere.rikkahub.debug:id/material_hour_tv = 0x7f080133
me.rerere.rikkahub.debug:id/forever = 0x7f0800bd
me.rerere.rikkahub.debug:attr/destination = 0x7f030166
me.rerere.rikkahub.debug:id/nav_controller_view_tag = 0x7f080163
me.rerere.rikkahub.debug:attr/colorSwitchThumbNormal = 0x7f030118
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_light_default = 0x7f050036
me.rerere.rikkahub.debug:attr/menuAlignmentMode = 0x7f0302fd
me.rerere.rikkahub.debug:attr/scaleType = 0x7f03038a
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f120375
me.rerere.rikkahub.debug:attr/badgeWithTextShapeAppearance = 0x7f03005e
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0061
me.rerere.rikkahub.debug:attr/deriveConstraintsFrom = 0x7f030165
me.rerere.rikkahub.debug:attr/defaultState = 0x7f030162
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary100 = 0x7f050112
me.rerere.rikkahub.debug:styleable/SideSheetBehavior_Layout = 0x7f130084
me.rerere.rikkahub.debug:attr/showText = 0x7f0303ac
me.rerere.rikkahub.debug:attr/checkedIcon = 0x7f0300a7
me.rerere.rikkahub.debug:attr/contentPaddingTop = 0x7f030132
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f12037b
me.rerere.rikkahub.debug:attr/listPreferredItemPaddingEnd = 0x7f0302b6
me.rerere.rikkahub.debug:styleable/AnimatedStateListDrawableItem = 0x7f130009
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f06014f
me.rerere.rikkahub.debug:layout/notification_action = 0x7f0b0070
me.rerere.rikkahub.debug:attr/daySelectedStyle = 0x7f03015b
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1200cb
me.rerere.rikkahub.debug:attr/dividerInsetEnd = 0x7f03016e
me.rerere.rikkahub.debug:attr/fontProviderCerts = 0x7f0301ef
me.rerere.rikkahub.debug:attr/topInsetScrimEnabled = 0x7f030479
me.rerere.rikkahub.debug:drawable/ic_call_answer_video_low = 0x7f07009f
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral40 = 0x7f050141
me.rerere.rikkahub.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f120159
me.rerere.rikkahub.debug:attr/closeIconVisible = 0x7f0300d2
me.rerere.rikkahub.debug:attr/paddingStart = 0x7f03034d
me.rerere.rikkahub.debug:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
me.rerere.rikkahub.debug:id/showTitle = 0x7f0801c3
me.rerere.rikkahub.debug:dimen/m3_badge_with_text_vertical_padding = 0x7f0600cb
me.rerere.rikkahub.debug:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
me.rerere.rikkahub.debug:attr/enforceMaterialTheme = 0x7f030199
me.rerere.rikkahub.debug:attr/textAppearanceHeadlineLarge = 0x7f030422
me.rerere.rikkahub.debug:id/leak_canary_heap_dump_leaks = 0x7f0800fd
me.rerere.rikkahub.debug:color/m3_timepicker_button_ripple_color = 0x7f05023e
me.rerere.rikkahub.debug:layout/material_timepicker = 0x7f0b004d
me.rerere.rikkahub.debug:attr/extendMotionSpec = 0x7f0301b3
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents = 0x7f1202c5
me.rerere.rikkahub.debug:attr/firstBaselineToTopHeight = 0x7f0301c9
me.rerere.rikkahub.debug:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f06019d
me.rerere.rikkahub.debug:macro/m3_comp_slider_inactive_track_color = 0x7f0c0110
me.rerere.rikkahub.debug:attr/behavior_saveFlags = 0x7f03006e
me.rerere.rikkahub.debug:attr/animateNavigationIcon = 0x7f030031
me.rerere.rikkahub.debug:string/use_web_search = 0x7f110212
me.rerere.rikkahub.debug:id/textinput_prefix_text = 0x7f08020c
me.rerere.rikkahub.debug:anim/abc_fade_out = 0x7f010001
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_container_highest = 0x7f0501aa
me.rerere.rikkahub.debug:dimen/mtrl_chip_pressed_translation_z = 0x7f0602b1
me.rerere.rikkahub.debug:attr/switchStyle = 0x7f0303ed
me.rerere.rikkahub.debug:attr/helperText = 0x7f030205
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant0 = 0x7f05014f
me.rerere.rikkahub.debug:string/assistant_page_inject_message_time_desc = 0x7f110034
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_error_container = 0x7f0501d5
me.rerere.rikkahub.debug:attr/css = 0x7f03014a
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1200c8
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060214
me.rerere.rikkahub.debug:id/topPanel = 0x7f080217
me.rerere.rikkahub.debug:attr/crossfade = 0x7f030149
me.rerere.rikkahub.debug:color/material_dynamic_neutral10 = 0x7f050258
me.rerere.rikkahub.debug:color/design_fab_stroke_top_inner_color = 0x7f05005d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f12040d
me.rerere.rikkahub.debug:style/TextAppearance.Material3.HeadlineSmall = 0x7f1201f2
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f06021e
me.rerere.rikkahub.debug:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301b9
me.rerere.rikkahub.debug:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f06019f
me.rerere.rikkahub.debug:layout/abc_alert_dialog_title_material = 0x7f0b000a
me.rerere.rikkahub.debug:color/leak_canary_heap_hprof_string = 0x7f050082
me.rerere.rikkahub.debug:attr/counterTextColor = 0x7f030148
me.rerere.rikkahub.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f120084
me.rerere.rikkahub.debug:string/mtrl_exceed_max_badge_number_content_description = 0x7f110177
me.rerere.rikkahub.debug:id/icon = 0x7f0800cf
me.rerere.rikkahub.debug:attr/counterTextAppearance = 0x7f030147
me.rerere.rikkahub.debug:style/TextAppearance.MaterialComponents.Button = 0x7f120200
me.rerere.rikkahub.debug:attr/counterOverflowTextColor = 0x7f030146
me.rerere.rikkahub.debug:attr/cornerSizeBottomLeft = 0x7f03013f
me.rerere.rikkahub.debug:id/mtrl_picker_text_input_date = 0x7f08015d
me.rerere.rikkahub.debug:attr/itemHorizontalPadding = 0x7f030235
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f06020d
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f120426
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_error_container = 0x7f050209
me.rerere.rikkahub.debug:attr/ucrop_frame_color = 0x7f03049b
me.rerere.rikkahub.debug:attr/customPixelDimension = 0x7f030156
me.rerere.rikkahub.debug:dimen/m3_large_fab_size = 0x7f0601ca
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b4
me.rerere.rikkahub.debug:attr/cornerRadius = 0x7f03013d
me.rerere.rikkahub.debug:macro/m3_comp_fab_secondary_container_color = 0x7f0c003b
me.rerere.rikkahub.debug:attr/colorControlActivated = 0x7f0300e4
me.rerere.rikkahub.debug:attr/itemStrokeWidth = 0x7f030249
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f060209
me.rerere.rikkahub.debug:attr/checkedIconVisible = 0x7f0300ad
me.rerere.rikkahub.debug:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
me.rerere.rikkahub.debug:attr/expandedTitleMargin = 0x7f0301ac
me.rerere.rikkahub.debug:attr/cornerFamilyTopLeft = 0x7f03013b
me.rerere.rikkahub.debug:color/cardview_dark_background = 0x7f05002c
me.rerere.rikkahub.debug:id/src_in = 0x7f0801d3
me.rerere.rikkahub.debug:drawable/indeterminate_static = 0x7f0700ad
me.rerere.rikkahub.debug:attr/behavior_autoHide = 0x7f030065
me.rerere.rikkahub.debug:integer/mtrl_card_anim_delay_ms = 0x7f090035
me.rerere.rikkahub.debug:attr/state_lifted = 0x7f0303d4
me.rerere.rikkahub.debug:styleable/PropertySet = 0x7f130078
me.rerere.rikkahub.debug:attr/maxButtonHeight = 0x7f0302f3
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501ed
me.rerere.rikkahub.debug:attr/contentInsetStartWithNavigation = 0x7f03012b
me.rerere.rikkahub.debug:attr/cornerFamily = 0x7f030138
me.rerere.rikkahub.debug:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060116
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.TimePicker.Button = 0x7f12045e
me.rerere.rikkahub.debug:id/scale_scroll_wheel = 0x7f0801ab
me.rerere.rikkahub.debug:animator/mtrl_card_state_list_anim = 0x7f020017
me.rerere.rikkahub.debug:attr/coplanarSiblingViewId = 0x7f030137
me.rerere.rikkahub.debug:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0081
me.rerere.rikkahub.debug:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601b3
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f0
me.rerere.rikkahub.debug:attr/layout_editor_absoluteX = 0x7f030295
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary40 = 0x7f050115
me.rerere.rikkahub.debug:attr/actionBarItemBackground = 0x7f030002
me.rerere.rikkahub.debug:attr/contrast = 0x7f030134
me.rerere.rikkahub.debug:attr/actionBarStyle = 0x7f030006
me.rerere.rikkahub.debug:attr/indeterminateAnimationType = 0x7f030224
me.rerere.rikkahub.debug:id/textinput_counter = 0x7f080208
me.rerere.rikkahub.debug:attr/titleCollapseMode = 0x7f030463
me.rerere.rikkahub.debug:dimen/material_clock_period_toggle_width = 0x7f06023e
me.rerere.rikkahub.debug:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06022c
me.rerere.rikkahub.debug:dimen/abc_alert_dialog_button_dimen = 0x7f060011
me.rerere.rikkahub.debug:string/com.google.firebase.crashlytics.mapping_file_id = 0x7f110084
me.rerere.rikkahub.debug:id/leak_canary_heap_rendering = 0x7f0800fe
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary40 = 0x7f050122
me.rerere.rikkahub.debug:attr/popupTheme = 0x7f03036c
me.rerere.rikkahub.debug:color/m3_sys_color_light_surface_dim = 0x7f05021f
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_headline_type = 0x7f0c0151
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_container_height = 0x7f060126
me.rerere.rikkahub.debug:attr/onNegativeCross = 0x7f030342
me.rerere.rikkahub.debug:dimen/m3_comp_input_chip_container_elevation = 0x7f060140
me.rerere.rikkahub.debug:attr/hoveredFocusedTranslationZ = 0x7f030216
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f12040c
me.rerere.rikkahub.debug:macro/m3_comp_dialog_container_shape = 0x7f0c0023
me.rerere.rikkahub.debug:dimen/mtrl_navigation_rail_margin = 0x7f0602e1
me.rerere.rikkahub.debug:attr/enforceTextAppearance = 0x7f03019a
me.rerere.rikkahub.debug:attr/showMarker = 0x7f0303a9
me.rerere.rikkahub.debug:style/TextAppearance.Material3.LabelLarge = 0x7f1201f3
me.rerere.rikkahub.debug:attr/windowFixedHeightMinor = 0x7f0304b8
me.rerere.rikkahub.debug:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ac
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06014a
me.rerere.rikkahub.debug:attr/iconTintMode = 0x7f03021e
me.rerere.rikkahub.debug:attr/toolbarNavigationButtonStyle = 0x7f030472
me.rerere.rikkahub.debug:attr/flow_wrapMode = 0x7f0301eb
me.rerere.rikkahub.debug:color/m3_sys_color_dark_inverse_on_surface = 0x7f050192
me.rerere.rikkahub.debug:styleable/MockView = 0x7f130062
me.rerere.rikkahub.debug:id/none = 0x7f08016d
me.rerere.rikkahub.debug:attr/buttonSize = 0x7f030091
me.rerere.rikkahub.debug:id/material_hour_text_input = 0x7f080132
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060170
me.rerere.rikkahub.debug:attr/contentPaddingRight = 0x7f030130
me.rerere.rikkahub.debug:attr/alertDialogStyle = 0x7f03002a
me.rerere.rikkahub.debug:attr/indicatorColor = 0x7f030226
me.rerere.rikkahub.debug:attr/constraints = 0x7f030123
me.rerere.rikkahub.debug:dimen/material_helper_text_default_padding_top = 0x7f06024d
me.rerere.rikkahub.debug:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600c0
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1200ce
me.rerere.rikkahub.debug:attr/constraint_referenced_ids = 0x7f030122
me.rerere.rikkahub.debug:color/call_notification_decline_color = 0x7f05002b
me.rerere.rikkahub.debug:color/leak_canary_class_name = 0x7f05006d
me.rerere.rikkahub.debug:xml/backup_rules = 0x7f140001
me.rerere.rikkahub.debug:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120117
me.rerere.rikkahub.debug:id/beginOnFirstDraw = 0x7f080056
me.rerere.rikkahub.debug:drawable/notification_bg_low = 0x7f0700fb
me.rerere.rikkahub.debug:attr/radioButtonStyle = 0x7f030378
me.rerere.rikkahub.debug:color/m3_sys_color_dark_on_primary_container = 0x7f050199
me.rerere.rikkahub.debug:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f120361
me.rerere.rikkahub.debug:attr/constraintSetStart = 0x7f030121
me.rerere.rikkahub.debug:string/path_password_eye = 0x7f1101ac
me.rerere.rikkahub.debug:attr/checkedIconTint = 0x7f0300ac
me.rerere.rikkahub.debug:color/m3_dynamic_primary_text_disable_only = 0x7f0500ba
me.rerere.rikkahub.debug:attr/checkboxStyle = 0x7f0300a4
me.rerere.rikkahub.debug:color/mtrl_switch_track_tint = 0x7f050317
me.rerere.rikkahub.debug:string/m3c_time_picker_toggle_touch = 0x7f11013e
me.rerere.rikkahub.debug:attr/arrowHeadLength = 0x7f030038
me.rerere.rikkahub.debug:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010024
me.rerere.rikkahub.debug:dimen/mtrl_btn_corner_radius = 0x7f060269
me.rerere.rikkahub.debug:dimen/abc_text_size_display_1_material = 0x7f060043
me.rerere.rikkahub.debug:string/leak_canary_notification_retained_dump_wait = 0x7f1100de
me.rerere.rikkahub.debug:layout/ucrop_aspect_ratio = 0x7f0b007d
me.rerere.rikkahub.debug:attr/colorPrimary = 0x7f030100
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant80 = 0x7f05026d
me.rerere.rikkahub.debug:attr/commitIcon = 0x7f03011d
me.rerere.rikkahub.debug:animator/mtrl_btn_state_list_anim = 0x7f020015
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog = 0x7f12026d
me.rerere.rikkahub.debug:anim/linear_indeterminate_line1_head_interpolator = 0x7f010023
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0064
me.rerere.rikkahub.debug:attr/tabTextAppearance = 0x7f030409
me.rerere.rikkahub.debug:color/leak_canary_bottom_menu = 0x7f05006c
me.rerere.rikkahub.debug:attr/colorSurfaceBright = 0x7f03010f
me.rerere.rikkahub.debug:dimen/m3_comp_assist_chip_container_height = 0x7f06010a
me.rerere.rikkahub.debug:attr/layout_constraintBaseline_creator = 0x7f03026c
me.rerere.rikkahub.debug:dimen/m3_appbar_scrim_height_trigger = 0x7f0600b6
me.rerere.rikkahub.debug:id/wrap_content = 0x7f08023b
me.rerere.rikkahub.debug:anim/m3_side_sheet_exit_to_left = 0x7f01002d
me.rerere.rikkahub.debug:attr/titleTextStyle = 0x7f03046f
me.rerere.rikkahub.debug:integer/bottom_sheet_slide_duration = 0x7f090003
me.rerere.rikkahub.debug:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602c8
me.rerere.rikkahub.debug:dimen/mtrl_btn_text_size = 0x7f06027f
me.rerere.rikkahub.debug:dimen/abc_text_size_body_1_material = 0x7f06003f
me.rerere.rikkahub.debug:drawable/$avd_hide_password__0 = 0x7f070000
me.rerere.rikkahub.debug:attr/colorSurfaceContainer = 0x7f030110
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060213
me.rerere.rikkahub.debug:string/material_timepicker_select_time = 0x7f110157
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c006f
me.rerere.rikkahub.debug:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f050228
me.rerere.rikkahub.debug:attr/actionOverflowMenuStyle = 0x7f030021
me.rerere.rikkahub.debug:attr/colorOutlineVariant = 0x7f0300ff
me.rerere.rikkahub.debug:style/Base.V26.Theme.AppCompat.Light = 0x7f1200b6
me.rerere.rikkahub.debug:string/m3c_date_picker_headline = 0x7f11010e
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant95 = 0x7f05026f
me.rerere.rikkahub.debug:bool/leak_canary_add_launcher_icon = 0x7f040006
me.rerere.rikkahub.debug:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301ce
me.rerere.rikkahub.debug:anim/abc_popup_exit = 0x7f010004
me.rerere.rikkahub.debug:attr/windowMinWidthMinor = 0x7f0304bc
me.rerere.rikkahub.debug:id/staticPostLayout = 0x7f0801e2
me.rerere.rikkahub.debug:attr/endIconMinSize = 0x7f030194
me.rerere.rikkahub.debug:attr/minSeparation = 0x7f030302
me.rerere.rikkahub.debug:attr/colorOnSecondaryFixed = 0x7f0300f5
me.rerere.rikkahub.debug:attr/colorOnSecondaryContainer = 0x7f0300f4
me.rerere.rikkahub.debug:string/m3c_time_picker_minute_text_field = 0x7f11013a
me.rerere.rikkahub.debug:attr/colorOnSecondary = 0x7f0300f3
me.rerere.rikkahub.debug:drawable/mtrl_popupmenu_background = 0x7f0700e9
me.rerere.rikkahub.debug:color/material_deep_teal_200 = 0x7f05024c
me.rerere.rikkahub.debug:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060152
me.rerere.rikkahub.debug:attr/collapsedSize = 0x7f0300d6
me.rerere.rikkahub.debug:id/TOP_START = 0x7f08000d
me.rerere.rikkahub.debug:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f12043e
me.rerere.rikkahub.debug:style/Widget.Material3.CardView.Outlined = 0x7f12036a
me.rerere.rikkahub.debug:attr/colorOnPrimaryContainer = 0x7f0300ef
me.rerere.rikkahub.debug:id/spline = 0x7f0801cd
me.rerere.rikkahub.debug:id/overlay_view = 0x7f080184
me.rerere.rikkahub.debug:attr/counterOverflowTextAppearance = 0x7f030145
me.rerere.rikkahub.debug:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600b7
me.rerere.rikkahub.debug:dimen/hint_pressed_alpha_material_light = 0x7f060099
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1201c6
me.rerere.rikkahub.debug:attr/colorOnPrimary = 0x7f0300ee
me.rerere.rikkahub.debug:attr/chipEndPadding = 0x7f0300b2
me.rerere.rikkahub.debug:style/Widget.AppCompat.ListView.Menu = 0x7f120321
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00fd
me.rerere.rikkahub.debug:attr/collapsedTitleGravity = 0x7f0300d7
me.rerere.rikkahub.debug:id/bottom = 0x7f080059
me.rerere.rikkahub.debug:attr/colorOnErrorContainer = 0x7f0300ed
me.rerere.rikkahub.debug:attr/layout_constraintTop_toTopOf = 0x7f03028c
me.rerere.rikkahub.debug:attr/actionModePasteDrawable = 0x7f030018
me.rerere.rikkahub.debug:attr/colorError = 0x7f0300e7
me.rerere.rikkahub.debug:string/m3c_dropdown_menu_toggle = 0x7f110128
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_card_container_elevation = 0x7f060160
me.rerere.rikkahub.debug:color/mtrl_choice_chip_background_color = 0x7f0502fc
me.rerere.rikkahub.debug:color/leak_canary_heap_class_dump = 0x7f05007f
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600d7
me.rerere.rikkahub.debug:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1200b7
me.rerere.rikkahub.debug:attr/lStar = 0x7f030259
me.rerere.rikkahub.debug:attr/colorButtonNormal = 0x7f0300e2
me.rerere.rikkahub.debug:color/design_default_color_primary_variant = 0x7f050053
me.rerere.rikkahub.debug:attr/touchRegionId = 0x7f03047c
me.rerere.rikkahub.debug:attr/marginHorizontal = 0x7f0302be
me.rerere.rikkahub.debug:attr/expandedTitleMarginStart = 0x7f0301af
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary10 = 0x7f050111
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0127
me.rerere.rikkahub.debug:color/m3_ref_palette_error99 = 0x7f050136
me.rerere.rikkahub.debug:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
me.rerere.rikkahub.debug:color/black = 0x7f050021
me.rerere.rikkahub.debug:attr/behavior_peekHeight = 0x7f03006d
me.rerere.rikkahub.debug:string/path_password_eye_mask_strike_through = 0x7f1101ad
me.rerere.rikkahub.debug:attr/colorScheme = 0x7f030108
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015a
me.rerere.rikkahub.debug:color/leak_canary_heap_app = 0x7f05007b
me.rerere.rikkahub.debug:attr/itemFillColor = 0x7f030234
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060218
me.rerere.rikkahub.debug:attr/checkedTextViewStyle = 0x7f0300af
me.rerere.rikkahub.debug:attr/layout_constraintRight_creator = 0x7f030284
me.rerere.rikkahub.debug:id/leak_canary_navigation_button_leaks = 0x7f080108
me.rerere.rikkahub.debug:dimen/mtrl_progress_circular_size_medium = 0x7f0602eb
me.rerere.rikkahub.debug:styleable/AppCompatSeekBar = 0x7f130010
me.rerere.rikkahub.debug:animator/fragment_close_enter = 0x7f020003
me.rerere.rikkahub.debug:animator/m3_card_elevated_state_list_anim = 0x7f02000c
me.rerere.rikkahub.debug:integer/abc_config_activityDefaultDur = 0x7f090000
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_on_surface = 0x7f0501be
me.rerere.rikkahub.debug:attr/collapseIcon = 0x7f0300d5
me.rerere.rikkahub.debug:color/m3_dynamic_default_color_secondary_text = 0x7f0500b7
me.rerere.rikkahub.debug:string/assistant_page_available_variables = 0x7f110021
me.rerere.rikkahub.debug:layout/mtrl_layout_snackbar = 0x7f0b0061
me.rerere.rikkahub.debug:attr/drawableTint = 0x7f03017d
me.rerere.rikkahub.debug:attr/thumbElevation = 0x7f030447
me.rerere.rikkahub.debug:dimen/mtrl_tooltip_arrowSize = 0x7f060316
me.rerere.rikkahub.debug:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
me.rerere.rikkahub.debug:attr/floatingActionButtonSurfaceStyle = 0x7f0301d7
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f12040a
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120074
me.rerere.rikkahub.debug:attr/colorControlHighlight = 0x7f0300e5
me.rerere.rikkahub.debug:id/center_horizontal = 0x7f080061
me.rerere.rikkahub.debug:attr/itemTextColor = 0x7f03024e
me.rerere.rikkahub.debug:string/chat_page_export_share_via = 0x7f110071
me.rerere.rikkahub.debug:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fa
me.rerere.rikkahub.debug:attr/searchHintIcon = 0x7f03038f
me.rerere.rikkahub.debug:color/m3_sys_color_dark_primary_container = 0x7f0501a3
me.rerere.rikkahub.debug:color/leak_canary_background_color = 0x7f05006b
me.rerere.rikkahub.debug:drawable/abc_list_selector_holo_dark = 0x7f070056
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary80 = 0x7f050119
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1202d2
me.rerere.rikkahub.debug:attr/colorTertiaryFixedDim = 0x7f03011c
me.rerere.rikkahub.debug:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
me.rerere.rikkahub.debug:style/ThemeOverlay.Material3.ActionBar = 0x7f120286
me.rerere.rikkahub.debug:dimen/mtrl_bottomappbar_height = 0x7f060268
me.rerere.rikkahub.debug:anim/abc_slide_out_top = 0x7f010009
me.rerere.rikkahub.debug:dimen/hint_alpha_material_dark = 0x7f060096
me.rerere.rikkahub.debug:attr/textAppearanceSearchResultTitle = 0x7f030430
me.rerere.rikkahub.debug:attr/clockFaceBackgroundColor = 0x7f0300c8
me.rerere.rikkahub.debug:string/setting_page_color_mode_dark = 0x7f1101d1
me.rerere.rikkahub.debug:attr/scopeUris = 0x7f03038b
me.rerere.rikkahub.debug:attr/contentInsetEnd = 0x7f030126
me.rerere.rikkahub.debug:string/abc_activitychooserview_choose_application = 0x7f110005
me.rerere.rikkahub.debug:anim/leak_canary_enter_forward = 0x7f01001f
me.rerere.rikkahub.debug:string/chat_page_context_truncated = 0x7f110213
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0131
me.rerere.rikkahub.debug:attr/circleRadius = 0x7f0300c5
me.rerere.rikkahub.debug:attr/chipStyle = 0x7f0300c2
me.rerere.rikkahub.debug:string/deep_thinking = 0x7f110098
me.rerere.rikkahub.debug:dimen/mtrl_btn_letter_spacing = 0x7f060273
me.rerere.rikkahub.debug:dimen/material_clock_hand_center_dot_radius = 0x7f060237
me.rerere.rikkahub.debug:attr/autoTransition = 0x7f030043
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500d6
me.rerere.rikkahub.debug:attr/chipBackgroundColor = 0x7f0300b0
me.rerere.rikkahub.debug:string/assistant_page_memory = 0x7f110038
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary70 = 0x7f050125
me.rerere.rikkahub.debug:layout/abc_action_bar_up_container = 0x7f0b0001
me.rerere.rikkahub.debug:attr/checkedIconSize = 0x7f0300ab
me.rerere.rikkahub.debug:attr/collapsingToolbarLayoutMediumSize = 0x7f0300dc
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant70 = 0x7f05026c
me.rerere.rikkahub.debug:attr/chipStrokeWidth = 0x7f0300c1
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1200fb
me.rerere.rikkahub.debug:color/material_personalized_color_on_tertiary_container = 0x7f0502c0
me.rerere.rikkahub.debug:attr/behavior_draggable = 0x7f030067
me.rerere.rikkahub.debug:attr/chipStartPadding = 0x7f0300bf
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f120263
me.rerere.rikkahub.debug:attr/deltaPolarAngle = 0x7f030163
me.rerere.rikkahub.debug:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014b
me.rerere.rikkahub.debug:attr/launchSingleTop = 0x7f030260
me.rerere.rikkahub.debug:style/leak_canary_LeakCanary.Base = 0x7f12046d
me.rerere.rikkahub.debug:id/image_view_state_contrast = 0x7f0800da
me.rerere.rikkahub.debug:dimen/mtrl_btn_disabled_z = 0x7f06026c
me.rerere.rikkahub.debug:attr/layout_goneMarginEnd = 0x7f030298
me.rerere.rikkahub.debug:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06013d
me.rerere.rikkahub.debug:styleable/KeyAttribute = 0x7f13003f
me.rerere.rikkahub.debug:string/mtrl_switch_thumb_path_name = 0x7f1101a1
me.rerere.rikkahub.debug:attr/behavior_fitToContents = 0x7f030069
me.rerere.rikkahub.debug:color/m3_sys_color_light_on_background = 0x7f050207
me.rerere.rikkahub.debug:animator/fragment_close_exit = 0x7f020004
me.rerere.rikkahub.debug:drawable/abc_list_selector_holo_light = 0x7f070057
me.rerere.rikkahub.debug:attr/singleLine = 0x7f0303b6
me.rerere.rikkahub.debug:styleable/DrawerLayout = 0x7f13002f
me.rerere.rikkahub.debug:id/action_text = 0x7f080040
me.rerere.rikkahub.debug:attr/searchViewStyle = 0x7f030392
me.rerere.rikkahub.debug:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010026
me.rerere.rikkahub.debug:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1203ac
me.rerere.rikkahub.debug:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060299
me.rerere.rikkahub.debug:attr/backgroundStacked = 0x7f03004d
me.rerere.rikkahub.debug:color/material_dynamic_color_light_error = 0x7f050253
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary20 = 0x7f050113
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500eb
me.rerere.rikkahub.debug:attr/chipIconVisible = 0x7f0300b8
me.rerere.rikkahub.debug:attr/addElevationShadow = 0x7f030027
me.rerere.rikkahub.debug:style/Widget.Material3.AppBarLayout = 0x7f120345
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1202c6
me.rerere.rikkahub.debug:attr/useCompatPadding = 0x7f0304a5
me.rerere.rikkahub.debug:color/m3_popupmenu_overlay_color = 0x7f0500ce
me.rerere.rikkahub.debug:id/disableScroll = 0x7f080094
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral80 = 0x7f050146
me.rerere.rikkahub.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1203e9
me.rerere.rikkahub.debug:attr/actionBarSplitStyle = 0x7f030005
me.rerere.rikkahub.debug:color/abc_background_cache_hint_selector_material_light = 0x7f050001
me.rerere.rikkahub.debug:drawable/$avd_show_password__0 = 0x7f070003
me.rerere.rikkahub.debug:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060127
me.rerere.rikkahub.debug:attr/collapsedTitleTextAppearance = 0x7f0300d8
me.rerere.rikkahub.debug:style/TextAppearance.Compat.Notification.Title = 0x7f1201cd
me.rerere.rikkahub.debug:attr/contentPaddingStart = 0x7f030131
me.rerere.rikkahub.debug:styleable/KeyTrigger = 0x7f130046
me.rerere.rikkahub.debug:string/chat_page_export_success = 0x7f110072
me.rerere.rikkahub.debug:attr/centerIfNoTextEnabled = 0x7f03009f
me.rerere.rikkahub.debug:styleable/NavInclude = 0x7f13006d
me.rerere.rikkahub.debug:dimen/mtrl_calendar_header_content_padding = 0x7f06028e
me.rerere.rikkahub.debug:style/ThemeOverlay.Design.TextInputEditText = 0x7f120284
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500de
me.rerere.rikkahub.debug:attr/barrierDirection = 0x7f030063
me.rerere.rikkahub.debug:bool/leak_canary_watcher_auto_install = 0x7f040009
me.rerere.rikkahub.debug:color/mtrl_textinput_focused_box_stroke_color = 0x7f050321
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.NavigationRailView = 0x7f12043d
me.rerere.rikkahub.debug:attr/materialAlertDialogTheme = 0x7f0302c4
me.rerere.rikkahub.debug:attr/colorOnPrimaryFixed = 0x7f0300f0
me.rerere.rikkahub.debug:string/menu_page_llm_leaderboard = 0x7f11015e
me.rerere.rikkahub.debug:attr/listPopupWindowStyle = 0x7f0302b2
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_handle_color = 0x7f0c0124
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_text_dark_focused = 0x7f07008c
me.rerere.rikkahub.debug:color/m3_icon_button_icon_color_selector = 0x7f0500c3
me.rerere.rikkahub.debug:attr/motionDurationExtraLong1 = 0x7f03030c
me.rerere.rikkahub.debug:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00cc
me.rerere.rikkahub.debug:attr/actionBarTabTextStyle = 0x7f030009
me.rerere.rikkahub.debug:color/mtrl_filled_icon_tint = 0x7f050304
me.rerere.rikkahub.debug:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
me.rerere.rikkahub.debug:dimen/m3_badge_horizontal_offset = 0x7f0600c3
me.rerere.rikkahub.debug:dimen/m3_appbar_size_medium = 0x7f0600bb
me.rerere.rikkahub.debug:attr/checkedIconGravity = 0x7f0300a9
me.rerere.rikkahub.debug:string/common_google_play_services_update_title = 0x7f110091
me.rerere.rikkahub.debug:id/homeAsUp = 0x7f0800cd
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary0 = 0x7f050176
me.rerere.rikkahub.debug:attr/checkedIconEnabled = 0x7f0300a8
me.rerere.rikkahub.debug:attr/materialCardViewStyle = 0x7f0302dd
me.rerere.rikkahub.debug:attr/cornerFamilyTopRight = 0x7f03013c
me.rerere.rikkahub.debug:id/BOTTOM_START = 0x7f080002
me.rerere.rikkahub.debug:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060187
me.rerere.rikkahub.debug:color/mtrl_fab_bg_color_selector = 0x7f050300
me.rerere.rikkahub.debug:color/leak_canary_heap_zygote = 0x7f05008d
me.rerere.rikkahub.debug:attr/snackbarTextViewStyle = 0x7f0303bc
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_light_secondary = 0x7f0501e8
me.rerere.rikkahub.debug:attr/flow_firstHorizontalBias = 0x7f0301d9
me.rerere.rikkahub.debug:dimen/m3_searchbar_margin_horizontal = 0x7f0601ec
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_secondary90 = 0x7f05011a
me.rerere.rikkahub.debug:id/navigation_bar_item_icon_view = 0x7f080166
me.rerere.rikkahub.debug:attr/layout_constraintStart_toStartOf = 0x7f030288
me.rerere.rikkahub.debug:attr/iconSize = 0x7f03021b
me.rerere.rikkahub.debug:attr/textAppearanceLabelMedium = 0x7f030426
me.rerere.rikkahub.debug:attr/drawableLeftCompat = 0x7f030179
me.rerere.rikkahub.debug:id/progress_view = 0x7f080199
me.rerere.rikkahub.debug:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
me.rerere.rikkahub.debug:attr/popUpTo = 0x7f030367
me.rerere.rikkahub.debug:attr/endIconCheckable = 0x7f030191
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1203ef
me.rerere.rikkahub.debug:attr/motionEasingStandard = 0x7f030324
me.rerere.rikkahub.debug:dimen/m3_card_hovered_z = 0x7f0600fb
me.rerere.rikkahub.debug:attr/dividerInsetStart = 0x7f03016f
me.rerere.rikkahub.debug:attr/cardUseCompatPadding = 0x7f03009c
me.rerere.rikkahub.debug:dimen/material_textinput_min_width = 0x7f060253
me.rerere.rikkahub.debug:dimen/compat_button_padding_vertical_material = 0x7f060059
me.rerere.rikkahub.debug:attr/cardMaxElevation = 0x7f03009a
me.rerere.rikkahub.debug:id/titleDividerNoCustom = 0x7f080210
me.rerere.rikkahub.debug:color/m3_navigation_item_text_color = 0x7f0500ca
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1202da
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_container_color = 0x7f0c014e
me.rerere.rikkahub.debug:attr/materialCardViewElevatedStyle = 0x7f0302da
me.rerere.rikkahub.debug:layout/select_dialog_singlechoice_material = 0x7f0b007a
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral0 = 0x7f050137
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Body2 = 0x7f120018
me.rerere.rikkahub.debug:attr/drawableBottomCompat = 0x7f030177
me.rerere.rikkahub.debug:attr/actionButtonStyle = 0x7f03000c
me.rerere.rikkahub.debug:attr/badgeGravity = 0x7f030050
me.rerere.rikkahub.debug:style/Theme.Design.NoActionBar = 0x7f12022c
me.rerere.rikkahub.debug:attr/colorOnTertiary = 0x7f0300fa
me.rerere.rikkahub.debug:attr/elevationOverlayColor = 0x7f03018d
me.rerere.rikkahub.debug:attr/behavior_overlapTop = 0x7f03006c
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f060206
me.rerere.rikkahub.debug:attr/closeIconTint = 0x7f0300d1
me.rerere.rikkahub.debug:string/m3c_time_picker_dialog_title = 0x7f110131
me.rerere.rikkahub.debug:color/m3_checkbox_button_icon_tint = 0x7f0500a3
me.rerere.rikkahub.debug:dimen/mtrl_card_elevation = 0x7f0602af
me.rerere.rikkahub.debug:id/easeInOut = 0x7f08009e
me.rerere.rikkahub.debug:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
me.rerere.rikkahub.debug:attr/tabIconTintMode = 0x7f0303f3
me.rerere.rikkahub.debug:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f06016e
me.rerere.rikkahub.debug:id/touch_outside = 0x7f080219
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600d8
me.rerere.rikkahub.debug:string/assistant_page_top_p_warning = 0x7f110050
me.rerere.rikkahub.debug:attr/rippleColor = 0x7f030385
me.rerere.rikkahub.debug:attr/yearStyle = 0x7f0304bf
me.rerere.rikkahub.debug:attr/buttonTint = 0x7f030094
me.rerere.rikkahub.debug:dimen/abc_dropdownitem_icon_width = 0x7f060029
me.rerere.rikkahub.debug:attr/layout_constraintDimensionRatio = 0x7f030274
me.rerere.rikkahub.debug:style/Widget.Material3.SearchView.Toolbar = 0x7f1203cf
me.rerere.rikkahub.debug:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502da
me.rerere.rikkahub.debug:attr/appBarLayoutStyle = 0x7f030034
me.rerere.rikkahub.debug:id/masked = 0x7f080128
me.rerere.rikkahub.debug:attr/buttonPanelSideLayout = 0x7f030090
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f12012d
me.rerere.rikkahub.debug:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600c1
me.rerere.rikkahub.debug:attr/actionViewClass = 0x7f030024
me.rerere.rikkahub.debug:drawable/leak_canary_count_background = 0x7f0700ae
me.rerere.rikkahub.debug:color/material_dynamic_neutral80 = 0x7f050260
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501fe
me.rerere.rikkahub.debug:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
me.rerere.rikkahub.debug:attr/colorOnContainer = 0x7f0300ea
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f12038d
me.rerere.rikkahub.debug:attr/alphabeticModifiers = 0x7f03002e
me.rerere.rikkahub.debug:macro/m3_comp_switch_selected_icon_color = 0x7f0c0129
me.rerere.rikkahub.debug:attr/buttonBarStyle = 0x7f030089
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Toolbar = 0x7f1200fa
me.rerere.rikkahub.debug:dimen/m3_card_elevated_hovered_z = 0x7f0600f9
me.rerere.rikkahub.debug:attr/buttonBarPositiveButtonStyle = 0x7f030088
me.rerere.rikkahub.debug:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0147
me.rerere.rikkahub.debug:attr/chipStandaloneStyle = 0x7f0300be
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c004f
me.rerere.rikkahub.debug:attr/chainUseRtl = 0x7f0300a0
me.rerere.rikkahub.debug:dimen/mtrl_slider_tick_min_spacing = 0x7f0602fb
me.rerere.rikkahub.debug:attr/height = 0x7f030204
me.rerere.rikkahub.debug:attr/queryBackground = 0x7f030375
me.rerere.rikkahub.debug:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0136
me.rerere.rikkahub.debug:dimen/ucrop_padding_crop_frame = 0x7f060342
me.rerere.rikkahub.debug:attr/font = 0x7f0301ec
me.rerere.rikkahub.debug:id/mtrl_child_content_container = 0x7f080155
me.rerere.rikkahub.debug:attr/scrimAnimationDuration = 0x7f03038c
me.rerere.rikkahub.debug:attr/percentHeight = 0x7f03035b
me.rerere.rikkahub.debug:id/layout_saturation_bar = 0x7f0800ef
me.rerere.rikkahub.debug:attr/boxCornerRadiusTopStart = 0x7f03007f
me.rerere.rikkahub.debug:style/Base.V23.Theme.AppCompat = 0x7f1200af
me.rerere.rikkahub.debug:layout/abc_action_mode_bar = 0x7f0b0004
me.rerere.rikkahub.debug:color/design_dark_default_color_on_surface = 0x7f050043
me.rerere.rikkahub.debug:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f120379
me.rerere.rikkahub.debug:attr/itemTextAppearanceActive = 0x7f03024b
me.rerere.rikkahub.debug:dimen/m3_card_stroke_width = 0x7f0600fc
me.rerere.rikkahub.debug:color/m3_tabs_ripple_color = 0x7f050231
me.rerere.rikkahub.debug:attr/materialSearchBarStyle = 0x7f0302e7
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f12006d
me.rerere.rikkahub.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070011
me.rerere.rikkahub.debug:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
me.rerere.rikkahub.debug:dimen/abc_list_item_height_material = 0x7f060031
me.rerere.rikkahub.debug:color/m3_textfield_stroke_color = 0x7f05023c
me.rerere.rikkahub.debug:color/m3_dynamic_hint_foreground = 0x7f0500b9
me.rerere.rikkahub.debug:attr/bottomNavigationStyle = 0x7f030075
me.rerere.rikkahub.debug:attr/layout_optimizationLevel = 0x7f03029f
me.rerere.rikkahub.debug:color/abc_tint_default = 0x7f050014
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501f8
me.rerere.rikkahub.debug:attr/onTouchUp = 0x7f030345
me.rerere.rikkahub.debug:attr/actionBarWidgetTheme = 0x7f03000b
me.rerere.rikkahub.debug:attr/maxAcceleration = 0x7f0302f1
me.rerere.rikkahub.debug:dimen/m3_btn_icon_only_default_size = 0x7f0600e4
me.rerere.rikkahub.debug:plurals/leak_canary_group_screen_title = 0x7f0f0001
me.rerere.rikkahub.debug:attr/errorContentDescription = 0x7f03019f
me.rerere.rikkahub.debug:attr/actionMenuTextColor = 0x7f030010
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f12025c
me.rerere.rikkahub.debug:integer/material_motion_duration_short_2 = 0x7f09002d
me.rerere.rikkahub.debug:attr/flow_firstVerticalStyle = 0x7f0301dc
me.rerere.rikkahub.debug:style/Widget.Material3.BottomSheet.Modal = 0x7f120354
me.rerere.rikkahub.debug:dimen/m3_comp_outlined_text_field_outline_width = 0x7f060169
me.rerere.rikkahub.debug:attr/chipMinTouchTargetSize = 0x7f0300ba
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f120151
me.rerere.rikkahub.debug:attr/state_collapsed = 0x7f0303ce
me.rerere.rikkahub.debug:style/Platform.V21.AppCompat = 0x7f120144
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060216
me.rerere.rikkahub.debug:attr/cornerSizeBottomRight = 0x7f030140
me.rerere.rikkahub.debug:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0014
me.rerere.rikkahub.debug:id/META = 0x7f080005
me.rerere.rikkahub.debug:animator/fragment_open_enter = 0x7f020007
me.rerere.rikkahub.debug:attr/isMaterial3Theme = 0x7f030230
me.rerere.rikkahub.debug:color/m3_slider_active_track_color_legacy = 0x7f050187
me.rerere.rikkahub.debug:drawable/ucrop_sharpness = 0x7f070129
me.rerere.rikkahub.debug:attr/itemShapeInsetEnd = 0x7f030244
me.rerere.rikkahub.debug:attr/behavior_autoShrink = 0x7f030066
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f120277
me.rerere.rikkahub.debug:layout/abc_dialog_title_material = 0x7f0b000c
me.rerere.rikkahub.debug:color/m3_sys_color_dark_surface_container_lowest = 0x7f0501ac
me.rerere.rikkahub.debug:attr/alpha = 0x7f03002d
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500e7
me.rerere.rikkahub.debug:attr/helperTextTextColor = 0x7f030208
me.rerere.rikkahub.debug:styleable/Transition = 0x7f130099
me.rerere.rikkahub.debug:anim/leak_canary_exit_alpha = 0x7f010020
me.rerere.rikkahub.debug:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f
me.rerere.rikkahub.debug:attr/boxCollapsedPaddingTop = 0x7f03007b
me.rerere.rikkahub.debug:attr/actionModeWebSearchDrawable = 0x7f03001f
me.rerere.rikkahub.debug:attr/layout_scrollFlags = 0x7f0302a1
me.rerere.rikkahub.debug:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
me.rerere.rikkahub.debug:drawable/abc_btn_default_mtrl_shape = 0x7f070030
me.rerere.rikkahub.debug:color/leak_canary_count_text = 0x7f050071
me.rerere.rikkahub.debug:attr/badgeStyle = 0x7f030055
me.rerere.rikkahub.debug:color/m3_ref_palette_secondary50 = 0x7f05016f
me.rerere.rikkahub.debug:dimen/m3_carousel_extra_small_item_size = 0x7f0600fe
me.rerere.rikkahub.debug:dimen/cardview_default_radius = 0x7f060054
me.rerere.rikkahub.debug:attr/isMaterial3DynamicColorApplied = 0x7f03022f
me.rerere.rikkahub.debug:string/chat_page_cancel = 0x7f110067
me.rerere.rikkahub.debug:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600bd
me.rerere.rikkahub.debug:style/Widget.Material3.Toolbar = 0x7f1203ea
me.rerere.rikkahub.debug:id/text_view_brightness = 0x7f080201
me.rerere.rikkahub.debug:drawable/m3_avd_hide_password = 0x7f0700be
me.rerere.rikkahub.debug:color/m3_fab_efab_foreground_color_selector = 0x7f0500be
me.rerere.rikkahub.debug:attr/badgeShapeAppearance = 0x7f030053
me.rerere.rikkahub.debug:attr/thumbTintMode = 0x7f030452
me.rerere.rikkahub.debug:macro/m3_comp_elevated_card_container_shape = 0x7f0c002b
me.rerere.rikkahub.debug:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f060247
me.rerere.rikkahub.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f12042c
me.rerere.rikkahub.debug:animator/m3_extended_fab_state_list_animator = 0x7f020014
me.rerere.rikkahub.debug:string/setting_display_page_show_token_usage_title = 0x7f1101c2
me.rerere.rikkahub.debug:attr/chipStrokeColor = 0x7f0300c0
me.rerere.rikkahub.debug:attr/barLength = 0x7f030061
me.rerere.rikkahub.debug:attr/errorAccessibilityLabel = 0x7f03019d
me.rerere.rikkahub.debug:attr/expandedTitleMarginTop = 0x7f0301b0
me.rerere.rikkahub.debug:attr/alertDialogButtonGroupStyle = 0x7f030028
me.rerere.rikkahub.debug:attr/attributeName = 0x7f03003a
me.rerere.rikkahub.debug:style/Widget.Material3.SideSheet.Detached = 0x7f1203d1
me.rerere.rikkahub.debug:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0107
me.rerere.rikkahub.debug:attr/imageAspectRatio = 0x7f030220
me.rerere.rikkahub.debug:attr/behavior_expandedOffset = 0x7f030068
me.rerere.rikkahub.debug:attr/windowActionBarOverlay = 0x7f0304b5
me.rerere.rikkahub.debug:animator/m3_chip_state_list_anim = 0x7f02000e
me.rerere.rikkahub.debug:animator/fragment_open_exit = 0x7f020008
me.rerere.rikkahub.debug:attr/insetForeground = 0x7f03022d
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f12014d
me.rerere.rikkahub.debug:attr/colorControlNormal = 0x7f0300e6
me.rerere.rikkahub.debug:id/accessibility_custom_action_31 = 0x7f080029
me.rerere.rikkahub.debug:attr/textAppearancePopupMenuHeader = 0x7f03042e
me.rerere.rikkahub.debug:styleable/ShapeableImageView = 0x7f130083
me.rerere.rikkahub.debug:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602b5
me.rerere.rikkahub.debug:attr/visibilityMode = 0x7f0304ac
me.rerere.rikkahub.debug:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f120274
me.rerere.rikkahub.debug:string/material_minute_selection = 0x7f110148
me.rerere.rikkahub.debug:attr/buttonGravity = 0x7f03008b
me.rerere.rikkahub.debug:drawable/ic_mtrl_checked_circle = 0x7f0700a8
me.rerere.rikkahub.debug:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f120198
me.rerere.rikkahub.debug:attr/isMaterialTheme = 0x7f030231
me.rerere.rikkahub.debug:string/common_open_on_phone = 0x7f110094
me.rerere.rikkahub.debug:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602d3
me.rerere.rikkahub.debug:string/m3c_floating_toolbar_collapse = 0x7f110129
me.rerere.rikkahub.debug:dimen/m3_bottom_sheet_elevation = 0x7f0600d3
me.rerere.rikkahub.debug:id/month_title = 0x7f080149
me.rerere.rikkahub.debug:attr/snackbarButtonStyle = 0x7f0303ba
me.rerere.rikkahub.debug:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f120104
me.rerere.rikkahub.debug:attr/listMenuViewStyle = 0x7f0302b1
me.rerere.rikkahub.debug:dimen/m3_carousel_debug_keyline_width = 0x7f0600fd
me.rerere.rikkahub.debug:id/action_mode_bar_stub = 0x7f08003e
me.rerere.rikkahub.debug:attr/bottomSheetDialogTheme = 0x7f030076
me.rerere.rikkahub.debug:layout/ime_secondary_split_test_activity = 0x7f0b002d
me.rerere.rikkahub.debug:color/mtrl_error = 0x7f0502ff
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f120030
me.rerere.rikkahub.debug:attr/activeIndicatorLabelPadding = 0x7f030025
me.rerere.rikkahub.debug:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0027
me.rerere.rikkahub.debug:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301b8
me.rerere.rikkahub.debug:string/translator_page_cancel = 0x7f1101fd
me.rerere.rikkahub.debug:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0118
me.rerere.rikkahub.debug:dimen/m3_badge_with_text_size = 0x7f0600c9
me.rerere.rikkahub.debug:anim/m3_side_sheet_exit_to_right = 0x7f01002e
me.rerere.rikkahub.debug:color/abc_hint_foreground_material_light = 0x7f050008
me.rerere.rikkahub.debug:style/Widget.AppCompat.ListMenuView = 0x7f12031d
me.rerere.rikkahub.debug:attr/autoSizePresetSizes = 0x7f030040
me.rerere.rikkahub.debug:attr/autoSizeMinTextSize = 0x7f03003f
me.rerere.rikkahub.debug:macro/m3_comp_search_bar_input_text_color = 0x7f0c00e9
me.rerere.rikkahub.debug:drawable/mtrl_ic_arrow_drop_down = 0x7f0700e0
me.rerere.rikkahub.debug:color/m3_sys_color_dark_inverse_surface = 0x7f050194
me.rerere.rikkahub.debug:anim/mtrl_card_lowers_interpolator = 0x7f010031
me.rerere.rikkahub.debug:attr/cardPreventCornerOverlap = 0x7f03009b
me.rerere.rikkahub.debug:id/ghost_view = 0x7f0800c0
me.rerere.rikkahub.debug:attr/backgroundInsetTop = 0x7f03004a
me.rerere.rikkahub.debug:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
me.rerere.rikkahub.debug:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1200bd
me.rerere.rikkahub.debug:attr/boxBackgroundColor = 0x7f030079
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f12039e
me.rerere.rikkahub.debug:dimen/leak_canary_more_size = 0x7f0600a3
me.rerere.rikkahub.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f120156
me.rerere.rikkahub.debug:id/fill = 0x7f0800ae
me.rerere.rikkahub.debug:attr/thumbTint = 0x7f030451
me.rerere.rikkahub.debug:attr/tabIndicatorGravity = 0x7f0303f9
me.rerere.rikkahub.debug:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
me.rerere.rikkahub.debug:attr/drawerLayoutStyle = 0x7f030182
me.rerere.rikkahub.debug:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060270
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0501c5
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Input = 0x7f120370
me.rerere.rikkahub.debug:color/m3_highlighted_text = 0x7f0500c1
me.rerere.rikkahub.debug:layout/mtrl_alert_dialog_actions = 0x7f0b0051
me.rerere.rikkahub.debug:attr/boxStrokeColor = 0x7f030080
me.rerere.rikkahub.debug:anim/mtrl_bottom_sheet_slide_out = 0x7f010030
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c0069
me.rerere.rikkahub.debug:attr/layout_constraintTop_toBottomOf = 0x7f03028b
me.rerere.rikkahub.debug:attr/chipSpacingVertical = 0x7f0300bd
me.rerere.rikkahub.debug:attr/colorPrimaryInverse = 0x7f030105
me.rerere.rikkahub.debug:drawable/leak_canary_toast_background = 0x7f0700bc
me.rerere.rikkahub.debug:drawable/$avd_hide_password__1 = 0x7f070001
me.rerere.rikkahub.debug:dimen/ucrop_width_middle_wheel_progress_line = 0x7f060349
me.rerere.rikkahub.debug:styleable/CollapsingToolbarLayout_Layout = 0x7f130024
me.rerere.rikkahub.debug:id/search_mag_icon = 0x7f0801b8
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_tertiary20 = 0x7f050120
me.rerere.rikkahub.debug:attr/colorOutline = 0x7f0300fe
me.rerere.rikkahub.debug:dimen/m3_btn_text_btn_padding_right = 0x7f0600f1
me.rerere.rikkahub.debug:dimen/mtrl_calendar_day_corner = 0x7f060286
me.rerere.rikkahub.debug:attr/placeholderTextAppearance = 0x7f030362
me.rerere.rikkahub.debug:string/chat_page_no_conversations = 0x7f110076
me.rerere.rikkahub.debug:attr/barrierMargin = 0x7f030064
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.ProgressBar = 0x7f1200ed
me.rerere.rikkahub.debug:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
me.rerere.rikkahub.debug:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602d4
me.rerere.rikkahub.debug:attr/layout_constraintEnd_toEndOf = 0x7f030275
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0166
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary80 = 0x7f05010c
me.rerere.rikkahub.debug:style/Widget.Material3.ChipGroup = 0x7f120376
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1201c4
me.rerere.rikkahub.debug:drawable/common_google_signin_btn_icon_disabled = 0x7f070086
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0153
me.rerere.rikkahub.debug:id/material_clock_period_toggle = 0x7f080131
me.rerere.rikkahub.debug:attr/ucrop_frame_stroke_size = 0x7f03049c
me.rerere.rikkahub.debug:attr/argType = 0x7f030037
me.rerere.rikkahub.debug:string/m3_sys_motion_easing_linear = 0x7f1100fd
me.rerere.rikkahub.debug:attr/actionModeShareDrawable = 0x7f03001b
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500f2
me.rerere.rikkahub.debug:color/m3_ref_palette_neutral_variant95 = 0x7f05015a
me.rerere.rikkahub.debug:animator/m3_appbar_state_list_animator = 0x7f020009
me.rerere.rikkahub.debug:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1203b4
me.rerere.rikkahub.debug:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f120050
me.rerere.rikkahub.debug:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060257
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f120042
me.rerere.rikkahub.debug:attr/imageButtonStyle = 0x7f030222
me.rerere.rikkahub.debug:id/skipCollapsed = 0x7f0801c5
me.rerere.rikkahub.debug:attr/actionBarTabBarStyle = 0x7f030007
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500d5
me.rerere.rikkahub.debug:string/setting_page_dynamic_color_desc = 0x7f1101dc
me.rerere.rikkahub.debug:id/transitionToStart = 0x7f08021b
me.rerere.rikkahub.debug:color/abc_btn_colored_text_material = 0x7f050003
me.rerere.rikkahub.debug:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b7
me.rerere.rikkahub.debug:color/material_dynamic_tertiary60 = 0x7f050292
me.rerere.rikkahub.debug:color/material_dynamic_primary95 = 0x7f05027c
me.rerere.rikkahub.debug:attr/actionModeBackground = 0x7f030011
me.rerere.rikkahub.debug:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f120132
me.rerere.rikkahub.debug:attr/materialDisplayDividerStyle = 0x7f0302e0
me.rerere.rikkahub.debug:attr/buttonIconTint = 0x7f03008e
me.rerere.rikkahub.debug:attr/barrierAllowsGoneWidgets = 0x7f030062
me.rerere.rikkahub.debug:attr/contentPaddingBottom = 0x7f03012d
me.rerere.rikkahub.debug:attr/itemPaddingTop = 0x7f03023e
me.rerere.rikkahub.debug:attr/colorOnSurfaceVariant = 0x7f0300f9
me.rerere.rikkahub.debug:id/group_divider = 0x7f0800c5
me.rerere.rikkahub.debug:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060250
me.rerere.rikkahub.debug:color/ucrop_color_statusbar = 0x7f050352
me.rerere.rikkahub.debug:attr/boxStrokeErrorColor = 0x7f030081
me.rerere.rikkahub.debug:style/Theme.AppCompat.Light = 0x7f12021f
me.rerere.rikkahub.debug:attr/animateMenuItems = 0x7f030030
me.rerere.rikkahub.debug:anim/design_bottom_sheet_slide_out = 0x7f010019
me.rerere.rikkahub.debug:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1202e8
me.rerere.rikkahub.debug:id/image_view_state_scale = 0x7f0800dd
me.rerere.rikkahub.debug:attr/actionModeCloseContentDescription = 0x7f030013
me.rerere.rikkahub.debug:drawable/mtrl_ic_checkbox_checked = 0x7f0700e4
me.rerere.rikkahub.debug:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
me.rerere.rikkahub.debug:color/m3_chip_text_color = 0x7f0500a9
me.rerere.rikkahub.debug:attr/triggerId = 0x7f030490
me.rerere.rikkahub.debug:attr/badgeWithTextHeight = 0x7f03005c
me.rerere.rikkahub.debug:style/ShapeAppearance.MaterialComponents.Badge = 0x7f120181
me.rerere.rikkahub.debug:color/m3_sys_color_light_inverse_primary = 0x7f050205
me.rerere.rikkahub.debug:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f120072
me.rerere.rikkahub.debug:attr/layout_constraintTop_creator = 0x7f03028a
me.rerere.rikkahub.debug:attr/navGraph = 0x7f030332
me.rerere.rikkahub.debug:string/close_sheet = 0x7f110081
me.rerere.rikkahub.debug:color/m3_ref_palette_error0 = 0x7f05012a
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Button = 0x7f12019c
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f12003c
me.rerere.rikkahub.debug:macro/m3_comp_slider_active_track_color = 0x7f0c010b
me.rerere.rikkahub.debug:dimen/mtrl_btn_padding_right = 0x7f060277
me.rerere.rikkahub.debug:id/transition_clip = 0x7f08021c
me.rerere.rikkahub.debug:attr/keylines = 0x7f030258
me.rerere.rikkahub.debug:animator/mtrl_chip_state_list_anim = 0x7f020018
me.rerere.rikkahub.debug:drawable/abc_ic_go_search_api_material = 0x7f070041
me.rerere.rikkahub.debug:attr/trackDecoration = 0x7f030482
me.rerere.rikkahub.debug:color/dim_foreground_disabled_material_light = 0x7f050062
me.rerere.rikkahub.debug:dimen/m3_side_sheet_standard_elevation = 0x7f0601f7
me.rerere.rikkahub.debug:attr/subtitleTextColor = 0x7f0303e4
me.rerere.rikkahub.debug:attr/curveFit = 0x7f03014e
me.rerere.rikkahub.debug:attr/actionModeStyle = 0x7f03001d
me.rerere.rikkahub.debug:attr/layout_constraintCircle = 0x7f030271
me.rerere.rikkahub.debug:style/Widget.AppCompat.ActionBar.TabView = 0x7f1202f1
me.rerere.rikkahub.debug:color/material_dynamic_neutral40 = 0x7f05025c
me.rerere.rikkahub.debug:string/mtrl_picker_range_header_only_start_selected = 0x7f11018d
me.rerere.rikkahub.debug:string/m3c_date_picker_today_description = 0x7f11011b
me.rerere.rikkahub.debug:attr/colorOnTertiaryFixedVariant = 0x7f0300fd
me.rerere.rikkahub.debug:anim/linear_indeterminate_line2_head_interpolator = 0x7f010025
me.rerere.rikkahub.debug:attr/defaultMarginsEnabled = 0x7f03015f
me.rerere.rikkahub.debug:dimen/design_navigation_item_horizontal_padding = 0x7f060078
me.rerere.rikkahub.debug:style/Base.Widget.AppCompat.Spinner = 0x7f1200f6
me.rerere.rikkahub.debug:attr/reverseLayout = 0x7f030384
me.rerere.rikkahub.debug:dimen/m3_chip_elevated_elevation = 0x7f060107
me.rerere.rikkahub.debug:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
me.rerere.rikkahub.debug:string/m3c_time_picker_period_toggle_description = 0x7f11013b
me.rerere.rikkahub.debug:id/contrast_scroll_wheel = 0x7f080079
me.rerere.rikkahub.debug:dimen/design_tab_max_width = 0x7f060089
me.rerere.rikkahub.debug:plurals/leak_canary_heap_analysis_list_screen_title = 0x7f0f0002
me.rerere.rikkahub.debug:attr/strokeColor = 0x7f0303d9
me.rerere.rikkahub.debug:attr/colorOnPrimarySurface = 0x7f0300f2
me.rerere.rikkahub.debug:string/app_name = 0x7f11001c
me.rerere.rikkahub.debug:id/text_input_error_icon = 0x7f0801ff
me.rerere.rikkahub.debug:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
me.rerere.rikkahub.debug:styleable/ImageFilterView = 0x7f13003c
me.rerere.rikkahub.debug:dimen/m3_extended_fab_start_padding = 0x7f0601c3
me.rerere.rikkahub.debug:id/text_view_scale = 0x7f080206
me.rerere.rikkahub.debug:attr/keyboardIcon = 0x7f030257
me.rerere.rikkahub.debug:attr/backgroundSplit = 0x7f03004c
me.rerere.rikkahub.debug:attr/rangeFillColor = 0x7f030379
me.rerere.rikkahub.debug:anim/leak_canary_exit_backward = 0x7f010021
me.rerere.rikkahub.debug:color/common_google_signin_btn_text_dark_focused = 0x7f050033
me.rerere.rikkahub.debug:attr/tabPadding = 0x7f0303ff
me.rerere.rikkahub.debug:layout/mtrl_picker_dialog = 0x7f0b0065
me.rerere.rikkahub.debug:dimen/mtrl_calendar_days_of_week_height = 0x7f06028c
me.rerere.rikkahub.debug:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f120143
me.rerere.rikkahub.debug:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301cb
me.rerere.rikkahub.debug:color/m3_ref_palette_dynamic_primary40 = 0x7f050108
me.rerere.rikkahub.debug:id/performance = 0x7f080190
me.rerere.rikkahub.debug:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600cf
me.rerere.rikkahub.debug:dimen/mtrl_btn_focused_z = 0x7f06026e
me.rerere.rikkahub.debug:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501fc
me.rerere.rikkahub.debug:attr/floatingActionButtonStyle = 0x7f0301d6
me.rerere.rikkahub.debug:attr/colorSecondary = 0x7f030109
me.rerere.rikkahub.debug:style/Widget.Material3.Chip.Input.Icon = 0x7f120372
me.rerere.rikkahub.debug:color/material_personalized_color_primary_text = 0x7f0502c6
me.rerere.rikkahub.debug:color/material_dynamic_neutral_variant60 = 0x7f05026b
me.rerere.rikkahub.debug:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
me.rerere.rikkahub.debug:layout/ucrop_layout_sharpness_wheel = 0x7f0b0085
me.rerere.rikkahub.debug:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600d6
me.rerere.rikkahub.debug:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0155
me.rerere.rikkahub.debug:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
me.rerere.rikkahub.debug:attr/motionDurationLong3 = 0x7f030312
me.rerere.rikkahub.debug:style/TextAppearance.AppCompat.Widget.Button = 0x7f1201bf
me.rerere.rikkahub.debug:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c0098
me.rerere.rikkahub.debug:anim/design_snackbar_out = 0x7f01001b
me.rerere.rikkahub.debug:attr/textAppearanceDisplayMedium = 0x7f03041a
me.rerere.rikkahub.debug:attr/elevationOverlayAccentColor = 0x7f03018c
me.rerere.rikkahub.debug:id/leak_canary_dump_heap_now = 0x7f0800f9
me.rerere.rikkahub.debug:color/material_dynamic_neutral90 = 0x7f050261
me.rerere.rikkahub.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f120387
me.rerere.rikkahub.debug:attr/layout_scrollInterpolator = 0x7f0302a2
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f12003b
me.rerere.rikkahub.debug:string/assistant_page_header_value = 0x7f110032
me.rerere.rikkahub.debug:color/m3_ref_palette_tertiary10 = 0x7f050177
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f06021f
me.rerere.rikkahub.debug:layout/quickie_scanner_activity = 0x7f0b0077
me.rerere.rikkahub.debug:attr/checkMarkTintMode = 0x7f0300a3
me.rerere.rikkahub.debug:attr/navigationIcon = 0x7f030334
me.rerere.rikkahub.debug:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601dc
me.rerere.rikkahub.debug:color/m3_sys_color_dark_tertiary_container = 0x7f0501b0
me.rerere.rikkahub.debug:attr/coordinatorLayoutStyle = 0x7f030136
me.rerere.rikkahub.debug:attr/badgeVerticalPadding = 0x7f030059
me.rerere.rikkahub.debug:anim/abc_popup_enter = 0x7f010003
me.rerere.rikkahub.debug:attr/hideNavigationIcon = 0x7f03020b
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004d
me.rerere.rikkahub.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f06020e
me.rerere.rikkahub.debug:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
me.rerere.rikkahub.debug:id/NO_DEBUG = 0x7f080006
me.rerere.rikkahub.debug:attr/badgeWithTextRadius = 0x7f03005d
me.rerere.rikkahub.debug:attr/colorPrimarySurface = 0x7f030106
me.rerere.rikkahub.debug:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
me.rerere.rikkahub.debug:color/mtrl_tabs_legacy_text_color_selector = 0x7f05031b
me.rerere.rikkahub.debug:string/m3c_date_picker_switch_to_calendar_mode = 0x7f110114
me.rerere.rikkahub.debug:anim/m3_motion_fade_exit = 0x7f01002a
me.rerere.rikkahub.debug:string/assistant_page_thinking_budget = 0x7f110048
me.rerere.rikkahub.debug:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0070
me.rerere.rikkahub.debug:attr/lineHeight = 0x7f0302a8
me.rerere.rikkahub.debug:id/text_view_crop = 0x7f080203
me.rerere.rikkahub.debug:attr/tabIndicatorColor = 0x7f0303f7
me.rerere.rikkahub.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f120035
me.rerere.rikkahub.debug:dimen/m3_comp_slider_inactive_track_height = 0x7f060197
me.rerere.rikkahub.debug:attr/allowStacking = 0x7f03002c
me.rerere.rikkahub.debug:macro/m3_comp_filled_text_field_container_color = 0x7f0c004b
me.rerere.rikkahub.debug:attr/route = 0x7f030388
me.rerere.rikkahub.debug:style/MaterialAlertDialog.Material3 = 0x7f120127
me.rerere.rikkahub.debug:attr/paddingEnd = 0x7f03034a
me.rerere.rikkahub.debug:integer/ucrop_progress_loading_anim_time = 0x7f090046
me.rerere.rikkahub.debug:attr/actionBarTabStyle = 0x7f030008
me.rerere.rikkahub.debug:id/save_non_transition_alpha = 0x7f0801a7
me.rerere.rikkahub.debug:id/multiply = 0x7f080162
me.rerere.rikkahub.debug:attr/fabAlignmentModeEndMargin = 0x7f0301bc
